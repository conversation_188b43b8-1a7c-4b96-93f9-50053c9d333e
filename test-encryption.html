<!DOCTYPE html>
<html>
<head>
    <title>Test RSA Encryption</title>
    <script src="https://unpkg.com/jsencrypt@3.3.2/bin/jsencrypt.min.js"></script>
    <script src="https://unpkg.com/js-base64@3.7.5/base64.min.js"></script>
</head>
<body>
    <h1>Test RSA Encryption</h1>
    <input type="text" id="password" placeholder="Enter password" value="root">
    <button onclick="testEncryption()">Test Encryption</button>
    <div id="result"></div>

    <script>
        function testEncryption() {
            const password = document.getElementById('password').value;
            const pub = '-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOOUEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVKRNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs2wIDAQAB-----END PUBLIC KEY-----';
            
            const encryptor = new JSEncrypt();
            encryptor.setPublicKey(pub);
            
            const base64Password = Base64.encode(password);
            const encryptedPassword = encryptor.encrypt(base64Password);
            
            document.getElementById('result').innerHTML = `
                <p><strong>Original:</strong> ${password}</p>
                <p><strong>Base64:</strong> ${base64Password}</p>
                <p><strong>Encrypted:</strong> ${encryptedPassword}</p>
                <p><strong>Length:</strong> ${encryptedPassword ? encryptedPassword.length : 'null'}</p>
            `;
            
            console.log('Encrypted password:', encryptedPassword);
        }
    </script>
</body>
</html>
