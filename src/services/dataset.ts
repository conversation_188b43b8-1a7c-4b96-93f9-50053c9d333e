import { get, post, put, del } from '@/utils/request';
import api from '@/utils/api';

export interface Dataset {
  id: string;
  name: string;
  description?: string;
  document_count: number;
  chunk_count: number;
  created_time: string;
  updated_time: string;
  status: 'processing' | 'completed' | 'failed';
}

export interface CreateDatasetRequest {
  name: string;
  description?: string;
}

export interface DatasetDocument {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'processing' | 'completed' | 'failed';
  created_time: string;
  chunk_count: number;
}

// Dataset API functions
export const datasetService = {
  // Get all datasets
  list: async (): Promise<{ data: Dataset[] }> => {
    return get(api.dataset_list);
  },

  // Create new dataset
  create: async (data: CreateDatasetRequest): Promise<{ data: Dataset }> => {
    return post(api.dataset, data);
  },

  // Get dataset details
  get: async (id: string): Promise<{ data: Dataset }> => {
    return get(api.dataset_detail(id));
  },

  // Update dataset
  update: async (id: string, data: Partial<Dataset>): Promise<{ data: Dataset }> => {
    return put(api.dataset_rename(id), data);
  },

  // Delete dataset
  delete: async (id: string): Promise<void> => {
    return del(api.dataset_delete(id));
  },

  // Upload document to dataset
  uploadDocument: async (id: string, file: File): Promise<{ data: DatasetDocument }> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return post(api.dataset_upload(id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Get documents in dataset
  getDocuments: async (id: string): Promise<{ data: DatasetDocument[] }> => {
    return get(api.dataset_document_list(id));
  },

  // Delete document from dataset
  deleteDocument: async (datasetId: string, documentId: string): Promise<void> => {
    return del(api.dataset_document_delete(datasetId, documentId));
  },

  // Test retrieval
  testRetrieval: async (id: string, query: string): Promise<{ data: any }> => {
    return post(api.dataset_retrieval_test(id), { query });
  },
};

export default datasetService;
