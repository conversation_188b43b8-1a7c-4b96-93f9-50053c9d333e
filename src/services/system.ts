import { get } from '@/utils/request';
import api from '@/utils/api';

export interface SystemStatus {
  status: string;
  version?: string;
  uptime?: number;
}

export interface SystemVersion {
  version: string;
  build_date?: string;
  commit_hash?: string;
}

// System API functions
export const systemService = {
  // Check system status
  getStatus: async (): Promise<{ data: SystemStatus }> => {
    return get(api.system_status);
  },

  // Get system version
  getVersion: async (): Promise<{ data: SystemVersion }> => {
    return get(api.system_version);
  },

  // Test backend connectivity
  testConnection: async (): Promise<boolean> => {
    try {
      await systemService.getStatus();
      return true;
    } catch (error) {
      console.error('Backend connection test failed:', error);
      return false;
    }
  },
};

export default systemService;
