import request, { post, get } from '@/utils/request';
import api from '@/utils/api';
import { authStorage, UserInfo } from '@/utils/auth';
import { rsaPsw } from '@/utils/encryption';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  code: number;
  data: {
    access_token: string;
    avatar?: string;
    email: string;
    nickname: string;
  };
  message: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  nickname: string;
}

// User API functions
export const userService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    console.log('🚀 Starting login for:', credentials.email);

    // Encrypt password using RSA
    const encryptedPassword = rsaPsw(credentials.password);
    if (!encryptedPassword) {
      console.error('❌ Failed to encrypt password');
      throw new Error('Failed to encrypt password');
    }

    console.log('✅ Password encrypted successfully');

    try {
      console.log('📡 Sending login request to:', api.login);

      // Use axios directly to get access to response headers
      const axiosResponse = await request.post(api.login, {
        email: credentials.email,
        password: encryptedPassword,
      }, {
        headers: { skipToken: true }, // Don't send auth header for login
      });

      const response = axiosResponse.data;
      console.log('📥 Login response:', response);

      // If login successful, store auth data
      if (response.code === 0) {
        console.log('✅ Login successful');
        const { data } = response;
        const userInfo: UserInfo = {
          email: data.email,
          nickname: data.nickname,
          avatar: data.avatar,
        };

        // Get authorization header from response
        const authorization = axiosResponse.headers['authorization'];
        console.log('🔑 Authorization header:', authorization);

        authStorage.setAuthData({
          token: data.access_token,
          authorization: authorization,
          userInfo,
        });

        console.log('💾 Auth data stored');
      } else {
        console.warn('⚠️ Login failed:', response.message);
      }

      return response;
    } catch (error: any) {
      console.error('❌ Login error:', error);
      // Re-throw with proper error structure
      if (error.response?.data) {
        return error.response.data;
      }
      throw error;
    }
  },

  // Register user
  register: async (userData: RegisterRequest): Promise<LoginResponse> => {
    // Encrypt password using RSA
    const encryptedPassword = rsaPsw(userData.password);
    if (!encryptedPassword) {
      throw new Error('Failed to encrypt password');
    }

    return post(api.register, {
      email: userData.email,
      nickname: userData.nickname,
      password: encryptedPassword,
    }, {
      headers: { skipToken: true }, // Don't send auth header for register
    });
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await get(api.logout);
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local auth data
      authStorage.clearAuth();
    }
  },

  // Get current user info
  getUserInfo: async (): Promise<{ data: UserInfo }> => {
    return get(api.user_info);
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return authStorage.isAuthenticated();
  },

  // Get stored user info
  getStoredUserInfo: (): UserInfo | null => {
    return authStorage.getUserInfo();
  },
};

export default userService;
