import { post, get } from '@/utils/request';
import api from '@/utils/api';
import { authStorage, UserInfo } from '@/utils/auth';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  code: number;
  data: {
    access_token: string;
    avatar?: string;
    email: string;
    nickname: string;
  };
  message: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  nickname: string;
}

// User API functions
export const userService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await post(api.login, credentials, {
      headers: { skipToken: true }, // Don't send auth header for login
    });
    
    // If login successful, store auth data
    if (response.code === 0) {
      const { data } = response;
      const userInfo: UserInfo = {
        email: data.email,
        nickname: data.nickname,
        avatar: data.avatar,
      };
      
      authStorage.setAuthData({
        token: data.access_token,
        userInfo,
      });
    }
    
    return response;
  },

  // Register user
  register: async (userData: RegisterRequest): Promise<LoginResponse> => {
    return post(api.register, userData, {
      headers: { skipToken: true }, // Don't send auth header for register
    });
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await get(api.logout);
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local auth data
      authStorage.clearAuth();
    }
  },

  // Get current user info
  getUserInfo: async (): Promise<{ data: UserInfo }> => {
    return get(api.user_info);
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return authStorage.isAuthenticated();
  },

  // Get stored user info
  getStoredUserInfo: (): UserInfo | null => {
    return authStorage.getUserInfo();
  },
};

export default userService;
