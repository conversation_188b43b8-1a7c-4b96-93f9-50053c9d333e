import { Database, Plus, ArrowRight } from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'

// Mock data - replace with real data from API
const mockDatasets = [
  { id: 1, name: 'Product Documentation', documents: 45, status: 'active' },
  { id: 2, name: 'Customer Support', documents: 128, status: 'active' },
  { id: 3, name: 'Technical Manuals', documents: 67, status: 'processing' },
]

export default function DatasetsSection() {
  const navigate = useNavigate()

  return (
    <section>
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-orange-100 rounded-xl">
            <Database className="w-6 h-6 text-orange-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Datasets</h2>
            <p className="text-gray-600">Manage your knowledge repositories</p>
          </div>
        </div>
        <button
          onClick={() => navigate('/datasets')}
          className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Create Dataset
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {mockDatasets.map((dataset) => (
          <div
            key={dataset.id}
            onClick={() => navigate('/datasets')}
            className="card hover:shadow-md transition-shadow cursor-pointer group"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Database className="w-5 h-5 text-orange-600" />
              </div>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                dataset.status === 'active' 
                  ? 'bg-green-100 text-green-700'
                  : 'bg-yellow-100 text-yellow-700'
              }`}>
                {dataset.status}
              </span>
            </div>
            
            <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
              {dataset.name}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {dataset.documents} documents
            </p>
            
            <div className="flex items-center text-sm text-primary-600 font-medium">
              View details
              <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
            </div>
          </div>
        ))}
        
        {/* See All Card */}
        <Link
          to="/datasets"
          className="card border-dashed border-2 border-gray-300 hover:border-primary-400 hover:bg-primary-50 transition-colors flex items-center justify-center min-h-[200px] group"
        >
          <div className="text-center">
            <ArrowRight className="w-8 h-8 text-gray-400 group-hover:text-primary-600 mx-auto mb-2 transition-colors" />
            <span className="text-gray-600 group-hover:text-primary-600 font-medium transition-colors">
              View All Datasets
            </span>
          </div>
        </Link>
      </div>
    </section>
  )
}
