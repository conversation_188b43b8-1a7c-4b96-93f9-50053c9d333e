import { useState } from 'react'
import { MessageSquare, Search, Bot, ArrowRight } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

const tabs = [
  { id: 'chat', name: 'Chat', icon: MessageSquare, href: '/chat' },
  { id: 'search', name: 'Search', icon: Search, href: '/search' },
  { id: 'agents', name: 'Agents', icon: Bot, href: '/agents' },
]

// Mock data
const mockApplications = {
  chat: [
    { id: 1, name: 'Customer Support Bot', messages: 1250, status: 'active' },
    { id: 2, name: 'Product Q&A', messages: 890, status: 'active' },
  ],
  search: [
    { id: 1, name: 'Document Search', queries: 2340, status: 'active' },
    { id: 2, name: 'Knowledge Base Search', queries: 1560, status: 'active' },
  ],
  agents: [
    { id: 1, name: 'Sales Assistant', interactions: 450, status: 'active' },
    { id: 2, name: 'Technical Support', interactions: 320, status: 'draft' },
  ],
}

export default function ApplicationsSection() {
  const [activeTab, setActiveTab] = useState('chat')
  const navigate = useNavigate()
  const currentTab = tabs.find(tab => tab.id === activeTab)!
  const applications = mockApplications[activeTab as keyof typeof mockApplications]

  return (
    <section>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-100 rounded-xl">
            <currentTab.icon className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{currentTab.name}</h2>
            <p className="text-gray-600">Build and manage your AI applications</p>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Tab Navigation */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.name}
                </button>
              )
            })}
          </div>
          
          <button
            onClick={() => navigate(currentTab.href)}
            className="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            View All
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {applications.map((app) => (
          <div
            key={app.id}
            onClick={() => navigate(currentTab.href)}
            className="card hover:shadow-md transition-shadow cursor-pointer group"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <currentTab.icon className="w-5 h-5 text-blue-600" />
              </div>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                app.status === 'active' 
                  ? 'bg-green-100 text-green-700'
                  : 'bg-gray-100 text-gray-700'
              }`}>
                {app.status}
              </span>
            </div>
            
            <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
              {app.name}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {'messages' in app && `${app.messages} messages`}
              {'queries' in app && `${app.queries} queries`}
              {'interactions' in app && `${app.interactions} interactions`}
            </p>
            
            <div className="flex items-center text-sm text-primary-600 font-medium">
              Open application
              <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
            </div>
          </div>
        ))}
        
        {/* Create New Card */}
        <div
          onClick={() => navigate(currentTab.href)}
          className="card border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-colors flex items-center justify-center min-h-[200px] group cursor-pointer"
        >
          <div className="text-center">
            <currentTab.icon className="w-8 h-8 text-gray-400 group-hover:text-blue-600 mx-auto mb-2 transition-colors" />
            <span className="text-gray-600 group-hover:text-blue-600 font-medium transition-colors">
              Create New {currentTab.name}
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
