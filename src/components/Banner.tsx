import { ArrowRight, Sparkles } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export default function Banner() {
  const navigate = useNavigate()

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-blue-800 rounded-2xl p-8 text-white">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
      <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-4 left-4 w-16 h-16 bg-purple-300/20 rounded-full blur-lg"></div>
      
      <div className="relative z-10">
        <div className="flex items-center gap-2 mb-6">
          <div className="p-2 bg-white/20 rounded-lg">
            <Sparkles className="w-5 h-5" />
          </div>
          <span className="text-white/90 text-sm font-medium">AI-Powered Knowledge Platform</span>
        </div>
        
        <h1 className="text-5xl font-bold mb-6 leading-tight">
          Welcome to{' '}
          <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
            AgentQuest
          </span>
        </h1>
        
        <p className="text-xl text-white/90 mb-8 max-w-2xl">
          Transform your documents into intelligent conversations. Build powerful RAG applications with cutting-edge AI technology.
        </p>
        
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => navigate('/datasets')}
            className="flex items-center gap-2 px-6 py-3 bg-white text-primary-600 font-semibold rounded-xl hover:bg-white/90 transition-colors shadow-lg"
          >
            Get Started
            <ArrowRight className="w-4 h-4" />
          </button>
          <button
            onClick={() => window.open('https://github.com/your-repo/agentquest', '_blank')}
            className="px-6 py-3 bg-white/20 text-white font-semibold rounded-xl hover:bg-white/30 transition-colors backdrop-blur-sm border border-white/20"
          >
            Learn More
          </button>
        </div>
      </div>
    </section>
  )
}
