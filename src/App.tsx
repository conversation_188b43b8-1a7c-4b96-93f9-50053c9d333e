import { RouterProvider } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { router } from './router'
import { systemService } from './services/system'

function App() {
  const [backendConnected, setBackendConnected] = useState<boolean | null>(null)

  useEffect(() => {
    // Test backend connection on app start
    const testConnection = async () => {
      try {
        const connected = await systemService.testConnection()
        setBackendConnected(connected)
        if (connected) {
          console.log('✅ Backend connection successful')
        } else {
          console.warn('❌ Backend connection failed')
        }
      } catch (error) {
        console.error('❌ Backend connection test error:', error)
        setBackendConnected(false)
      }
    }

    testConnection()
  }, [])

  // Show connection status in console for debugging
  useEffect(() => {
    if (backendConnected !== null) {
      console.log(`Backend Status: ${backendConnected ? 'Connected' : 'Disconnected'}`)
    }
  }, [backendConnected])

  return <RouterProvider router={router} />
}

export default App
