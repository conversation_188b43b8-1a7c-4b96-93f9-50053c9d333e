import { Base64 } from 'js-base64';
import JSEncrypt from 'jsencrypt';

// RSA public key for password encryption (same as in original ragflow)
const RSA_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOOUEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVKRNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs2wIDAQAB-----E<PERSON> PUBLIC KEY-----`;

/**
 * Encrypt password using RSA encryption
 * This matches the encryption used in the original ragflow system
 * @param password - Plain text password
 * @returns Encrypted password string
 */
export const rsaPsw = (password: string): string | false => {
  try {
    console.log('🔐 Encrypting password:', password);
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(RSA_PUBLIC_KEY);

    // First encode the password in base64, then encrypt it
    const base64Password = Base64.encode(password);
    console.log('📝 Base64 encoded:', base64Password);

    const encryptedPassword = encryptor.encrypt(base64Password);
    console.log('🔒 Encrypted password:', encryptedPassword);
    console.log('📏 Encrypted length:', encryptedPassword ? encryptedPassword.length : 'null');

    return encryptedPassword;
  } catch (error) {
    console.error('❌ Password encryption failed:', error);
    return false;
  }
};

export default {
  rsaPsw,
};
