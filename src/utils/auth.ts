// Authentication utilities
export const AUTH_KEYS = {
  TOKEN: 'token',
  AUTHORIZATION: 'Authorization',
  USER_INFO: 'userInfo',
} as const;

export interface UserInfo {
  email: string;
  nickname: string;
  avatar?: string;
}

export const authStorage = {
  // Get token from localStorage
  getToken: (): string | null => {
    return localStorage.getItem(AUTH_KEYS.TOKEN);
  },

  // Set token in localStorage
  setToken: (token: string): void => {
    localStorage.setItem(AUTH_KEYS.TOKEN, token);
  },

  // Get authorization header value
  getAuthorization: (): string | null => {
    return localStorage.getItem(AUTH_KEYS.AUTHORIZATION);
  },

  // Set authorization header value
  setAuthorization: (auth: string): void => {
    localStorage.setItem(AUTH_KEYS.AUTHORIZATION, auth);
  },

  // Get user info
  getUserInfo: (): UserInfo | null => {
    const userInfo = localStorage.getItem(AUTH_KEYS.USER_INFO);
    return userInfo ? JSON.parse(userInfo) : null;
  },

  // Set user info
  setUserInfo: (userInfo: UserInfo): void => {
    localStorage.setItem(AUTH_KEYS.USER_INFO, JSON.stringify(userInfo));
  },

  // Set all auth data at once
  setAuthData: (data: {
    token?: string;
    authorization?: string;
    userInfo?: UserInfo;
  }): void => {
    if (data.token) {
      authStorage.setToken(data.token);
    }
    if (data.authorization) {
      authStorage.setAuthorization(data.authorization);
    }
    if (data.userInfo) {
      authStorage.setUserInfo(data.userInfo);
    }
  },

  // Clear all auth data
  clearAuth: (): void => {
    localStorage.removeItem(AUTH_KEYS.TOKEN);
    localStorage.removeItem(AUTH_KEYS.AUTHORIZATION);
    localStorage.removeItem(AUTH_KEYS.USER_INFO);
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return !!(authStorage.getToken() || authStorage.getAuthorization());
  },
};

export default authStorage;
