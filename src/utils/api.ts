let api_host = `/v1`;

export { api_host };

export default {
  // user
  login: `${api_host}/user/login`,
  logout: `${api_host}/user/logout`,
  register: `${api_host}/user/register`,
  setting: `${api_host}/user/setting`,
  user_info: `${api_host}/user/info`,
  tenant_info: `${api_host}/user/tenant_info`,
  set_tenant_info: `${api_host}/user/set_tenant_info`,
  login_channels: `${api_host}/user/login/channels`,
  login_channel: (channel: string) => `${api_host}/user/login/${channel}`,

  // dataset
  dataset: `${api_host}/dataset`,
  dataset_list: `${api_host}/dataset/list`,
  dataset_detail: (id: string) => `${api_host}/dataset/${id}`,
  dataset_rename: (id: string) => `${api_host}/dataset/${id}`,
  dataset_delete: (id: string) => `${api_host}/dataset/${id}`,
  dataset_upload: (id: string) => `${api_host}/dataset/${id}/document`,
  dataset_document_list: (id: string) => `${api_host}/dataset/${id}/document/list`,
  dataset_document_delete: (id: string, doc_id: string) => `${api_host}/dataset/${id}/document/${doc_id}`,
  dataset_document_rename: (id: string, doc_id: string) => `${api_host}/dataset/${id}/document/${doc_id}`,
  dataset_document_run: (id: string, doc_id: string) => `${api_host}/dataset/${id}/document/${doc_id}/run`,
  dataset_document_stop: (id: string, doc_id: string) => `${api_host}/dataset/${id}/document/${doc_id}/stop`,
  dataset_document_chunk_list: (id: string, doc_id: string) => `${api_host}/dataset/${id}/document/${doc_id}/chunk`,
  dataset_document_chunk_create: (id: string, doc_id: string) => `${api_host}/dataset/${id}/document/${doc_id}/chunk`,
  dataset_document_chunk_delete: (id: string, doc_id: string, chunk_id: string) => `${api_host}/dataset/${id}/document/${doc_id}/chunk/${chunk_id}`,
  dataset_document_chunk_edit: (id: string, doc_id: string, chunk_id: string) => `${api_host}/dataset/${id}/document/${doc_id}/chunk/${chunk_id}`,
  dataset_retrieval_test: (id: string) => `${api_host}/dataset/${id}/retrieval`,

  // chat
  chat: `${api_host}/chat`,
  chat_list: `${api_host}/chat/list`,
  chat_detail: (id: string) => `${api_host}/chat/${id}`,
  chat_delete: (id: string) => `${api_host}/chat/${id}`,
  chat_rename: (id: string) => `${api_host}/chat/${id}`,
  chat_conversation_list: (id: string) => `${api_host}/chat/${id}/conversation`,
  chat_conversation_create: (id: string) => `${api_host}/chat/${id}/conversation`,
  chat_conversation_delete: (id: string, conversation_id: string) => `${api_host}/chat/${id}/conversation/${conversation_id}`,
  chat_conversation_completion: (id: string, conversation_id: string) => `${api_host}/chat/${id}/conversation/${conversation_id}/completion`,

  // file
  file_upload: `${api_host}/file/upload`,
  file_list: `${api_host}/file/list`,
  file_delete: (id: string) => `${api_host}/file/${id}`,
  file_rename: (id: string) => `${api_host}/file/${id}`,

  // system
  system_status: `${api_host}/system/status`,
  system_version: `${api_host}/system/version`,
};
