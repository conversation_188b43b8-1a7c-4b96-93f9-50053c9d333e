import axios, { AxiosResponse, AxiosError } from 'axios';
import { authStorage } from './auth';

// Create axios instance
const request = axios.create({
  timeout: 300000, // 5 minutes
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
request.interceptors.request.use(
  (config) => {
    // Add authorization token if available
    const authorization = authStorage.getAuthorization();
    const token = authStorage.getToken();

    if (!config.headers.skipToken) {
      if (authorization) {
        config.headers.Authorization = authorization;
      } else if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
request.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - clear auth data and redirect to login
      authStorage.clearAuth();
      // You can add navigation logic here if needed
      console.warn('Unauthorized access - auth data cleared');
    } else if (error.response?.status === 403) {
      console.warn('Forbidden access');
    } else if (error.response?.status && error.response.status >= 500) {
      console.error('Server error:', error.response.status);
    } else if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      console.error('Network error - check if backend is running');
    }
    
    return Promise.reject(error);
  }
);

export default request;

// Helper function for API calls
export const apiCall = async <T = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: any,
  config?: any
): Promise<T> => {
  try {
    const response = await request({
      method,
      url,
      data,
      ...config,
    });
    return response.data;
  } catch (error) {
    console.error(`API call failed: ${method} ${url}`, error);
    throw error;
  }
};

// Convenience methods
export const get = <T = any>(url: string, config?: any): Promise<T> => 
  apiCall<T>('GET', url, undefined, config);

export const post = <T = any>(url: string, data?: any, config?: any): Promise<T> => 
  apiCall<T>('POST', url, data, config);

export const put = <T = any>(url: string, data?: any, config?: any): Promise<T> => 
  apiCall<T>('PUT', url, data, config);

export const del = <T = any>(url: string, config?: any): Promise<T> => 
  apiCall<T>('DELETE', url, undefined, config);
