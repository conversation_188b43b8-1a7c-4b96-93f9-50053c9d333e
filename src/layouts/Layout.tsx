import { useEffect } from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import Header from '../components/Header'
import { userService } from '@/services/user'

export default function Layout() {
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user is authenticated
    if (!userService.isAuthenticated()) {
      navigate('/login')
    }
  }, [navigate])

  // Don't render layout if not authenticated
  if (!userService.isAuthenticated()) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <Outlet />
      </main>
    </div>
  )
}
