import { Files as FilesIcon, Upload } from 'lucide-react'

export default function Files() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Files</h1>
          <p className="text-gray-600 mt-1">Manage your documents and files</p>
        </div>
        <button
          onClick={() => alert('Upload Files - This will open the file upload dialog')}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Upload className="w-4 h-4" />
          Upload Files
        </button>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <FilesIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No files yet</h3>
        <p className="text-gray-600 mb-6">Upload your first files to start building your knowledge base</p>
        <button
          onClick={() => alert('Upload Your First Files - This will open the file upload wizard')}
          className="btn-primary"
        >
          Upload Your First Files
        </button>
      </div>
    </div>
  )
}
