import { useState } from 'react'
import { MessageSquare, Plus, Send, Bot, User, X } from 'lucide-react'

const mockChats = [
  { id: 1, name: 'Customer Support Bot', messages: 1250, status: 'active', lastUsed: '2 hours ago' },
  { id: 2, name: 'Product Q&A', messages: 890, status: 'active', lastUsed: '1 day ago' },
  { id: 3, name: 'Technical Support', messages: 456, status: 'draft', lastUsed: '3 days ago' },
]

const mockMessages = [
  { id: 1, type: 'user', content: 'Hello! Can you help me with product information?' },
  { id: 2, type: 'bot', content: 'Hello! I\'d be happy to help you with product information. What specific product are you interested in?' },
  { id: 3, type: 'user', content: 'I\'m looking for information about your pricing plans.' },
  { id: 4, type: 'bot', content: 'We offer several pricing plans to fit different needs. Our basic plan starts at $29/month and includes core features like document processing and basic chat functionality. Would you like me to explain the different tiers?' },
]

export default function Chat() {
  const [chats, setChats] = useState(mockChats)
  const [selectedChat, setSelectedChat] = useState<number | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newChatName, setNewChatName] = useState('')
  const [messages, setMessages] = useState(mockMessages)
  const [newMessage, setNewMessage] = useState('')

  const handleCreateChat = () => {
    if (newChatName.trim()) {
      const newChat = {
        id: chats.length + 1,
        name: newChatName,
        messages: 0,
        status: 'active' as const,
        lastUsed: 'Just now'
      }
      setChats([...chats, newChat])
      setNewChatName('')
      setShowCreateForm(false)
    }
  }

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        type: 'user' as const,
        content: newMessage
      }
      const botResponse = {
        id: messages.length + 2,
        type: 'bot' as const,
        content: 'Thank you for your message! This is a demo response. In a real implementation, this would be powered by your AI model and knowledge base.'
      }
      setMessages([...messages, userMessage, botResponse])
      setNewMessage('')
    }
  }

  const selectedChatData = chats.find(chat => chat.id === selectedChat)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Chat</h1>
          <p className="text-gray-600 mt-1">Create and manage chat applications</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Create Chat
        </button>
      </div>

      {/* Create Chat Form */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Create New Chat</h3>
              <button onClick={() => setShowCreateForm(false)}>
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Chat Application Name</label>
                <input
                  type="text"
                  value={newChatName}
                  onChange={(e) => setNewChatName(e.target.value)}
                  placeholder="Enter chat name..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleCreateChat}
                  className="flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Create Chat
                </button>
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedChat ? (
        // Chat Interface
        <div className="bg-white rounded-lg border border-gray-200 h-96 flex flex-col">
          <div className="p-4 border-b border-gray-200 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bot className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-semibold">{selectedChatData?.name}</h3>
                <p className="text-sm text-gray-500">{selectedChatData?.messages} messages</p>
              </div>
            </div>
            <button
              onClick={() => setSelectedChat(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="flex-1 p-4 overflow-y-auto space-y-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`flex items-start gap-2 max-w-xs ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    message.type === 'user' ? 'bg-primary-100' : 'bg-gray-100'
                  }`}>
                    {message.type === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                  </div>
                  <div className={`p-3 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    {message.content}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="p-4 border-t border-gray-200">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <button
                onClick={handleSendMessage}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      ) : chats.length > 0 ? (
        // Chat List
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {chats.map((chat) => (
            <div
              key={chat.id}
              onClick={() => setSelectedChat(chat.id)}
              className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MessageSquare className="w-5 h-5 text-blue-600" />
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  chat.status === 'active'
                    ? 'bg-green-100 text-green-700'
                    : 'bg-gray-100 text-gray-700'
                }`}>
                  {chat.status}
                </span>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2">{chat.name}</h3>
              <p className="text-sm text-gray-600 mb-2">{chat.messages} messages</p>
              <p className="text-xs text-gray-500">Last used: {chat.lastUsed}</p>

              <button className="mt-4 w-full text-sm bg-primary-50 text-primary-600 py-2 rounded-lg hover:bg-primary-100 transition-colors">
                Open Chat
              </button>
            </div>
          ))}
        </div>
      ) : (
        // Empty State
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No chat applications yet</h3>
          <p className="text-gray-600 mb-6">Create your first chat application to start conversations with your data</p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn-primary"
          >
            Create Your First Chat
          </button>
        </div>
      )}
    </div>
  )
}
