import { useState } from 'react'
import { Database, Plus, Search, Filter, X, Upload, FileText } from 'lucide-react'

// Mock datasets for demonstration
const mockDatasets = [
  { id: 1, name: 'Product Documentation', documents: 45, status: 'active', created: '2024-01-15' },
  { id: 2, name: 'Customer Support', documents: 128, status: 'active', created: '2024-01-10' },
  { id: 3, name: 'Technical Manuals', documents: 67, status: 'processing', created: '2024-01-08' },
  { id: 4, name: 'Marketing Content', documents: 23, status: 'active', created: '2024-01-05' },
]

export default function Datasets() {
  const [datasets, setDatasets] = useState(mockDatasets)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showFilter, setShowFilter] = useState(false)
  const [newDatasetName, setNewDatasetName] = useState('')

  const filteredDatasets = datasets.filter(dataset =>
    dataset.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateDataset = () => {
    if (newDatasetName.trim()) {
      const newDataset = {
        id: datasets.length + 1,
        name: newDatasetName,
        documents: 0,
        status: 'active' as const,
        created: new Date().toISOString().split('T')[0]
      }
      setDatasets([...datasets, newDataset])
      setNewDatasetName('')
      setShowCreateForm(false)
    }
  }

  const handleDeleteDataset = (id: number) => {
    setDatasets(datasets.filter(d => d.id !== id))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Datasets</h1>
          <p className="text-gray-600 mt-1">Manage your knowledge repositories</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Create Dataset
        </button>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search datasets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        <button
          onClick={() => setShowFilter(!showFilter)}
          className={`flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors ${
            showFilter ? 'bg-primary-50 border-primary-300' : 'border-gray-300 hover:bg-gray-50'
          }`}
        >
          <Filter className="w-4 h-4" />
          Filter
        </button>
      </div>

      {showFilter && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium mb-3">Filter Options</h4>
          <div className="flex gap-4">
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" />
              Active datasets
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-2" />
              Processing datasets
            </label>
          </div>
        </div>
      )}

      {/* Create Dataset Form */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Create New Dataset</h3>
              <button onClick={() => setShowCreateForm(false)}>
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Dataset Name</label>
                <input
                  type="text"
                  value={newDatasetName}
                  onChange={(e) => setNewDatasetName(e.target.value)}
                  placeholder="Enter dataset name..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleCreateDataset}
                  className="flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Create Dataset
                </button>
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Datasets Grid */}
      {filteredDatasets.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDatasets.map((dataset) => (
            <div key={dataset.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Database className="w-5 h-5 text-orange-600" />
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    dataset.status === 'active'
                      ? 'bg-green-100 text-green-700'
                      : 'bg-yellow-100 text-yellow-700'
                  }`}>
                    {dataset.status}
                  </span>
                  <button
                    onClick={() => handleDeleteDataset(dataset.id)}
                    className="text-gray-400 hover:text-red-500 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2">{dataset.name}</h3>
              <p className="text-sm text-gray-600 mb-2">{dataset.documents} documents</p>
              <p className="text-xs text-gray-500">Created: {dataset.created}</p>

              <div className="mt-4 flex gap-2">
                <button className="flex-1 text-sm bg-primary-50 text-primary-600 py-2 rounded-lg hover:bg-primary-100 transition-colors">
                  <FileText className="w-4 h-4 inline mr-1" />
                  View
                </button>
                <button className="flex-1 text-sm bg-gray-50 text-gray-600 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                  <Upload className="w-4 h-4 inline mr-1" />
                  Upload
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {searchTerm ? 'No datasets found' : 'No datasets yet'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm
              ? `No datasets match "${searchTerm}"`
              : 'Create your first dataset to get started with AgentQuest'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={() => setShowCreateForm(true)}
              className="btn-primary"
            >
              Create Your First Dataset
            </button>
          )}
        </div>
      )}
    </div>
  )
}
