import { Bot, Plus } from 'lucide-react'

export default function Agents() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Agents</h1>
          <p className="text-gray-600 mt-1">Create and manage AI agents</p>
        </div>
        <button
          onClick={() => alert('Create Agent - This will open the AI agent creation form')}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Create Agent
        </button>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <Bot className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No agents yet</h3>
        <p className="text-gray-600 mb-6">Create your first AI agent to automate tasks and workflows</p>
        <button
          onClick={() => alert('Create Your First Agent - This will open the agent creation wizard')}
          className="btn-primary"
        >
          Create Your First Agent
        </button>
      </div>
    </div>
  )
}
