import { Search as SearchIcon, Plus } from 'lucide-react'

export default function Search() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Search</h1>
          <p className="text-gray-600 mt-1">Create and manage search applications</p>
        </div>
        <button
          onClick={() => alert('Create Search - This will open the search application creation form')}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Create Search
        </button>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <SearchIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No search applications yet</h3>
        <p className="text-gray-600 mb-6">Create your first search application to enable intelligent document search</p>
        <button
          onClick={() => alert('Create Your First Search - This will open the search creation wizard')}
          className="btn-primary"
        >
          Create Your First Search
        </button>
      </div>
    </div>
  )
}
