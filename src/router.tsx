import { createBrowserRouter } from 'react-router-dom'
import Layout from './layouts/Layout'
import Home from './pages/Home'
import Datasets from './pages/Datasets'
import Chat from './pages/Chat'
import Search from './pages/Search'
import Agents from './pages/Agents'
import Files from './pages/Files'
import Login from './pages/Login'

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: 'datasets',
        element: <Datasets />,
      },
      {
        path: 'chat',
        element: <Chat />,
      },
      {
        path: 'search',
        element: <Search />,
      },
      {
        path: 'agents',
        element: <Agents />,
      },
      {
        path: 'files',
        element: <Files />,
      },
    ],
  },
])

export const routes = {
  home: '/',
  datasets: '/datasets',
  chat: '/chat',
  search: '/search',
  agents: '/agents',
  files: '/files',
  login: '/login',
} as const
