function qd(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const o=Object.getOwnPropertyDescriptor(r,i);o&&Object.defineProperty(e,i,o.get?o:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Qd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Gd={exports:{}},Zs={},Jd={exports:{}},X={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ao=Symbol.for("react.element"),L0=Symbol.for("react.portal"),M0=Symbol.for("react.fragment"),I0=Symbol.for("react.strict_mode"),F0=Symbol.for("react.profiler"),B0=Symbol.for("react.provider"),U0=Symbol.for("react.context"),z0=Symbol.for("react.forward_ref"),V0=Symbol.for("react.suspense"),$0=Symbol.for("react.memo"),H0=Symbol.for("react.lazy"),Mc=Symbol.iterator;function K0(e){return e===null||typeof e!="object"?null:(e=Mc&&e[Mc]||e["@@iterator"],typeof e=="function"?e:null)}var Yd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Zd=Object.assign,Xd={};function Zr(e,t,n){this.props=e,this.context=t,this.refs=Xd,this.updater=n||Yd}Zr.prototype.isReactComponent={};Zr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function eh(){}eh.prototype=Zr.prototype;function yu(e,t,n){this.props=e,this.context=t,this.refs=Xd,this.updater=n||Yd}var vu=yu.prototype=new eh;vu.constructor=yu;Zd(vu,Zr.prototype);vu.isPureReactComponent=!0;var Ic=Array.isArray,th=Object.prototype.hasOwnProperty,xu={current:null},nh={key:!0,ref:!0,__self:!0,__source:!0};function rh(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)th.call(t,r)&&!nh.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:ao,type:e,key:o,ref:s,props:i,_owner:xu.current}}function W0(e,t){return{$$typeof:ao,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function wu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ao}function q0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Fc=/\/+/g;function _l(e,t){return typeof e=="object"&&e!==null&&e.key!=null?q0(""+e.key):t.toString(36)}function Xo(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ao:case L0:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+_l(s,0):r,Ic(i)?(n="",e!=null&&(n=e.replace(Fc,"$&/")+"/"),Xo(i,t,n,"",function(u){return u})):i!=null&&(wu(i)&&(i=W0(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Fc,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Ic(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+_l(o,l);s+=Xo(o,t,n,a,i)}else if(a=K0(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+_l(o,l++),s+=Xo(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Do(e,t,n){if(e==null)return e;var r=[],i=0;return Xo(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Q0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var et={current:null},es={transition:null},G0={ReactCurrentDispatcher:et,ReactCurrentBatchConfig:es,ReactCurrentOwner:xu};function ih(){throw Error("act(...) is not supported in production builds of React.")}X.Children={map:Do,forEach:function(e,t,n){Do(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Do(e,function(){t++}),t},toArray:function(e){return Do(e,function(t){return t})||[]},only:function(e){if(!wu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};X.Component=Zr;X.Fragment=M0;X.Profiler=F0;X.PureComponent=yu;X.StrictMode=I0;X.Suspense=V0;X.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=G0;X.act=ih;X.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Zd({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=xu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)th.call(t,a)&&!nh.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:ao,type:e.type,key:i,ref:o,props:r,_owner:s}};X.createContext=function(e){return e={$$typeof:U0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:B0,_context:e},e.Consumer=e};X.createElement=rh;X.createFactory=function(e){var t=rh.bind(null,e);return t.type=e,t};X.createRef=function(){return{current:null}};X.forwardRef=function(e){return{$$typeof:z0,render:e}};X.isValidElement=wu;X.lazy=function(e){return{$$typeof:H0,_payload:{_status:-1,_result:e},_init:Q0}};X.memo=function(e,t){return{$$typeof:$0,type:e,compare:t===void 0?null:t}};X.startTransition=function(e){var t=es.transition;es.transition={};try{e()}finally{es.transition=t}};X.unstable_act=ih;X.useCallback=function(e,t){return et.current.useCallback(e,t)};X.useContext=function(e){return et.current.useContext(e)};X.useDebugValue=function(){};X.useDeferredValue=function(e){return et.current.useDeferredValue(e)};X.useEffect=function(e,t){return et.current.useEffect(e,t)};X.useId=function(){return et.current.useId()};X.useImperativeHandle=function(e,t,n){return et.current.useImperativeHandle(e,t,n)};X.useInsertionEffect=function(e,t){return et.current.useInsertionEffect(e,t)};X.useLayoutEffect=function(e,t){return et.current.useLayoutEffect(e,t)};X.useMemo=function(e,t){return et.current.useMemo(e,t)};X.useReducer=function(e,t,n){return et.current.useReducer(e,t,n)};X.useRef=function(e){return et.current.useRef(e)};X.useState=function(e){return et.current.useState(e)};X.useSyncExternalStore=function(e,t,n){return et.current.useSyncExternalStore(e,t,n)};X.useTransition=function(){return et.current.useTransition()};X.version="18.3.1";Jd.exports=X;var D=Jd.exports;const oh=Qd(D),J0=qd({__proto__:null,default:oh},[D]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Y0=D,Z0=Symbol.for("react.element"),X0=Symbol.for("react.fragment"),eg=Object.prototype.hasOwnProperty,tg=Y0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ng={key:!0,ref:!0,__self:!0,__source:!0};function sh(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)eg.call(t,r)&&!ng.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Z0,type:e,key:o,ref:s,props:i,_owner:tg.current}}Zs.Fragment=X0;Zs.jsx=sh;Zs.jsxs=sh;Gd.exports=Zs;var d=Gd.exports,fa={},lh={exports:{}},gt={},ah={exports:{}},uh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,H){var W=O.length;O.push(H);e:for(;0<W;){var ae=W-1>>>1,ue=O[ae];if(0<i(ue,H))O[ae]=H,O[W]=ue,W=ae;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var H=O[0],W=O.pop();if(W!==H){O[0]=W;e:for(var ae=0,ue=O.length,Dt=ue>>>1;ae<Dt;){var ct=2*(ae+1)-1,qe=O[ct],Qe=ct+1,vt=O[Qe];if(0>i(qe,W))Qe<ue&&0>i(vt,qe)?(O[ae]=vt,O[Qe]=W,ae=Qe):(O[ae]=qe,O[ct]=W,ae=ct);else if(Qe<ue&&0>i(vt,W))O[ae]=vt,O[Qe]=W,ae=Qe;else break e}}return H}function i(O,H){var W=O.sortIndex-H.sortIndex;return W!==0?W:O.id-H.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,h=3,y=!1,m=!1,v=!1,N=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(O){for(var H=n(u);H!==null;){if(H.callback===null)r(u);else if(H.startTime<=O)r(u),H.sortIndex=H.expirationTime,t(a,H);else break;H=n(u)}}function R(O){if(v=!1,x(O),!m)if(n(a)!==null)m=!0,un(b);else{var H=n(u);H!==null&&cn(R,H.startTime-O)}}function b(O,H){m=!1,v&&(v=!1,g(_),_=-1),y=!0;var W=h;try{for(x(H),f=n(a);f!==null&&(!(f.expirationTime>H)||O&&!J());){var ae=f.callback;if(typeof ae=="function"){f.callback=null,h=f.priorityLevel;var ue=ae(f.expirationTime<=H);H=e.unstable_now(),typeof ue=="function"?f.callback=ue:f===n(a)&&r(a),x(H)}else r(a);f=n(a)}if(f!==null)var Dt=!0;else{var ct=n(u);ct!==null&&cn(R,ct.startTime-H),Dt=!1}return Dt}finally{f=null,h=W,y=!1}}var T=!1,S=null,_=-1,B=5,L=-1;function J(){return!(e.unstable_now()-L<B)}function le(){if(S!==null){var O=e.unstable_now();L=O;var H=!0;try{H=S(!0,O)}finally{H?pe():(T=!1,S=null)}}else T=!1}var pe;if(typeof p=="function")pe=function(){p(le)};else if(typeof MessageChannel<"u"){var Me=new MessageChannel,Rt=Me.port2;Me.port1.onmessage=le,pe=function(){Rt.postMessage(null)}}else pe=function(){N(le,0)};function un(O){S=O,T||(T=!0,pe())}function cn(O,H){_=N(function(){O(e.unstable_now())},H)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){m||y||(m=!0,un(b))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):B=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(O){switch(h){case 1:case 2:case 3:var H=3;break;default:H=h}var W=h;h=H;try{return O()}finally{h=W}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,H){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var W=h;h=O;try{return H()}finally{h=W}},e.unstable_scheduleCallback=function(O,H,W){var ae=e.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?ae+W:ae):W=ae,O){case 1:var ue=-1;break;case 2:ue=250;break;case 5:ue=**********;break;case 4:ue=1e4;break;default:ue=5e3}return ue=W+ue,O={id:c++,callback:H,priorityLevel:O,startTime:W,expirationTime:ue,sortIndex:-1},W>ae?(O.sortIndex=W,t(u,O),n(a)===null&&O===n(u)&&(v?(g(_),_=-1):v=!0,cn(R,W-ae))):(O.sortIndex=ue,t(a,O),m||y||(m=!0,un(b))),O},e.unstable_shouldYield=J,e.unstable_wrapCallback=function(O){var H=h;return function(){var W=h;h=H;try{return O.apply(this,arguments)}finally{h=W}}}})(uh);ah.exports=uh;var rg=ah.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ig=D,mt=rg;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ch=new Set,Bi={};function cr(e,t){$r(e,t),$r(e+"Capture",t)}function $r(e,t){for(Bi[e]=t,e=0;e<t.length;e++)ch.add(t[e])}var rn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),da=Object.prototype.hasOwnProperty,og=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Bc={},Uc={};function sg(e){return da.call(Uc,e)?!0:da.call(Bc,e)?!1:og.test(e)?Uc[e]=!0:(Bc[e]=!0,!1)}function lg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ag(e,t,n,r){if(t===null||typeof t>"u"||lg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function tt(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ze={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ze[e]=new tt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ze[t]=new tt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ze[e]=new tt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ze[e]=new tt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ze[e]=new tt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ze[e]=new tt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ze[e]=new tt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ze[e]=new tt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ze[e]=new tt(e,5,!1,e.toLowerCase(),null,!1,!1)});var Su=/[\-:]([a-z])/g;function Eu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Su,Eu);ze[t]=new tt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Su,Eu);ze[t]=new tt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Su,Eu);ze[t]=new tt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ze[e]=new tt(e,1,!1,e.toLowerCase(),null,!1,!1)});ze.xlinkHref=new tt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ze[e]=new tt(e,1,!1,e.toLowerCase(),null,!0,!0)});function Nu(e,t,n,r){var i=ze.hasOwnProperty(t)?ze[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ag(t,n,i,r)&&(n=null),r||i===null?sg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var an=ig.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_o=Symbol.for("react.element"),Nr=Symbol.for("react.portal"),kr=Symbol.for("react.fragment"),ku=Symbol.for("react.strict_mode"),ha=Symbol.for("react.profiler"),fh=Symbol.for("react.provider"),dh=Symbol.for("react.context"),Tu=Symbol.for("react.forward_ref"),pa=Symbol.for("react.suspense"),ma=Symbol.for("react.suspense_list"),Cu=Symbol.for("react.memo"),mn=Symbol.for("react.lazy"),hh=Symbol.for("react.offscreen"),zc=Symbol.iterator;function ci(e){return e===null||typeof e!="object"?null:(e=zc&&e[zc]||e["@@iterator"],typeof e=="function"?e:null)}var xe=Object.assign,jl;function Ei(e){if(jl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);jl=t&&t[1]||""}return`
`+jl+e}var bl=!1;function Pl(e,t){if(!e||bl)return"";bl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{bl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ei(e):""}function ug(e){switch(e.tag){case 5:return Ei(e.type);case 16:return Ei("Lazy");case 13:return Ei("Suspense");case 19:return Ei("SuspenseList");case 0:case 2:case 15:return e=Pl(e.type,!1),e;case 11:return e=Pl(e.type.render,!1),e;case 1:return e=Pl(e.type,!0),e;default:return""}}function ga(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case kr:return"Fragment";case Nr:return"Portal";case ha:return"Profiler";case ku:return"StrictMode";case pa:return"Suspense";case ma:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case dh:return(e.displayName||"Context")+".Consumer";case fh:return(e._context.displayName||"Context")+".Provider";case Tu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Cu:return t=e.displayName||null,t!==null?t:ga(e.type)||"Memo";case mn:t=e._payload,e=e._init;try{return ga(e(t))}catch{}}return null}function cg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ga(t);case 8:return t===ku?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function An(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ph(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function fg(e){var t=ph(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function jo(e){e._valueTracker||(e._valueTracker=fg(e))}function mh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ph(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function vs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ya(e,t){var n=t.checked;return xe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Vc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=An(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function gh(e,t){t=t.checked,t!=null&&Nu(e,"checked",t,!1)}function va(e,t){gh(e,t);var n=An(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?xa(e,t.type,n):t.hasOwnProperty("defaultValue")&&xa(e,t.type,An(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function $c(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function xa(e,t,n){(t!=="number"||vs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ni=Array.isArray;function Lr(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+An(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function wa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return xe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Hc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(A(92));if(Ni(n)){if(1<n.length)throw Error(A(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:An(n)}}function yh(e,t){var n=An(t.value),r=An(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Kc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function vh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Sa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?vh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var bo,xh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(bo=bo||document.createElement("div"),bo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=bo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ui(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var _i={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},dg=["Webkit","ms","Moz","O"];Object.keys(_i).forEach(function(e){dg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),_i[t]=_i[e]})});function wh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||_i.hasOwnProperty(e)&&_i[e]?(""+t).trim():t+"px"}function Sh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=wh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var hg=xe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ea(e,t){if(t){if(hg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function Na(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ka=null;function Ru(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ta=null,Mr=null,Ir=null;function Wc(e){if(e=fo(e)){if(typeof Ta!="function")throw Error(A(280));var t=e.stateNode;t&&(t=rl(t),Ta(e.stateNode,e.type,t))}}function Eh(e){Mr?Ir?Ir.push(e):Ir=[e]:Mr=e}function Nh(){if(Mr){var e=Mr,t=Ir;if(Ir=Mr=null,Wc(e),t)for(e=0;e<t.length;e++)Wc(t[e])}}function kh(e,t){return e(t)}function Th(){}var Al=!1;function Ch(e,t,n){if(Al)return e(t,n);Al=!0;try{return kh(e,t,n)}finally{Al=!1,(Mr!==null||Ir!==null)&&(Th(),Nh())}}function zi(e,t){var n=e.stateNode;if(n===null)return null;var r=rl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(A(231,t,typeof n));return n}var Ca=!1;if(rn)try{var fi={};Object.defineProperty(fi,"passive",{get:function(){Ca=!0}}),window.addEventListener("test",fi,fi),window.removeEventListener("test",fi,fi)}catch{Ca=!1}function pg(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var ji=!1,xs=null,ws=!1,Ra=null,mg={onError:function(e){ji=!0,xs=e}};function gg(e,t,n,r,i,o,s,l,a){ji=!1,xs=null,pg.apply(mg,arguments)}function yg(e,t,n,r,i,o,s,l,a){if(gg.apply(this,arguments),ji){if(ji){var u=xs;ji=!1,xs=null}else throw Error(A(198));ws||(ws=!0,Ra=u)}}function fr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Rh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function qc(e){if(fr(e)!==e)throw Error(A(188))}function vg(e){var t=e.alternate;if(!t){if(t=fr(e),t===null)throw Error(A(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return qc(i),e;if(o===r)return qc(i),t;o=o.sibling}throw Error(A(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(A(189))}}if(n.alternate!==r)throw Error(A(190))}if(n.tag!==3)throw Error(A(188));return n.stateNode.current===n?e:t}function Dh(e){return e=vg(e),e!==null?_h(e):null}function _h(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=_h(e);if(t!==null)return t;e=e.sibling}return null}var jh=mt.unstable_scheduleCallback,Qc=mt.unstable_cancelCallback,xg=mt.unstable_shouldYield,wg=mt.unstable_requestPaint,Te=mt.unstable_now,Sg=mt.unstable_getCurrentPriorityLevel,Du=mt.unstable_ImmediatePriority,bh=mt.unstable_UserBlockingPriority,Ss=mt.unstable_NormalPriority,Eg=mt.unstable_LowPriority,Ph=mt.unstable_IdlePriority,Xs=null,Ht=null;function Ng(e){if(Ht&&typeof Ht.onCommitFiberRoot=="function")try{Ht.onCommitFiberRoot(Xs,e,void 0,(e.current.flags&128)===128)}catch{}}var Lt=Math.clz32?Math.clz32:Cg,kg=Math.log,Tg=Math.LN2;function Cg(e){return e>>>=0,e===0?32:31-(kg(e)/Tg|0)|0}var Po=64,Ao=4194304;function ki(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Es(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=ki(l):(o&=s,o!==0&&(r=ki(o)))}else s=n&~i,s!==0?r=ki(s):o!==0&&(r=ki(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Lt(t),i=1<<n,r|=e[n],t&=~i;return r}function Rg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Dg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Lt(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=Rg(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function Da(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ah(){var e=Po;return Po<<=1,!(Po&4194240)&&(Po=64),e}function Ol(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function uo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Lt(t),e[t]=n}function _g(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Lt(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function _u(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Lt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var se=0;function Oh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Lh,ju,Mh,Ih,Fh,_a=!1,Oo=[],kn=null,Tn=null,Cn=null,Vi=new Map,$i=new Map,vn=[],jg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Gc(e,t){switch(e){case"focusin":case"focusout":kn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Cn=null;break;case"pointerover":case"pointerout":Vi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":$i.delete(t.pointerId)}}function di(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=fo(t),t!==null&&ju(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function bg(e,t,n,r,i){switch(t){case"focusin":return kn=di(kn,e,t,n,r,i),!0;case"dragenter":return Tn=di(Tn,e,t,n,r,i),!0;case"mouseover":return Cn=di(Cn,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Vi.set(o,di(Vi.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,$i.set(o,di($i.get(o)||null,e,t,n,r,i)),!0}return!1}function Bh(e){var t=Qn(e.target);if(t!==null){var n=fr(t);if(n!==null){if(t=n.tag,t===13){if(t=Rh(n),t!==null){e.blockedOn=t,Fh(e.priority,function(){Mh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ts(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ja(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ka=r,n.target.dispatchEvent(r),ka=null}else return t=fo(n),t!==null&&ju(t),e.blockedOn=n,!1;t.shift()}return!0}function Jc(e,t,n){ts(e)&&n.delete(t)}function Pg(){_a=!1,kn!==null&&ts(kn)&&(kn=null),Tn!==null&&ts(Tn)&&(Tn=null),Cn!==null&&ts(Cn)&&(Cn=null),Vi.forEach(Jc),$i.forEach(Jc)}function hi(e,t){e.blockedOn===t&&(e.blockedOn=null,_a||(_a=!0,mt.unstable_scheduleCallback(mt.unstable_NormalPriority,Pg)))}function Hi(e){function t(i){return hi(i,e)}if(0<Oo.length){hi(Oo[0],e);for(var n=1;n<Oo.length;n++){var r=Oo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(kn!==null&&hi(kn,e),Tn!==null&&hi(Tn,e),Cn!==null&&hi(Cn,e),Vi.forEach(t),$i.forEach(t),n=0;n<vn.length;n++)r=vn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<vn.length&&(n=vn[0],n.blockedOn===null);)Bh(n),n.blockedOn===null&&vn.shift()}var Fr=an.ReactCurrentBatchConfig,Ns=!0;function Ag(e,t,n,r){var i=se,o=Fr.transition;Fr.transition=null;try{se=1,bu(e,t,n,r)}finally{se=i,Fr.transition=o}}function Og(e,t,n,r){var i=se,o=Fr.transition;Fr.transition=null;try{se=4,bu(e,t,n,r)}finally{se=i,Fr.transition=o}}function bu(e,t,n,r){if(Ns){var i=ja(e,t,n,r);if(i===null)Hl(e,t,r,ks,n),Gc(e,r);else if(bg(i,e,t,n,r))r.stopPropagation();else if(Gc(e,r),t&4&&-1<jg.indexOf(e)){for(;i!==null;){var o=fo(i);if(o!==null&&Lh(o),o=ja(e,t,n,r),o===null&&Hl(e,t,r,ks,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Hl(e,t,r,null,n)}}var ks=null;function ja(e,t,n,r){if(ks=null,e=Ru(r),e=Qn(e),e!==null)if(t=fr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Rh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ks=e,null}function Uh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sg()){case Du:return 1;case bh:return 4;case Ss:case Eg:return 16;case Ph:return 536870912;default:return 16}default:return 16}}var wn=null,Pu=null,ns=null;function zh(){if(ns)return ns;var e,t=Pu,n=t.length,r,i="value"in wn?wn.value:wn.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return ns=i.slice(e,1<r?1-r:void 0)}function rs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Lo(){return!0}function Yc(){return!1}function yt(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Lo:Yc,this.isPropagationStopped=Yc,this}return xe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Lo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Lo)},persist:function(){},isPersistent:Lo}),t}var Xr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Au=yt(Xr),co=xe({},Xr,{view:0,detail:0}),Lg=yt(co),Ll,Ml,pi,el=xe({},co,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ou,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pi&&(pi&&e.type==="mousemove"?(Ll=e.screenX-pi.screenX,Ml=e.screenY-pi.screenY):Ml=Ll=0,pi=e),Ll)},movementY:function(e){return"movementY"in e?e.movementY:Ml}}),Zc=yt(el),Mg=xe({},el,{dataTransfer:0}),Ig=yt(Mg),Fg=xe({},co,{relatedTarget:0}),Il=yt(Fg),Bg=xe({},Xr,{animationName:0,elapsedTime:0,pseudoElement:0}),Ug=yt(Bg),zg=xe({},Xr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Vg=yt(zg),$g=xe({},Xr,{data:0}),Xc=yt($g),Hg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Kg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Wg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function qg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Wg[e])?!!t[e]:!1}function Ou(){return qg}var Qg=xe({},co,{key:function(e){if(e.key){var t=Hg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=rs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Kg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ou,charCode:function(e){return e.type==="keypress"?rs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?rs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Gg=yt(Qg),Jg=xe({},el,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ef=yt(Jg),Yg=xe({},co,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ou}),Zg=yt(Yg),Xg=xe({},Xr,{propertyName:0,elapsedTime:0,pseudoElement:0}),ey=yt(Xg),ty=xe({},el,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ny=yt(ty),ry=[9,13,27,32],Lu=rn&&"CompositionEvent"in window,bi=null;rn&&"documentMode"in document&&(bi=document.documentMode);var iy=rn&&"TextEvent"in window&&!bi,Vh=rn&&(!Lu||bi&&8<bi&&11>=bi),tf=" ",nf=!1;function $h(e,t){switch(e){case"keyup":return ry.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tr=!1;function oy(e,t){switch(e){case"compositionend":return Hh(t);case"keypress":return t.which!==32?null:(nf=!0,tf);case"textInput":return e=t.data,e===tf&&nf?null:e;default:return null}}function sy(e,t){if(Tr)return e==="compositionend"||!Lu&&$h(e,t)?(e=zh(),ns=Pu=wn=null,Tr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Vh&&t.locale!=="ko"?null:t.data;default:return null}}var ly={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function rf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ly[e.type]:t==="textarea"}function Kh(e,t,n,r){Eh(r),t=Ts(t,"onChange"),0<t.length&&(n=new Au("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Pi=null,Ki=null;function ay(e){np(e,0)}function tl(e){var t=Dr(e);if(mh(t))return e}function uy(e,t){if(e==="change")return t}var Wh=!1;if(rn){var Fl;if(rn){var Bl="oninput"in document;if(!Bl){var of=document.createElement("div");of.setAttribute("oninput","return;"),Bl=typeof of.oninput=="function"}Fl=Bl}else Fl=!1;Wh=Fl&&(!document.documentMode||9<document.documentMode)}function sf(){Pi&&(Pi.detachEvent("onpropertychange",qh),Ki=Pi=null)}function qh(e){if(e.propertyName==="value"&&tl(Ki)){var t=[];Kh(t,Ki,e,Ru(e)),Ch(ay,t)}}function cy(e,t,n){e==="focusin"?(sf(),Pi=t,Ki=n,Pi.attachEvent("onpropertychange",qh)):e==="focusout"&&sf()}function fy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return tl(Ki)}function dy(e,t){if(e==="click")return tl(t)}function hy(e,t){if(e==="input"||e==="change")return tl(t)}function py(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var It=typeof Object.is=="function"?Object.is:py;function Wi(e,t){if(It(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!da.call(t,i)||!It(e[i],t[i]))return!1}return!0}function lf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function af(e,t){var n=lf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=lf(n)}}function Qh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Qh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Gh(){for(var e=window,t=vs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=vs(e.document)}return t}function Mu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function my(e){var t=Gh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Qh(n.ownerDocument.documentElement,n)){if(r!==null&&Mu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=af(n,o);var s=af(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gy=rn&&"documentMode"in document&&11>=document.documentMode,Cr=null,ba=null,Ai=null,Pa=!1;function uf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Pa||Cr==null||Cr!==vs(r)||(r=Cr,"selectionStart"in r&&Mu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ai&&Wi(Ai,r)||(Ai=r,r=Ts(ba,"onSelect"),0<r.length&&(t=new Au("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cr)))}function Mo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rr={animationend:Mo("Animation","AnimationEnd"),animationiteration:Mo("Animation","AnimationIteration"),animationstart:Mo("Animation","AnimationStart"),transitionend:Mo("Transition","TransitionEnd")},Ul={},Jh={};rn&&(Jh=document.createElement("div").style,"AnimationEvent"in window||(delete Rr.animationend.animation,delete Rr.animationiteration.animation,delete Rr.animationstart.animation),"TransitionEvent"in window||delete Rr.transitionend.transition);function nl(e){if(Ul[e])return Ul[e];if(!Rr[e])return e;var t=Rr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Jh)return Ul[e]=t[n];return e}var Yh=nl("animationend"),Zh=nl("animationiteration"),Xh=nl("animationstart"),ep=nl("transitionend"),tp=new Map,cf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ln(e,t){tp.set(e,t),cr(t,[e])}for(var zl=0;zl<cf.length;zl++){var Vl=cf[zl],yy=Vl.toLowerCase(),vy=Vl[0].toUpperCase()+Vl.slice(1);Ln(yy,"on"+vy)}Ln(Yh,"onAnimationEnd");Ln(Zh,"onAnimationIteration");Ln(Xh,"onAnimationStart");Ln("dblclick","onDoubleClick");Ln("focusin","onFocus");Ln("focusout","onBlur");Ln(ep,"onTransitionEnd");$r("onMouseEnter",["mouseout","mouseover"]);$r("onMouseLeave",["mouseout","mouseover"]);$r("onPointerEnter",["pointerout","pointerover"]);$r("onPointerLeave",["pointerout","pointerover"]);cr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));cr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));cr("onBeforeInput",["compositionend","keypress","textInput","paste"]);cr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));cr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));cr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ti="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),xy=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ti));function ff(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,yg(r,t,void 0,e),e.currentTarget=null}function np(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;ff(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;ff(i,l,u),o=a}}}if(ws)throw e=Ra,ws=!1,Ra=null,e}function de(e,t){var n=t[Ia];n===void 0&&(n=t[Ia]=new Set);var r=e+"__bubble";n.has(r)||(rp(t,e,2,!1),n.add(r))}function $l(e,t,n){var r=0;t&&(r|=4),rp(n,e,r,t)}var Io="_reactListening"+Math.random().toString(36).slice(2);function qi(e){if(!e[Io]){e[Io]=!0,ch.forEach(function(n){n!=="selectionchange"&&(xy.has(n)||$l(n,!1,e),$l(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Io]||(t[Io]=!0,$l("selectionchange",!1,t))}}function rp(e,t,n,r){switch(Uh(t)){case 1:var i=Ag;break;case 4:i=Og;break;default:i=bu}n=i.bind(null,t,n,e),i=void 0,!Ca||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Hl(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=Qn(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}Ch(function(){var u=o,c=Ru(n),f=[];e:{var h=tp.get(e);if(h!==void 0){var y=Au,m=e;switch(e){case"keypress":if(rs(n)===0)break e;case"keydown":case"keyup":y=Gg;break;case"focusin":m="focus",y=Il;break;case"focusout":m="blur",y=Il;break;case"beforeblur":case"afterblur":y=Il;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Zc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Ig;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Zg;break;case Yh:case Zh:case Xh:y=Ug;break;case ep:y=ey;break;case"scroll":y=Lg;break;case"wheel":y=ny;break;case"copy":case"cut":case"paste":y=Vg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=ef}var v=(t&4)!==0,N=!v&&e==="scroll",g=v?h!==null?h+"Capture":null:h;v=[];for(var p=u,x;p!==null;){x=p;var R=x.stateNode;if(x.tag===5&&R!==null&&(x=R,g!==null&&(R=zi(p,g),R!=null&&v.push(Qi(p,R,x)))),N)break;p=p.return}0<v.length&&(h=new y(h,m,null,n,c),f.push({event:h,listeners:v}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",h&&n!==ka&&(m=n.relatedTarget||n.fromElement)&&(Qn(m)||m[on]))break e;if((y||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,y?(m=n.relatedTarget||n.toElement,y=u,m=m?Qn(m):null,m!==null&&(N=fr(m),m!==N||m.tag!==5&&m.tag!==6)&&(m=null)):(y=null,m=u),y!==m)){if(v=Zc,R="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=ef,R="onPointerLeave",g="onPointerEnter",p="pointer"),N=y==null?h:Dr(y),x=m==null?h:Dr(m),h=new v(R,p+"leave",y,n,c),h.target=N,h.relatedTarget=x,R=null,Qn(c)===u&&(v=new v(g,p+"enter",m,n,c),v.target=x,v.relatedTarget=N,R=v),N=R,y&&m)t:{for(v=y,g=m,p=0,x=v;x;x=vr(x))p++;for(x=0,R=g;R;R=vr(R))x++;for(;0<p-x;)v=vr(v),p--;for(;0<x-p;)g=vr(g),x--;for(;p--;){if(v===g||g!==null&&v===g.alternate)break t;v=vr(v),g=vr(g)}v=null}else v=null;y!==null&&df(f,h,y,v,!1),m!==null&&N!==null&&df(f,N,m,v,!0)}}e:{if(h=u?Dr(u):window,y=h.nodeName&&h.nodeName.toLowerCase(),y==="select"||y==="input"&&h.type==="file")var b=uy;else if(rf(h))if(Wh)b=hy;else{b=fy;var T=cy}else(y=h.nodeName)&&y.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(b=dy);if(b&&(b=b(e,u))){Kh(f,b,n,c);break e}T&&T(e,h,u),e==="focusout"&&(T=h._wrapperState)&&T.controlled&&h.type==="number"&&xa(h,"number",h.value)}switch(T=u?Dr(u):window,e){case"focusin":(rf(T)||T.contentEditable==="true")&&(Cr=T,ba=u,Ai=null);break;case"focusout":Ai=ba=Cr=null;break;case"mousedown":Pa=!0;break;case"contextmenu":case"mouseup":case"dragend":Pa=!1,uf(f,n,c);break;case"selectionchange":if(gy)break;case"keydown":case"keyup":uf(f,n,c)}var S;if(Lu)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Tr?$h(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Vh&&n.locale!=="ko"&&(Tr||_!=="onCompositionStart"?_==="onCompositionEnd"&&Tr&&(S=zh()):(wn=c,Pu="value"in wn?wn.value:wn.textContent,Tr=!0)),T=Ts(u,_),0<T.length&&(_=new Xc(_,e,null,n,c),f.push({event:_,listeners:T}),S?_.data=S:(S=Hh(n),S!==null&&(_.data=S)))),(S=iy?oy(e,n):sy(e,n))&&(u=Ts(u,"onBeforeInput"),0<u.length&&(c=new Xc("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=S))}np(f,t)})}function Qi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ts(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=zi(e,n),o!=null&&r.unshift(Qi(e,o,i)),o=zi(e,t),o!=null&&r.push(Qi(e,o,i))),e=e.return}return r}function vr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function df(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=zi(n,o),a!=null&&s.unshift(Qi(n,a,l))):i||(a=zi(n,o),a!=null&&s.push(Qi(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var wy=/\r\n?/g,Sy=/\u0000|\uFFFD/g;function hf(e){return(typeof e=="string"?e:""+e).replace(wy,`
`).replace(Sy,"")}function Fo(e,t,n){if(t=hf(t),hf(e)!==t&&n)throw Error(A(425))}function Cs(){}var Aa=null,Oa=null;function La(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ma=typeof setTimeout=="function"?setTimeout:void 0,Ey=typeof clearTimeout=="function"?clearTimeout:void 0,pf=typeof Promise=="function"?Promise:void 0,Ny=typeof queueMicrotask=="function"?queueMicrotask:typeof pf<"u"?function(e){return pf.resolve(null).then(e).catch(ky)}:Ma;function ky(e){setTimeout(function(){throw e})}function Kl(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Hi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Hi(t)}function Rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function mf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ei=Math.random().toString(36).slice(2),$t="__reactFiber$"+ei,Gi="__reactProps$"+ei,on="__reactContainer$"+ei,Ia="__reactEvents$"+ei,Ty="__reactListeners$"+ei,Cy="__reactHandles$"+ei;function Qn(e){var t=e[$t];if(t)return t;for(var n=e.parentNode;n;){if(t=n[on]||n[$t]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=mf(e);e!==null;){if(n=e[$t])return n;e=mf(e)}return t}e=n,n=e.parentNode}return null}function fo(e){return e=e[$t]||e[on],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Dr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function rl(e){return e[Gi]||null}var Fa=[],_r=-1;function Mn(e){return{current:e}}function he(e){0>_r||(e.current=Fa[_r],Fa[_r]=null,_r--)}function fe(e,t){_r++,Fa[_r]=e.current,e.current=t}var On={},We=Mn(On),ot=Mn(!1),rr=On;function Hr(e,t){var n=e.type.contextTypes;if(!n)return On;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function st(e){return e=e.childContextTypes,e!=null}function Rs(){he(ot),he(We)}function gf(e,t,n){if(We.current!==On)throw Error(A(168));fe(We,t),fe(ot,n)}function ip(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(A(108,cg(e)||"Unknown",i));return xe({},n,r)}function Ds(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||On,rr=We.current,fe(We,e),fe(ot,ot.current),!0}function yf(e,t,n){var r=e.stateNode;if(!r)throw Error(A(169));n?(e=ip(e,t,rr),r.__reactInternalMemoizedMergedChildContext=e,he(ot),he(We),fe(We,e)):he(ot),fe(ot,n)}var Zt=null,il=!1,Wl=!1;function op(e){Zt===null?Zt=[e]:Zt.push(e)}function Ry(e){il=!0,op(e)}function In(){if(!Wl&&Zt!==null){Wl=!0;var e=0,t=se;try{var n=Zt;for(se=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Zt=null,il=!1}catch(i){throw Zt!==null&&(Zt=Zt.slice(e+1)),jh(Du,In),i}finally{se=t,Wl=!1}}return null}var jr=[],br=0,_s=null,js=0,xt=[],wt=0,ir=null,Xt=1,en="";function Hn(e,t){jr[br++]=js,jr[br++]=_s,_s=e,js=t}function sp(e,t,n){xt[wt++]=Xt,xt[wt++]=en,xt[wt++]=ir,ir=e;var r=Xt;e=en;var i=32-Lt(r)-1;r&=~(1<<i),n+=1;var o=32-Lt(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,Xt=1<<32-Lt(t)+i|n<<i|r,en=o+e}else Xt=1<<o|n<<i|r,en=e}function Iu(e){e.return!==null&&(Hn(e,1),sp(e,1,0))}function Fu(e){for(;e===_s;)_s=jr[--br],jr[br]=null,js=jr[--br],jr[br]=null;for(;e===ir;)ir=xt[--wt],xt[wt]=null,en=xt[--wt],xt[wt]=null,Xt=xt[--wt],xt[wt]=null}var pt=null,ht=null,ge=!1,Ot=null;function lp(e,t){var n=Et(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function vf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,pt=e,ht=Rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,pt=e,ht=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=ir!==null?{id:Xt,overflow:en}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Et(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,pt=e,ht=null,!0):!1;default:return!1}}function Ba(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ua(e){if(ge){var t=ht;if(t){var n=t;if(!vf(e,t)){if(Ba(e))throw Error(A(418));t=Rn(n.nextSibling);var r=pt;t&&vf(e,t)?lp(r,n):(e.flags=e.flags&-4097|2,ge=!1,pt=e)}}else{if(Ba(e))throw Error(A(418));e.flags=e.flags&-4097|2,ge=!1,pt=e}}}function xf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;pt=e}function Bo(e){if(e!==pt)return!1;if(!ge)return xf(e),ge=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!La(e.type,e.memoizedProps)),t&&(t=ht)){if(Ba(e))throw ap(),Error(A(418));for(;t;)lp(e,t),t=Rn(t.nextSibling)}if(xf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ht=Rn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ht=null}}else ht=pt?Rn(e.stateNode.nextSibling):null;return!0}function ap(){for(var e=ht;e;)e=Rn(e.nextSibling)}function Kr(){ht=pt=null,ge=!1}function Bu(e){Ot===null?Ot=[e]:Ot.push(e)}var Dy=an.ReactCurrentBatchConfig;function mi(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(A(309));var r=n.stateNode}if(!r)throw Error(A(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(A(284));if(!n._owner)throw Error(A(290,e))}return e}function Uo(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function wf(e){var t=e._init;return t(e._payload)}function up(e){function t(g,p){if(e){var x=g.deletions;x===null?(g.deletions=[p],g.flags|=16):x.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function i(g,p){return g=bn(g,p),g.index=0,g.sibling=null,g}function o(g,p,x){return g.index=x,e?(x=g.alternate,x!==null?(x=x.index,x<p?(g.flags|=2,p):x):(g.flags|=2,p)):(g.flags|=1048576,p)}function s(g){return e&&g.alternate===null&&(g.flags|=2),g}function l(g,p,x,R){return p===null||p.tag!==6?(p=Xl(x,g.mode,R),p.return=g,p):(p=i(p,x),p.return=g,p)}function a(g,p,x,R){var b=x.type;return b===kr?c(g,p,x.props.children,R,x.key):p!==null&&(p.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===mn&&wf(b)===p.type)?(R=i(p,x.props),R.ref=mi(g,p,x),R.return=g,R):(R=cs(x.type,x.key,x.props,null,g.mode,R),R.ref=mi(g,p,x),R.return=g,R)}function u(g,p,x,R){return p===null||p.tag!==4||p.stateNode.containerInfo!==x.containerInfo||p.stateNode.implementation!==x.implementation?(p=ea(x,g.mode,R),p.return=g,p):(p=i(p,x.children||[]),p.return=g,p)}function c(g,p,x,R,b){return p===null||p.tag!==7?(p=er(x,g.mode,R,b),p.return=g,p):(p=i(p,x),p.return=g,p)}function f(g,p,x){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Xl(""+p,g.mode,x),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case _o:return x=cs(p.type,p.key,p.props,null,g.mode,x),x.ref=mi(g,null,p),x.return=g,x;case Nr:return p=ea(p,g.mode,x),p.return=g,p;case mn:var R=p._init;return f(g,R(p._payload),x)}if(Ni(p)||ci(p))return p=er(p,g.mode,x,null),p.return=g,p;Uo(g,p)}return null}function h(g,p,x,R){var b=p!==null?p.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return b!==null?null:l(g,p,""+x,R);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case _o:return x.key===b?a(g,p,x,R):null;case Nr:return x.key===b?u(g,p,x,R):null;case mn:return b=x._init,h(g,p,b(x._payload),R)}if(Ni(x)||ci(x))return b!==null?null:c(g,p,x,R,null);Uo(g,x)}return null}function y(g,p,x,R,b){if(typeof R=="string"&&R!==""||typeof R=="number")return g=g.get(x)||null,l(p,g,""+R,b);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case _o:return g=g.get(R.key===null?x:R.key)||null,a(p,g,R,b);case Nr:return g=g.get(R.key===null?x:R.key)||null,u(p,g,R,b);case mn:var T=R._init;return y(g,p,x,T(R._payload),b)}if(Ni(R)||ci(R))return g=g.get(x)||null,c(p,g,R,b,null);Uo(p,R)}return null}function m(g,p,x,R){for(var b=null,T=null,S=p,_=p=0,B=null;S!==null&&_<x.length;_++){S.index>_?(B=S,S=null):B=S.sibling;var L=h(g,S,x[_],R);if(L===null){S===null&&(S=B);break}e&&S&&L.alternate===null&&t(g,S),p=o(L,p,_),T===null?b=L:T.sibling=L,T=L,S=B}if(_===x.length)return n(g,S),ge&&Hn(g,_),b;if(S===null){for(;_<x.length;_++)S=f(g,x[_],R),S!==null&&(p=o(S,p,_),T===null?b=S:T.sibling=S,T=S);return ge&&Hn(g,_),b}for(S=r(g,S);_<x.length;_++)B=y(S,g,_,x[_],R),B!==null&&(e&&B.alternate!==null&&S.delete(B.key===null?_:B.key),p=o(B,p,_),T===null?b=B:T.sibling=B,T=B);return e&&S.forEach(function(J){return t(g,J)}),ge&&Hn(g,_),b}function v(g,p,x,R){var b=ci(x);if(typeof b!="function")throw Error(A(150));if(x=b.call(x),x==null)throw Error(A(151));for(var T=b=null,S=p,_=p=0,B=null,L=x.next();S!==null&&!L.done;_++,L=x.next()){S.index>_?(B=S,S=null):B=S.sibling;var J=h(g,S,L.value,R);if(J===null){S===null&&(S=B);break}e&&S&&J.alternate===null&&t(g,S),p=o(J,p,_),T===null?b=J:T.sibling=J,T=J,S=B}if(L.done)return n(g,S),ge&&Hn(g,_),b;if(S===null){for(;!L.done;_++,L=x.next())L=f(g,L.value,R),L!==null&&(p=o(L,p,_),T===null?b=L:T.sibling=L,T=L);return ge&&Hn(g,_),b}for(S=r(g,S);!L.done;_++,L=x.next())L=y(S,g,_,L.value,R),L!==null&&(e&&L.alternate!==null&&S.delete(L.key===null?_:L.key),p=o(L,p,_),T===null?b=L:T.sibling=L,T=L);return e&&S.forEach(function(le){return t(g,le)}),ge&&Hn(g,_),b}function N(g,p,x,R){if(typeof x=="object"&&x!==null&&x.type===kr&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case _o:e:{for(var b=x.key,T=p;T!==null;){if(T.key===b){if(b=x.type,b===kr){if(T.tag===7){n(g,T.sibling),p=i(T,x.props.children),p.return=g,g=p;break e}}else if(T.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===mn&&wf(b)===T.type){n(g,T.sibling),p=i(T,x.props),p.ref=mi(g,T,x),p.return=g,g=p;break e}n(g,T);break}else t(g,T);T=T.sibling}x.type===kr?(p=er(x.props.children,g.mode,R,x.key),p.return=g,g=p):(R=cs(x.type,x.key,x.props,null,g.mode,R),R.ref=mi(g,p,x),R.return=g,g=R)}return s(g);case Nr:e:{for(T=x.key;p!==null;){if(p.key===T)if(p.tag===4&&p.stateNode.containerInfo===x.containerInfo&&p.stateNode.implementation===x.implementation){n(g,p.sibling),p=i(p,x.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=ea(x,g.mode,R),p.return=g,g=p}return s(g);case mn:return T=x._init,N(g,p,T(x._payload),R)}if(Ni(x))return m(g,p,x,R);if(ci(x))return v(g,p,x,R);Uo(g,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,p!==null&&p.tag===6?(n(g,p.sibling),p=i(p,x),p.return=g,g=p):(n(g,p),p=Xl(x,g.mode,R),p.return=g,g=p),s(g)):n(g,p)}return N}var Wr=up(!0),cp=up(!1),bs=Mn(null),Ps=null,Pr=null,Uu=null;function zu(){Uu=Pr=Ps=null}function Vu(e){var t=bs.current;he(bs),e._currentValue=t}function za(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Br(e,t){Ps=e,Uu=Pr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(it=!0),e.firstContext=null)}function Tt(e){var t=e._currentValue;if(Uu!==e)if(e={context:e,memoizedValue:t,next:null},Pr===null){if(Ps===null)throw Error(A(308));Pr=e,Ps.dependencies={lanes:0,firstContext:e}}else Pr=Pr.next=e;return t}var Gn=null;function $u(e){Gn===null?Gn=[e]:Gn.push(e)}function fp(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,$u(t)):(n.next=i.next,i.next=n),t.interleaved=n,sn(e,r)}function sn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gn=!1;function Hu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function dp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function tn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ne&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,sn(e,n)}return i=r.interleaved,i===null?(t.next=t,$u(r)):(t.next=i.next,i.next=t),r.interleaved=t,sn(e,n)}function is(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,_u(e,n)}}function Sf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function As(e,t,n,r){var i=e.updateQueue;gn=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var f=i.baseState;s=0,c=u=a=null,l=o;do{var h=l.lane,y=l.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var m=e,v=l;switch(h=t,y=n,v.tag){case 1:if(m=v.payload,typeof m=="function"){f=m.call(y,f,h);break e}f=m;break e;case 3:m.flags=m.flags&-65537|128;case 0:if(m=v.payload,h=typeof m=="function"?m.call(y,f,h):m,h==null)break e;f=xe({},f,h);break e;case 2:gn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[l]:h.push(l))}else y={eventTime:y,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=y,a=f):c=c.next=y,s|=h;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;h=l,l=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);sr|=s,e.lanes=s,e.memoizedState=f}}function Ef(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(A(191,i));i.call(r)}}}var ho={},Kt=Mn(ho),Ji=Mn(ho),Yi=Mn(ho);function Jn(e){if(e===ho)throw Error(A(174));return e}function Ku(e,t){switch(fe(Yi,t),fe(Ji,e),fe(Kt,ho),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Sa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Sa(t,e)}he(Kt),fe(Kt,t)}function qr(){he(Kt),he(Ji),he(Yi)}function hp(e){Jn(Yi.current);var t=Jn(Kt.current),n=Sa(t,e.type);t!==n&&(fe(Ji,e),fe(Kt,n))}function Wu(e){Ji.current===e&&(he(Kt),he(Ji))}var ye=Mn(0);function Os(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ql=[];function qu(){for(var e=0;e<ql.length;e++)ql[e]._workInProgressVersionPrimary=null;ql.length=0}var os=an.ReactCurrentDispatcher,Ql=an.ReactCurrentBatchConfig,or=0,ve=null,be=null,Oe=null,Ls=!1,Oi=!1,Zi=0,_y=0;function Ve(){throw Error(A(321))}function Qu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!It(e[n],t[n]))return!1;return!0}function Gu(e,t,n,r,i,o){if(or=o,ve=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,os.current=e===null||e.memoizedState===null?Ay:Oy,e=n(r,i),Oi){o=0;do{if(Oi=!1,Zi=0,25<=o)throw Error(A(301));o+=1,Oe=be=null,t.updateQueue=null,os.current=Ly,e=n(r,i)}while(Oi)}if(os.current=Ms,t=be!==null&&be.next!==null,or=0,Oe=be=ve=null,Ls=!1,t)throw Error(A(300));return e}function Ju(){var e=Zi!==0;return Zi=0,e}function Vt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Oe===null?ve.memoizedState=Oe=e:Oe=Oe.next=e,Oe}function Ct(){if(be===null){var e=ve.alternate;e=e!==null?e.memoizedState:null}else e=be.next;var t=Oe===null?ve.memoizedState:Oe.next;if(t!==null)Oe=t,be=e;else{if(e===null)throw Error(A(310));be=e,e={memoizedState:be.memoizedState,baseState:be.baseState,baseQueue:be.baseQueue,queue:be.queue,next:null},Oe===null?ve.memoizedState=Oe=e:Oe=Oe.next=e}return Oe}function Xi(e,t){return typeof t=="function"?t(e):t}function Gl(e){var t=Ct(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=be,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((or&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,ve.lanes|=c,sr|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,It(r,t.memoizedState)||(it=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,ve.lanes|=o,sr|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Jl(e){var t=Ct(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);It(o,t.memoizedState)||(it=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function pp(){}function mp(e,t){var n=ve,r=Ct(),i=t(),o=!It(r.memoizedState,i);if(o&&(r.memoizedState=i,it=!0),r=r.queue,Yu(vp.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||Oe!==null&&Oe.memoizedState.tag&1){if(n.flags|=2048,eo(9,yp.bind(null,n,r,i,t),void 0,null),Le===null)throw Error(A(349));or&30||gp(n,t,i)}return i}function gp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ve.updateQueue,t===null?(t={lastEffect:null,stores:null},ve.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function yp(e,t,n,r){t.value=n,t.getSnapshot=r,xp(t)&&wp(e)}function vp(e,t,n){return n(function(){xp(t)&&wp(e)})}function xp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!It(e,n)}catch{return!0}}function wp(e){var t=sn(e,1);t!==null&&Mt(t,e,1,-1)}function Nf(e){var t=Vt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Xi,lastRenderedState:e},t.queue=e,e=e.dispatch=Py.bind(null,ve,e),[t.memoizedState,e]}function eo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ve.updateQueue,t===null?(t={lastEffect:null,stores:null},ve.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Sp(){return Ct().memoizedState}function ss(e,t,n,r){var i=Vt();ve.flags|=e,i.memoizedState=eo(1|t,n,void 0,r===void 0?null:r)}function ol(e,t,n,r){var i=Ct();r=r===void 0?null:r;var o=void 0;if(be!==null){var s=be.memoizedState;if(o=s.destroy,r!==null&&Qu(r,s.deps)){i.memoizedState=eo(t,n,o,r);return}}ve.flags|=e,i.memoizedState=eo(1|t,n,o,r)}function kf(e,t){return ss(8390656,8,e,t)}function Yu(e,t){return ol(2048,8,e,t)}function Ep(e,t){return ol(4,2,e,t)}function Np(e,t){return ol(4,4,e,t)}function kp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Tp(e,t,n){return n=n!=null?n.concat([e]):null,ol(4,4,kp.bind(null,t,e),n)}function Zu(){}function Cp(e,t){var n=Ct();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Rp(e,t){var n=Ct();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Dp(e,t,n){return or&21?(It(n,t)||(n=Ah(),ve.lanes|=n,sr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,it=!0),e.memoizedState=n)}function jy(e,t){var n=se;se=n!==0&&4>n?n:4,e(!0);var r=Ql.transition;Ql.transition={};try{e(!1),t()}finally{se=n,Ql.transition=r}}function _p(){return Ct().memoizedState}function by(e,t,n){var r=jn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},jp(e))bp(t,n);else if(n=fp(e,t,n,r),n!==null){var i=Xe();Mt(n,e,r,i),Pp(n,t,r)}}function Py(e,t,n){var r=jn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(jp(e))bp(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,It(l,s)){var a=t.interleaved;a===null?(i.next=i,$u(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=fp(e,t,i,r),n!==null&&(i=Xe(),Mt(n,e,r,i),Pp(n,t,r))}}function jp(e){var t=e.alternate;return e===ve||t!==null&&t===ve}function bp(e,t){Oi=Ls=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Pp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,_u(e,n)}}var Ms={readContext:Tt,useCallback:Ve,useContext:Ve,useEffect:Ve,useImperativeHandle:Ve,useInsertionEffect:Ve,useLayoutEffect:Ve,useMemo:Ve,useReducer:Ve,useRef:Ve,useState:Ve,useDebugValue:Ve,useDeferredValue:Ve,useTransition:Ve,useMutableSource:Ve,useSyncExternalStore:Ve,useId:Ve,unstable_isNewReconciler:!1},Ay={readContext:Tt,useCallback:function(e,t){return Vt().memoizedState=[e,t===void 0?null:t],e},useContext:Tt,useEffect:kf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ss(4194308,4,kp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ss(4194308,4,e,t)},useInsertionEffect:function(e,t){return ss(4,2,e,t)},useMemo:function(e,t){var n=Vt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Vt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=by.bind(null,ve,e),[r.memoizedState,e]},useRef:function(e){var t=Vt();return e={current:e},t.memoizedState=e},useState:Nf,useDebugValue:Zu,useDeferredValue:function(e){return Vt().memoizedState=e},useTransition:function(){var e=Nf(!1),t=e[0];return e=jy.bind(null,e[1]),Vt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ve,i=Vt();if(ge){if(n===void 0)throw Error(A(407));n=n()}else{if(n=t(),Le===null)throw Error(A(349));or&30||gp(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,kf(vp.bind(null,r,o,e),[e]),r.flags|=2048,eo(9,yp.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Vt(),t=Le.identifierPrefix;if(ge){var n=en,r=Xt;n=(r&~(1<<32-Lt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Zi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=_y++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Oy={readContext:Tt,useCallback:Cp,useContext:Tt,useEffect:Yu,useImperativeHandle:Tp,useInsertionEffect:Ep,useLayoutEffect:Np,useMemo:Rp,useReducer:Gl,useRef:Sp,useState:function(){return Gl(Xi)},useDebugValue:Zu,useDeferredValue:function(e){var t=Ct();return Dp(t,be.memoizedState,e)},useTransition:function(){var e=Gl(Xi)[0],t=Ct().memoizedState;return[e,t]},useMutableSource:pp,useSyncExternalStore:mp,useId:_p,unstable_isNewReconciler:!1},Ly={readContext:Tt,useCallback:Cp,useContext:Tt,useEffect:Yu,useImperativeHandle:Tp,useInsertionEffect:Ep,useLayoutEffect:Np,useMemo:Rp,useReducer:Jl,useRef:Sp,useState:function(){return Jl(Xi)},useDebugValue:Zu,useDeferredValue:function(e){var t=Ct();return be===null?t.memoizedState=e:Dp(t,be.memoizedState,e)},useTransition:function(){var e=Jl(Xi)[0],t=Ct().memoizedState;return[e,t]},useMutableSource:pp,useSyncExternalStore:mp,useId:_p,unstable_isNewReconciler:!1};function jt(e,t){if(e&&e.defaultProps){t=xe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Va(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:xe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var sl={isMounted:function(e){return(e=e._reactInternals)?fr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Xe(),i=jn(e),o=tn(r,i);o.payload=t,n!=null&&(o.callback=n),t=Dn(e,o,i),t!==null&&(Mt(t,e,i,r),is(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Xe(),i=jn(e),o=tn(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Dn(e,o,i),t!==null&&(Mt(t,e,i,r),is(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Xe(),r=jn(e),i=tn(n,r);i.tag=2,t!=null&&(i.callback=t),t=Dn(e,i,r),t!==null&&(Mt(t,e,r,n),is(t,e,r))}};function Tf(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Wi(n,r)||!Wi(i,o):!0}function Ap(e,t,n){var r=!1,i=On,o=t.contextType;return typeof o=="object"&&o!==null?o=Tt(o):(i=st(t)?rr:We.current,r=t.contextTypes,o=(r=r!=null)?Hr(e,i):On),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=sl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Cf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&sl.enqueueReplaceState(t,t.state,null)}function $a(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Hu(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Tt(o):(o=st(t)?rr:We.current,i.context=Hr(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Va(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&sl.enqueueReplaceState(i,i.state,null),As(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Qr(e,t){try{var n="",r=t;do n+=ug(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Yl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ha(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var My=typeof WeakMap=="function"?WeakMap:Map;function Op(e,t,n){n=tn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Fs||(Fs=!0,eu=r),Ha(e,t)},n}function Lp(e,t,n){n=tn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ha(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Ha(e,t),typeof r!="function"&&(_n===null?_n=new Set([this]):_n.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Rf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new My;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Jy.bind(null,e,t,n),t.then(e,e))}function Df(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function _f(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=tn(-1,1),t.tag=2,Dn(n,t,1))),n.lanes|=1),e)}var Iy=an.ReactCurrentOwner,it=!1;function Ye(e,t,n,r){t.child=e===null?cp(t,null,n,r):Wr(t,e.child,n,r)}function jf(e,t,n,r,i){n=n.render;var o=t.ref;return Br(t,i),r=Gu(e,t,n,r,o,i),n=Ju(),e!==null&&!it?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ln(e,t,i)):(ge&&n&&Iu(t),t.flags|=1,Ye(e,t,r,i),t.child)}function bf(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!sc(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Mp(e,t,o,r,i)):(e=cs(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Wi,n(s,r)&&e.ref===t.ref)return ln(e,t,i)}return t.flags|=1,e=bn(o,r),e.ref=t.ref,e.return=t,t.child=e}function Mp(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Wi(o,r)&&e.ref===t.ref)if(it=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(it=!0);else return t.lanes=e.lanes,ln(e,t,i)}return Ka(e,t,n,r,i)}function Ip(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},fe(Or,ft),ft|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,fe(Or,ft),ft|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,fe(Or,ft),ft|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,fe(Or,ft),ft|=r;return Ye(e,t,i,n),t.child}function Fp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ka(e,t,n,r,i){var o=st(n)?rr:We.current;return o=Hr(t,o),Br(t,i),n=Gu(e,t,n,r,o,i),r=Ju(),e!==null&&!it?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ln(e,t,i)):(ge&&r&&Iu(t),t.flags|=1,Ye(e,t,n,i),t.child)}function Pf(e,t,n,r,i){if(st(n)){var o=!0;Ds(t)}else o=!1;if(Br(t,i),t.stateNode===null)ls(e,t),Ap(t,n,r),$a(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Tt(u):(u=st(n)?rr:We.current,u=Hr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Cf(t,s,r,u),gn=!1;var h=t.memoizedState;s.state=h,As(t,r,s,i),a=t.memoizedState,l!==r||h!==a||ot.current||gn?(typeof c=="function"&&(Va(t,n,c,r),a=t.memoizedState),(l=gn||Tf(t,n,l,r,h,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,dp(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:jt(t.type,l),s.props=u,f=t.pendingProps,h=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Tt(a):(a=st(n)?rr:We.current,a=Hr(t,a));var y=n.getDerivedStateFromProps;(c=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||h!==a)&&Cf(t,s,r,a),gn=!1,h=t.memoizedState,s.state=h,As(t,r,s,i);var m=t.memoizedState;l!==f||h!==m||ot.current||gn?(typeof y=="function"&&(Va(t,n,y,r),m=t.memoizedState),(u=gn||Tf(t,n,u,r,h,m,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,m,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,m,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),s.props=r,s.state=m,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Wa(e,t,n,r,o,i)}function Wa(e,t,n,r,i,o){Fp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&yf(t,n,!1),ln(e,t,o);r=t.stateNode,Iy.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Wr(t,e.child,null,o),t.child=Wr(t,null,l,o)):Ye(e,t,l,o),t.memoizedState=r.state,i&&yf(t,n,!0),t.child}function Bp(e){var t=e.stateNode;t.pendingContext?gf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&gf(e,t.context,!1),Ku(e,t.containerInfo)}function Af(e,t,n,r,i){return Kr(),Bu(i),t.flags|=256,Ye(e,t,n,r),t.child}var qa={dehydrated:null,treeContext:null,retryLane:0};function Qa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Up(e,t,n){var r=t.pendingProps,i=ye.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),fe(ye,i&1),e===null)return Ua(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=ul(s,r,0,null),e=er(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Qa(n),t.memoizedState=qa,e):Xu(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Fy(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=bn(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=bn(l,o):(o=er(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?Qa(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=qa,r}return o=e.child,e=o.sibling,r=bn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Xu(e,t){return t=ul({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function zo(e,t,n,r){return r!==null&&Bu(r),Wr(t,e.child,null,n),e=Xu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Fy(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Yl(Error(A(422))),zo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=ul({mode:"visible",children:r.children},i,0,null),o=er(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Wr(t,e.child,null,s),t.child.memoizedState=Qa(s),t.memoizedState=qa,o);if(!(t.mode&1))return zo(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(A(419)),r=Yl(o,r,void 0),zo(e,t,s,r)}if(l=(s&e.childLanes)!==0,it||l){if(r=Le,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,sn(e,i),Mt(r,e,i,-1))}return oc(),r=Yl(Error(A(421))),zo(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Yy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,ht=Rn(i.nextSibling),pt=t,ge=!0,Ot=null,e!==null&&(xt[wt++]=Xt,xt[wt++]=en,xt[wt++]=ir,Xt=e.id,en=e.overflow,ir=t),t=Xu(t,r.children),t.flags|=4096,t)}function Of(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),za(e.return,t,n)}function Zl(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function zp(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ye(e,t,r.children,n),r=ye.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Of(e,n,t);else if(e.tag===19)Of(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(fe(ye,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Os(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Zl(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Os(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Zl(t,!0,n,null,o);break;case"together":Zl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ls(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ln(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),sr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,n=bn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=bn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function By(e,t,n){switch(t.tag){case 3:Bp(t),Kr();break;case 5:hp(t);break;case 1:st(t.type)&&Ds(t);break;case 4:Ku(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;fe(bs,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(fe(ye,ye.current&1),t.flags|=128,null):n&t.child.childLanes?Up(e,t,n):(fe(ye,ye.current&1),e=ln(e,t,n),e!==null?e.sibling:null);fe(ye,ye.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return zp(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),fe(ye,ye.current),r)break;return null;case 22:case 23:return t.lanes=0,Ip(e,t,n)}return ln(e,t,n)}var Vp,Ga,$p,Hp;Vp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ga=function(){};$p=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Jn(Kt.current);var o=null;switch(n){case"input":i=ya(e,i),r=ya(e,r),o=[];break;case"select":i=xe({},i,{value:void 0}),r=xe({},r,{value:void 0}),o=[];break;case"textarea":i=wa(e,i),r=wa(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Cs)}Ea(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Bi.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Bi.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&de("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Hp=function(e,t,n,r){n!==r&&(t.flags|=4)};function gi(e,t){if(!ge)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function $e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Uy(e,t,n){var r=t.pendingProps;switch(Fu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $e(t),null;case 1:return st(t.type)&&Rs(),$e(t),null;case 3:return r=t.stateNode,qr(),he(ot),he(We),qu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Bo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ot!==null&&(ru(Ot),Ot=null))),Ga(e,t),$e(t),null;case 5:Wu(t);var i=Jn(Yi.current);if(n=t.type,e!==null&&t.stateNode!=null)$p(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(A(166));return $e(t),null}if(e=Jn(Kt.current),Bo(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[$t]=t,r[Gi]=o,e=(t.mode&1)!==0,n){case"dialog":de("cancel",r),de("close",r);break;case"iframe":case"object":case"embed":de("load",r);break;case"video":case"audio":for(i=0;i<Ti.length;i++)de(Ti[i],r);break;case"source":de("error",r);break;case"img":case"image":case"link":de("error",r),de("load",r);break;case"details":de("toggle",r);break;case"input":Vc(r,o),de("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},de("invalid",r);break;case"textarea":Hc(r,o),de("invalid",r)}Ea(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&Fo(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&Fo(r.textContent,l,e),i=["children",""+l]):Bi.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&de("scroll",r)}switch(n){case"input":jo(r),$c(r,o,!0);break;case"textarea":jo(r),Kc(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Cs)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=vh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[$t]=t,e[Gi]=r,Vp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Na(n,r),n){case"dialog":de("cancel",e),de("close",e),i=r;break;case"iframe":case"object":case"embed":de("load",e),i=r;break;case"video":case"audio":for(i=0;i<Ti.length;i++)de(Ti[i],e);i=r;break;case"source":de("error",e),i=r;break;case"img":case"image":case"link":de("error",e),de("load",e),i=r;break;case"details":de("toggle",e),i=r;break;case"input":Vc(e,r),i=ya(e,r),de("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=xe({},r,{value:void 0}),de("invalid",e);break;case"textarea":Hc(e,r),i=wa(e,r),de("invalid",e);break;default:i=r}Ea(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Sh(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&xh(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Ui(e,a):typeof a=="number"&&Ui(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Bi.hasOwnProperty(o)?a!=null&&o==="onScroll"&&de("scroll",e):a!=null&&Nu(e,o,a,s))}switch(n){case"input":jo(e),$c(e,r,!1);break;case"textarea":jo(e),Kc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+An(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Lr(e,!!r.multiple,o,!1):r.defaultValue!=null&&Lr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Cs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return $e(t),null;case 6:if(e&&t.stateNode!=null)Hp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(A(166));if(n=Jn(Yi.current),Jn(Kt.current),Bo(t)){if(r=t.stateNode,n=t.memoizedProps,r[$t]=t,(o=r.nodeValue!==n)&&(e=pt,e!==null))switch(e.tag){case 3:Fo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Fo(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$t]=t,t.stateNode=r}return $e(t),null;case 13:if(he(ye),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ge&&ht!==null&&t.mode&1&&!(t.flags&128))ap(),Kr(),t.flags|=98560,o=!1;else if(o=Bo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(A(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(A(317));o[$t]=t}else Kr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;$e(t),o=!1}else Ot!==null&&(ru(Ot),Ot=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ye.current&1?Pe===0&&(Pe=3):oc())),t.updateQueue!==null&&(t.flags|=4),$e(t),null);case 4:return qr(),Ga(e,t),e===null&&qi(t.stateNode.containerInfo),$e(t),null;case 10:return Vu(t.type._context),$e(t),null;case 17:return st(t.type)&&Rs(),$e(t),null;case 19:if(he(ye),o=t.memoizedState,o===null)return $e(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)gi(o,!1);else{if(Pe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Os(e),s!==null){for(t.flags|=128,gi(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return fe(ye,ye.current&1|2),t.child}e=e.sibling}o.tail!==null&&Te()>Gr&&(t.flags|=128,r=!0,gi(o,!1),t.lanes=4194304)}else{if(!r)if(e=Os(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),gi(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!ge)return $e(t),null}else 2*Te()-o.renderingStartTime>Gr&&n!==1073741824&&(t.flags|=128,r=!0,gi(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Te(),t.sibling=null,n=ye.current,fe(ye,r?n&1|2:n&1),t):($e(t),null);case 22:case 23:return ic(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ft&1073741824&&($e(t),t.subtreeFlags&6&&(t.flags|=8192)):$e(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function zy(e,t){switch(Fu(t),t.tag){case 1:return st(t.type)&&Rs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return qr(),he(ot),he(We),qu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Wu(t),null;case 13:if(he(ye),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));Kr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return he(ye),null;case 4:return qr(),null;case 10:return Vu(t.type._context),null;case 22:case 23:return ic(),null;case 24:return null;default:return null}}var Vo=!1,He=!1,Vy=typeof WeakSet=="function"?WeakSet:Set,I=null;function Ar(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ne(e,t,r)}else n.current=null}function Ja(e,t,n){try{n()}catch(r){Ne(e,t,r)}}var Lf=!1;function $y(e,t){if(Aa=Ns,e=Gh(),Mu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,h=null;t:for(;;){for(var y;f!==n||i!==0&&f.nodeType!==3||(l=s+i),f!==o||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(y=f.firstChild)!==null;)h=f,f=y;for(;;){if(f===e)break t;if(h===n&&++u===i&&(l=s),h===o&&++c===r&&(a=s),(y=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=y}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oa={focusedElem:e,selectionRange:n},Ns=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var m=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(m!==null){var v=m.memoizedProps,N=m.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?v:jt(t.type,v),N);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var x=t.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(R){Ne(t,t.return,R)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return m=Lf,Lf=!1,m}function Li(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Ja(t,n,o)}i=i.next}while(i!==r)}}function ll(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ya(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Kp(e){var t=e.alternate;t!==null&&(e.alternate=null,Kp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$t],delete t[Gi],delete t[Ia],delete t[Ty],delete t[Cy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Wp(e){return e.tag===5||e.tag===3||e.tag===4}function Mf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Wp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Za(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Cs));else if(r!==4&&(e=e.child,e!==null))for(Za(e,t,n),e=e.sibling;e!==null;)Za(e,t,n),e=e.sibling}function Xa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Xa(e,t,n),e=e.sibling;e!==null;)Xa(e,t,n),e=e.sibling}var Fe=null,bt=!1;function hn(e,t,n){for(n=n.child;n!==null;)qp(e,t,n),n=n.sibling}function qp(e,t,n){if(Ht&&typeof Ht.onCommitFiberUnmount=="function")try{Ht.onCommitFiberUnmount(Xs,n)}catch{}switch(n.tag){case 5:He||Ar(n,t);case 6:var r=Fe,i=bt;Fe=null,hn(e,t,n),Fe=r,bt=i,Fe!==null&&(bt?(e=Fe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Fe.removeChild(n.stateNode));break;case 18:Fe!==null&&(bt?(e=Fe,n=n.stateNode,e.nodeType===8?Kl(e.parentNode,n):e.nodeType===1&&Kl(e,n),Hi(e)):Kl(Fe,n.stateNode));break;case 4:r=Fe,i=bt,Fe=n.stateNode.containerInfo,bt=!0,hn(e,t,n),Fe=r,bt=i;break;case 0:case 11:case 14:case 15:if(!He&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Ja(n,t,s),i=i.next}while(i!==r)}hn(e,t,n);break;case 1:if(!He&&(Ar(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Ne(n,t,l)}hn(e,t,n);break;case 21:hn(e,t,n);break;case 22:n.mode&1?(He=(r=He)||n.memoizedState!==null,hn(e,t,n),He=r):hn(e,t,n);break;default:hn(e,t,n)}}function If(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Vy),t.forEach(function(r){var i=Zy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function _t(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Fe=l.stateNode,bt=!1;break e;case 3:Fe=l.stateNode.containerInfo,bt=!0;break e;case 4:Fe=l.stateNode.containerInfo,bt=!0;break e}l=l.return}if(Fe===null)throw Error(A(160));qp(o,s,i),Fe=null,bt=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Ne(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Qp(t,e),t=t.sibling}function Qp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(_t(t,e),Ut(e),r&4){try{Li(3,e,e.return),ll(3,e)}catch(v){Ne(e,e.return,v)}try{Li(5,e,e.return)}catch(v){Ne(e,e.return,v)}}break;case 1:_t(t,e),Ut(e),r&512&&n!==null&&Ar(n,n.return);break;case 5:if(_t(t,e),Ut(e),r&512&&n!==null&&Ar(n,n.return),e.flags&32){var i=e.stateNode;try{Ui(i,"")}catch(v){Ne(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&gh(i,o),Na(l,s);var u=Na(l,o);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Sh(i,f):c==="dangerouslySetInnerHTML"?xh(i,f):c==="children"?Ui(i,f):Nu(i,c,f,u)}switch(l){case"input":va(i,o);break;case"textarea":yh(i,o);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var y=o.value;y!=null?Lr(i,!!o.multiple,y,!1):h!==!!o.multiple&&(o.defaultValue!=null?Lr(i,!!o.multiple,o.defaultValue,!0):Lr(i,!!o.multiple,o.multiple?[]:"",!1))}i[Gi]=o}catch(v){Ne(e,e.return,v)}}break;case 6:if(_t(t,e),Ut(e),r&4){if(e.stateNode===null)throw Error(A(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){Ne(e,e.return,v)}}break;case 3:if(_t(t,e),Ut(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Hi(t.containerInfo)}catch(v){Ne(e,e.return,v)}break;case 4:_t(t,e),Ut(e);break;case 13:_t(t,e),Ut(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(nc=Te())),r&4&&If(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(He=(u=He)||c,_t(t,e),He=u):_t(t,e),Ut(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(I=e,c=e.child;c!==null;){for(f=I=c;I!==null;){switch(h=I,y=h.child,h.tag){case 0:case 11:case 14:case 15:Li(4,h,h.return);break;case 1:Ar(h,h.return);var m=h.stateNode;if(typeof m.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(v){Ne(r,n,v)}}break;case 5:Ar(h,h.return);break;case 22:if(h.memoizedState!==null){Bf(f);continue}}y!==null?(y.return=h,I=y):Bf(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=wh("display",s))}catch(v){Ne(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Ne(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:_t(t,e),Ut(e),r&4&&If(e);break;case 21:break;default:_t(t,e),Ut(e)}}function Ut(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Wp(n)){var r=n;break e}n=n.return}throw Error(A(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Ui(i,""),r.flags&=-33);var o=Mf(e);Xa(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Mf(e);Za(e,l,s);break;default:throw Error(A(161))}}catch(a){Ne(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hy(e,t,n){I=e,Gp(e)}function Gp(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var i=I,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Vo;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||He;l=Vo;var u=He;if(Vo=s,(He=a)&&!u)for(I=i;I!==null;)s=I,a=s.child,s.tag===22&&s.memoizedState!==null?Uf(i):a!==null?(a.return=s,I=a):Uf(i);for(;o!==null;)I=o,Gp(o),o=o.sibling;I=i,Vo=l,He=u}Ff(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,I=o):Ff(e)}}function Ff(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:He||ll(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!He)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:jt(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Ef(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ef(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Hi(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}He||t.flags&512&&Ya(t)}catch(h){Ne(t,t.return,h)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Bf(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function Uf(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ll(4,t)}catch(a){Ne(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Ne(t,i,a)}}var o=t.return;try{Ya(t)}catch(a){Ne(t,o,a)}break;case 5:var s=t.return;try{Ya(t)}catch(a){Ne(t,s,a)}}}catch(a){Ne(t,t.return,a)}if(t===e){I=null;break}var l=t.sibling;if(l!==null){l.return=t.return,I=l;break}I=t.return}}var Ky=Math.ceil,Is=an.ReactCurrentDispatcher,ec=an.ReactCurrentOwner,kt=an.ReactCurrentBatchConfig,ne=0,Le=null,_e=null,Ue=0,ft=0,Or=Mn(0),Pe=0,to=null,sr=0,al=0,tc=0,Mi=null,rt=null,nc=0,Gr=1/0,Jt=null,Fs=!1,eu=null,_n=null,$o=!1,Sn=null,Bs=0,Ii=0,tu=null,as=-1,us=0;function Xe(){return ne&6?Te():as!==-1?as:as=Te()}function jn(e){return e.mode&1?ne&2&&Ue!==0?Ue&-Ue:Dy.transition!==null?(us===0&&(us=Ah()),us):(e=se,e!==0||(e=window.event,e=e===void 0?16:Uh(e.type)),e):1}function Mt(e,t,n,r){if(50<Ii)throw Ii=0,tu=null,Error(A(185));uo(e,n,r),(!(ne&2)||e!==Le)&&(e===Le&&(!(ne&2)&&(al|=n),Pe===4&&xn(e,Ue)),lt(e,r),n===1&&ne===0&&!(t.mode&1)&&(Gr=Te()+500,il&&In()))}function lt(e,t){var n=e.callbackNode;Dg(e,t);var r=Es(e,e===Le?Ue:0);if(r===0)n!==null&&Qc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Qc(n),t===1)e.tag===0?Ry(zf.bind(null,e)):op(zf.bind(null,e)),Ny(function(){!(ne&6)&&In()}),n=null;else{switch(Oh(r)){case 1:n=Du;break;case 4:n=bh;break;case 16:n=Ss;break;case 536870912:n=Ph;break;default:n=Ss}n=rm(n,Jp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Jp(e,t){if(as=-1,us=0,ne&6)throw Error(A(327));var n=e.callbackNode;if(Ur()&&e.callbackNode!==n)return null;var r=Es(e,e===Le?Ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Us(e,r);else{t=r;var i=ne;ne|=2;var o=Zp();(Le!==e||Ue!==t)&&(Jt=null,Gr=Te()+500,Xn(e,t));do try{Qy();break}catch(l){Yp(e,l)}while(!0);zu(),Is.current=o,ne=i,_e!==null?t=0:(Le=null,Ue=0,t=Pe)}if(t!==0){if(t===2&&(i=Da(e),i!==0&&(r=i,t=nu(e,i))),t===1)throw n=to,Xn(e,0),xn(e,r),lt(e,Te()),n;if(t===6)xn(e,r);else{if(i=e.current.alternate,!(r&30)&&!Wy(i)&&(t=Us(e,r),t===2&&(o=Da(e),o!==0&&(r=o,t=nu(e,o))),t===1))throw n=to,Xn(e,0),xn(e,r),lt(e,Te()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(A(345));case 2:Kn(e,rt,Jt);break;case 3:if(xn(e,r),(r&130023424)===r&&(t=nc+500-Te(),10<t)){if(Es(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Xe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Ma(Kn.bind(null,e,rt,Jt),t);break}Kn(e,rt,Jt);break;case 4:if(xn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Lt(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=Te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ky(r/1960))-r,10<r){e.timeoutHandle=Ma(Kn.bind(null,e,rt,Jt),r);break}Kn(e,rt,Jt);break;case 5:Kn(e,rt,Jt);break;default:throw Error(A(329))}}}return lt(e,Te()),e.callbackNode===n?Jp.bind(null,e):null}function nu(e,t){var n=Mi;return e.current.memoizedState.isDehydrated&&(Xn(e,t).flags|=256),e=Us(e,t),e!==2&&(t=rt,rt=n,t!==null&&ru(t)),e}function ru(e){rt===null?rt=e:rt.push.apply(rt,e)}function Wy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!It(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function xn(e,t){for(t&=~tc,t&=~al,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Lt(t),r=1<<n;e[n]=-1,t&=~r}}function zf(e){if(ne&6)throw Error(A(327));Ur();var t=Es(e,0);if(!(t&1))return lt(e,Te()),null;var n=Us(e,t);if(e.tag!==0&&n===2){var r=Da(e);r!==0&&(t=r,n=nu(e,r))}if(n===1)throw n=to,Xn(e,0),xn(e,t),lt(e,Te()),n;if(n===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Kn(e,rt,Jt),lt(e,Te()),null}function rc(e,t){var n=ne;ne|=1;try{return e(t)}finally{ne=n,ne===0&&(Gr=Te()+500,il&&In())}}function lr(e){Sn!==null&&Sn.tag===0&&!(ne&6)&&Ur();var t=ne;ne|=1;var n=kt.transition,r=se;try{if(kt.transition=null,se=1,e)return e()}finally{se=r,kt.transition=n,ne=t,!(ne&6)&&In()}}function ic(){ft=Or.current,he(Or)}function Xn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ey(n)),_e!==null)for(n=_e.return;n!==null;){var r=n;switch(Fu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Rs();break;case 3:qr(),he(ot),he(We),qu();break;case 5:Wu(r);break;case 4:qr();break;case 13:he(ye);break;case 19:he(ye);break;case 10:Vu(r.type._context);break;case 22:case 23:ic()}n=n.return}if(Le=e,_e=e=bn(e.current,null),Ue=ft=t,Pe=0,to=null,tc=al=sr=0,rt=Mi=null,Gn!==null){for(t=0;t<Gn.length;t++)if(n=Gn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}Gn=null}return e}function Yp(e,t){do{var n=_e;try{if(zu(),os.current=Ms,Ls){for(var r=ve.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Ls=!1}if(or=0,Oe=be=ve=null,Oi=!1,Zi=0,ec.current=null,n===null||n.return===null){Pe=1,to=t,_e=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=Ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var y=Df(s);if(y!==null){y.flags&=-257,_f(y,s,l,o,t),y.mode&1&&Rf(o,u,t),t=y,a=u;var m=t.updateQueue;if(m===null){var v=new Set;v.add(a),t.updateQueue=v}else m.add(a);break e}else{if(!(t&1)){Rf(o,u,t),oc();break e}a=Error(A(426))}}else if(ge&&l.mode&1){var N=Df(s);if(N!==null){!(N.flags&65536)&&(N.flags|=256),_f(N,s,l,o,t),Bu(Qr(a,l));break e}}o=a=Qr(a,l),Pe!==4&&(Pe=2),Mi===null?Mi=[o]:Mi.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var g=Op(o,a,t);Sf(o,g);break e;case 1:l=a;var p=o.type,x=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(_n===null||!_n.has(x)))){o.flags|=65536,t&=-t,o.lanes|=t;var R=Lp(o,l,t);Sf(o,R);break e}}o=o.return}while(o!==null)}em(n)}catch(b){t=b,_e===n&&n!==null&&(_e=n=n.return);continue}break}while(!0)}function Zp(){var e=Is.current;return Is.current=Ms,e===null?Ms:e}function oc(){(Pe===0||Pe===3||Pe===2)&&(Pe=4),Le===null||!(sr&268435455)&&!(al&268435455)||xn(Le,Ue)}function Us(e,t){var n=ne;ne|=2;var r=Zp();(Le!==e||Ue!==t)&&(Jt=null,Xn(e,t));do try{qy();break}catch(i){Yp(e,i)}while(!0);if(zu(),ne=n,Is.current=r,_e!==null)throw Error(A(261));return Le=null,Ue=0,Pe}function qy(){for(;_e!==null;)Xp(_e)}function Qy(){for(;_e!==null&&!xg();)Xp(_e)}function Xp(e){var t=nm(e.alternate,e,ft);e.memoizedProps=e.pendingProps,t===null?em(e):_e=t,ec.current=null}function em(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=zy(n,t),n!==null){n.flags&=32767,_e=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Pe=6,_e=null;return}}else if(n=Uy(n,t,ft),n!==null){_e=n;return}if(t=t.sibling,t!==null){_e=t;return}_e=t=e}while(t!==null);Pe===0&&(Pe=5)}function Kn(e,t,n){var r=se,i=kt.transition;try{kt.transition=null,se=1,Gy(e,t,n,r)}finally{kt.transition=i,se=r}return null}function Gy(e,t,n,r){do Ur();while(Sn!==null);if(ne&6)throw Error(A(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(_g(e,o),e===Le&&(_e=Le=null,Ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||$o||($o=!0,rm(Ss,function(){return Ur(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=kt.transition,kt.transition=null;var s=se;se=1;var l=ne;ne|=4,ec.current=null,$y(e,n),Qp(n,e),my(Oa),Ns=!!Aa,Oa=Aa=null,e.current=n,Hy(n),wg(),ne=l,se=s,kt.transition=o}else e.current=n;if($o&&($o=!1,Sn=e,Bs=i),o=e.pendingLanes,o===0&&(_n=null),Ng(n.stateNode),lt(e,Te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Fs)throw Fs=!1,e=eu,eu=null,e;return Bs&1&&e.tag!==0&&Ur(),o=e.pendingLanes,o&1?e===tu?Ii++:(Ii=0,tu=e):Ii=0,In(),null}function Ur(){if(Sn!==null){var e=Oh(Bs),t=kt.transition,n=se;try{if(kt.transition=null,se=16>e?16:e,Sn===null)var r=!1;else{if(e=Sn,Sn=null,Bs=0,ne&6)throw Error(A(331));var i=ne;for(ne|=4,I=e.current;I!==null;){var o=I,s=o.child;if(I.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(I=u;I!==null;){var c=I;switch(c.tag){case 0:case 11:case 15:Li(8,c,o)}var f=c.child;if(f!==null)f.return=c,I=f;else for(;I!==null;){c=I;var h=c.sibling,y=c.return;if(Kp(c),c===u){I=null;break}if(h!==null){h.return=y,I=h;break}I=y}}}var m=o.alternate;if(m!==null){var v=m.child;if(v!==null){m.child=null;do{var N=v.sibling;v.sibling=null,v=N}while(v!==null)}}I=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,I=s;else e:for(;I!==null;){if(o=I,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Li(9,o,o.return)}var g=o.sibling;if(g!==null){g.return=o.return,I=g;break e}I=o.return}}var p=e.current;for(I=p;I!==null;){s=I;var x=s.child;if(s.subtreeFlags&2064&&x!==null)x.return=s,I=x;else e:for(s=p;I!==null;){if(l=I,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:ll(9,l)}}catch(b){Ne(l,l.return,b)}if(l===s){I=null;break e}var R=l.sibling;if(R!==null){R.return=l.return,I=R;break e}I=l.return}}if(ne=i,In(),Ht&&typeof Ht.onPostCommitFiberRoot=="function")try{Ht.onPostCommitFiberRoot(Xs,e)}catch{}r=!0}return r}finally{se=n,kt.transition=t}}return!1}function Vf(e,t,n){t=Qr(n,t),t=Op(e,t,1),e=Dn(e,t,1),t=Xe(),e!==null&&(uo(e,1,t),lt(e,t))}function Ne(e,t,n){if(e.tag===3)Vf(e,e,n);else for(;t!==null;){if(t.tag===3){Vf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_n===null||!_n.has(r))){e=Qr(n,e),e=Lp(t,e,1),t=Dn(t,e,1),e=Xe(),t!==null&&(uo(t,1,e),lt(t,e));break}}t=t.return}}function Jy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Xe(),e.pingedLanes|=e.suspendedLanes&n,Le===e&&(Ue&n)===n&&(Pe===4||Pe===3&&(Ue&130023424)===Ue&&500>Te()-nc?Xn(e,0):tc|=n),lt(e,t)}function tm(e,t){t===0&&(e.mode&1?(t=Ao,Ao<<=1,!(Ao&130023424)&&(Ao=4194304)):t=1);var n=Xe();e=sn(e,t),e!==null&&(uo(e,t,n),lt(e,n))}function Yy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),tm(e,n)}function Zy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(A(314))}r!==null&&r.delete(t),tm(e,n)}var nm;nm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ot.current)it=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return it=!1,By(e,t,n);it=!!(e.flags&131072)}else it=!1,ge&&t.flags&1048576&&sp(t,js,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ls(e,t),e=t.pendingProps;var i=Hr(t,We.current);Br(t,n),i=Gu(null,t,r,e,i,n);var o=Ju();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,st(r)?(o=!0,Ds(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Hu(t),i.updater=sl,t.stateNode=i,i._reactInternals=t,$a(t,r,e,n),t=Wa(null,t,r,!0,o,n)):(t.tag=0,ge&&o&&Iu(t),Ye(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ls(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ev(r),e=jt(r,e),i){case 0:t=Ka(null,t,r,e,n);break e;case 1:t=Pf(null,t,r,e,n);break e;case 11:t=jf(null,t,r,e,n);break e;case 14:t=bf(null,t,r,jt(r.type,e),n);break e}throw Error(A(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:jt(r,i),Ka(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:jt(r,i),Pf(e,t,r,i,n);case 3:e:{if(Bp(t),e===null)throw Error(A(387));r=t.pendingProps,o=t.memoizedState,i=o.element,dp(e,t),As(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Qr(Error(A(423)),t),t=Af(e,t,r,n,i);break e}else if(r!==i){i=Qr(Error(A(424)),t),t=Af(e,t,r,n,i);break e}else for(ht=Rn(t.stateNode.containerInfo.firstChild),pt=t,ge=!0,Ot=null,n=cp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Kr(),r===i){t=ln(e,t,n);break e}Ye(e,t,r,n)}t=t.child}return t;case 5:return hp(t),e===null&&Ua(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,La(r,i)?s=null:o!==null&&La(r,o)&&(t.flags|=32),Fp(e,t),Ye(e,t,s,n),t.child;case 6:return e===null&&Ua(t),null;case 13:return Up(e,t,n);case 4:return Ku(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wr(t,null,r,n):Ye(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:jt(r,i),jf(e,t,r,i,n);case 7:return Ye(e,t,t.pendingProps,n),t.child;case 8:return Ye(e,t,t.pendingProps.children,n),t.child;case 12:return Ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,fe(bs,r._currentValue),r._currentValue=s,o!==null)if(It(o.value,s)){if(o.children===i.children&&!ot.current){t=ln(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=tn(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),za(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(A(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),za(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ye(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Br(t,n),i=Tt(i),r=r(i),t.flags|=1,Ye(e,t,r,n),t.child;case 14:return r=t.type,i=jt(r,t.pendingProps),i=jt(r.type,i),bf(e,t,r,i,n);case 15:return Mp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:jt(r,i),ls(e,t),t.tag=1,st(r)?(e=!0,Ds(t)):e=!1,Br(t,n),Ap(t,r,i),$a(t,r,i,n),Wa(null,t,r,!0,e,n);case 19:return zp(e,t,n);case 22:return Ip(e,t,n)}throw Error(A(156,t.tag))};function rm(e,t){return jh(e,t)}function Xy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Et(e,t,n,r){return new Xy(e,t,n,r)}function sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ev(e){if(typeof e=="function")return sc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Tu)return 11;if(e===Cu)return 14}return 2}function bn(e,t){var n=e.alternate;return n===null?(n=Et(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function cs(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")sc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case kr:return er(n.children,i,o,t);case ku:s=8,i|=8;break;case ha:return e=Et(12,n,t,i|2),e.elementType=ha,e.lanes=o,e;case pa:return e=Et(13,n,t,i),e.elementType=pa,e.lanes=o,e;case ma:return e=Et(19,n,t,i),e.elementType=ma,e.lanes=o,e;case hh:return ul(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case fh:s=10;break e;case dh:s=9;break e;case Tu:s=11;break e;case Cu:s=14;break e;case mn:s=16,r=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=Et(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function er(e,t,n,r){return e=Et(7,e,r,t),e.lanes=n,e}function ul(e,t,n,r){return e=Et(22,e,r,t),e.elementType=hh,e.lanes=n,e.stateNode={isHidden:!1},e}function Xl(e,t,n){return e=Et(6,e,null,t),e.lanes=n,e}function ea(e,t,n){return t=Et(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function tv(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ol(0),this.expirationTimes=Ol(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ol(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function lc(e,t,n,r,i,o,s,l,a){return e=new tv(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Et(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Hu(o),e}function nv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Nr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function im(e){if(!e)return On;e=e._reactInternals;e:{if(fr(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(st(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var n=e.type;if(st(n))return ip(e,n,t)}return t}function om(e,t,n,r,i,o,s,l,a){return e=lc(n,r,!0,e,i,o,s,l,a),e.context=im(null),n=e.current,r=Xe(),i=jn(n),o=tn(r,i),o.callback=t??null,Dn(n,o,i),e.current.lanes=i,uo(e,i,r),lt(e,r),e}function cl(e,t,n,r){var i=t.current,o=Xe(),s=jn(i);return n=im(n),t.context===null?t.context=n:t.pendingContext=n,t=tn(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Dn(i,t,s),e!==null&&(Mt(e,i,s,o),is(e,i,s)),s}function zs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function $f(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ac(e,t){$f(e,t),(e=e.alternate)&&$f(e,t)}function rv(){return null}var sm=typeof reportError=="function"?reportError:function(e){console.error(e)};function uc(e){this._internalRoot=e}fl.prototype.render=uc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));cl(e,t,null,null)};fl.prototype.unmount=uc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;lr(function(){cl(null,e,null,null)}),t[on]=null}};function fl(e){this._internalRoot=e}fl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ih();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vn.length&&t!==0&&t<vn[n].priority;n++);vn.splice(n,0,e),n===0&&Bh(e)}};function cc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function dl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Hf(){}function iv(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=zs(s);o.call(u)}}var s=om(t,r,e,0,null,!1,!1,"",Hf);return e._reactRootContainer=s,e[on]=s.current,qi(e.nodeType===8?e.parentNode:e),lr(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=zs(a);l.call(u)}}var a=lc(e,0,!1,null,null,!1,!1,"",Hf);return e._reactRootContainer=a,e[on]=a.current,qi(e.nodeType===8?e.parentNode:e),lr(function(){cl(t,a,n,r)}),a}function hl(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=zs(s);l.call(a)}}cl(t,s,e,i)}else s=iv(n,t,e,i,r);return zs(s)}Lh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ki(t.pendingLanes);n!==0&&(_u(t,n|1),lt(t,Te()),!(ne&6)&&(Gr=Te()+500,In()))}break;case 13:lr(function(){var r=sn(e,1);if(r!==null){var i=Xe();Mt(r,e,1,i)}}),ac(e,1)}};ju=function(e){if(e.tag===13){var t=sn(e,134217728);if(t!==null){var n=Xe();Mt(t,e,134217728,n)}ac(e,134217728)}};Mh=function(e){if(e.tag===13){var t=jn(e),n=sn(e,t);if(n!==null){var r=Xe();Mt(n,e,t,r)}ac(e,t)}};Ih=function(){return se};Fh=function(e,t){var n=se;try{return se=e,t()}finally{se=n}};Ta=function(e,t,n){switch(t){case"input":if(va(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=rl(r);if(!i)throw Error(A(90));mh(r),va(r,i)}}}break;case"textarea":yh(e,n);break;case"select":t=n.value,t!=null&&Lr(e,!!n.multiple,t,!1)}};kh=rc;Th=lr;var ov={usingClientEntryPoint:!1,Events:[fo,Dr,rl,Eh,Nh,rc]},yi={findFiberByHostInstance:Qn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},sv={bundleType:yi.bundleType,version:yi.version,rendererPackageName:yi.rendererPackageName,rendererConfig:yi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:an.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Dh(e),e===null?null:e.stateNode},findFiberByHostInstance:yi.findFiberByHostInstance||rv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ho=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ho.isDisabled&&Ho.supportsFiber)try{Xs=Ho.inject(sv),Ht=Ho}catch{}}gt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ov;gt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!cc(t))throw Error(A(200));return nv(e,t,null,n)};gt.createRoot=function(e,t){if(!cc(e))throw Error(A(299));var n=!1,r="",i=sm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=lc(e,1,!1,null,null,n,!1,r,i),e[on]=t.current,qi(e.nodeType===8?e.parentNode:e),new uc(t)};gt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=Dh(t),e=e===null?null:e.stateNode,e};gt.flushSync=function(e){return lr(e)};gt.hydrate=function(e,t,n){if(!dl(t))throw Error(A(200));return hl(null,e,t,!0,n)};gt.hydrateRoot=function(e,t,n){if(!cc(e))throw Error(A(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=sm;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=om(t,null,e,1,n??null,i,!1,o,s),e[on]=t.current,qi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new fl(t)};gt.render=function(e,t,n){if(!dl(t))throw Error(A(200));return hl(null,e,t,!1,n)};gt.unmountComponentAtNode=function(e){if(!dl(e))throw Error(A(40));return e._reactRootContainer?(lr(function(){hl(null,null,e,!1,function(){e._reactRootContainer=null,e[on]=null})}),!0):!1};gt.unstable_batchedUpdates=rc;gt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!dl(n))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return hl(e,t,n,!1,r)};gt.version="18.3.1-next-f1338f8080-20240426";function lm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(lm)}catch(e){console.error(e)}}lm(),lh.exports=gt;var fc=lh.exports;const lv=Qd(fc),av=qd({__proto__:null,default:lv},[fc]);var Kf=fc;fa.createRoot=Kf.createRoot,fa.hydrateRoot=Kf.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function me(){return me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},me.apply(this,arguments)}var De;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(De||(De={}));const Wf="popstate";function uv(e){e===void 0&&(e={});function t(r,i){let{pathname:o,search:s,hash:l}=r.location;return no("",{pathname:o,search:s,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:ar(i)}return fv(t,n,null,e)}function ee(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Jr(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function cv(){return Math.random().toString(36).substr(2,8)}function qf(e,t){return{usr:e.state,key:e.key,idx:t}}function no(e,t,n,r){return n===void 0&&(n=null),me({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Fn(t):t,{state:n,key:t&&t.key||r||cv()})}function ar(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Fn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function fv(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,s=i.history,l=De.Pop,a=null,u=c();u==null&&(u=0,s.replaceState(me({},s.state,{idx:u}),""));function c(){return(s.state||{idx:null}).idx}function f(){l=De.Pop;let N=c(),g=N==null?null:N-u;u=N,a&&a({action:l,location:v.location,delta:g})}function h(N,g){l=De.Push;let p=no(v.location,N,g);u=c()+1;let x=qf(p,u),R=v.createHref(p);try{s.pushState(x,"",R)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;i.location.assign(R)}o&&a&&a({action:l,location:v.location,delta:1})}function y(N,g){l=De.Replace;let p=no(v.location,N,g);u=c();let x=qf(p,u),R=v.createHref(p);s.replaceState(x,"",R),o&&a&&a({action:l,location:v.location,delta:0})}function m(N){let g=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof N=="string"?N:ar(N);return p=p.replace(/ $/,"%20"),ee(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let v={get action(){return l},get location(){return e(i,s)},listen(N){if(a)throw new Error("A history only accepts one active listener");return i.addEventListener(Wf,f),a=N,()=>{i.removeEventListener(Wf,f),a=null}},createHref(N){return t(i,N)},createURL:m,encodeLocation(N){let g=m(N);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:h,replace:y,go(N){return s.go(N)}};return v}var oe;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(oe||(oe={}));const dv=new Set(["lazy","caseSensitive","path","id","index","children"]);function hv(e){return e.index===!0}function Vs(e,t,n,r){return n===void 0&&(n=[]),r===void 0&&(r={}),e.map((i,o)=>{let s=[...n,String(o)],l=typeof i.id=="string"?i.id:s.join("-");if(ee(i.index!==!0||!i.children,"Cannot specify children on an index route"),ee(!r[l],'Found a route id collision on id "'+l+`".  Route id's must be globally unique within Data Router usages`),hv(i)){let a=me({},i,t(i),{id:l});return r[l]=a,a}else{let a=me({},i,t(i),{id:l,children:void 0});return r[l]=a,i.children&&(a.children=Vs(i.children,t,s,r)),a}})}function Wn(e,t,n){return n===void 0&&(n="/"),fs(e,t,n,!1)}function fs(e,t,n,r){let i=typeof t=="string"?Fn(t):t,o=ti(i.pathname||"/",n);if(o==null)return null;let s=am(e);mv(s);let l=null;for(let a=0;l==null&&a<s.length;++a){let u=Cv(o);l=kv(s[a],u,r)}return l}function pv(e,t){let{route:n,pathname:r,params:i}=e;return{id:n.id,pathname:r,params:i,data:t[n.id],handle:n.handle}}function am(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(o,s,l)=>{let a={relativePath:l===void 0?o.path||"":l,caseSensitive:o.caseSensitive===!0,childrenIndex:s,route:o};a.relativePath.startsWith("/")&&(ee(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=nn([r,a.relativePath]),c=n.concat(a);o.children&&o.children.length>0&&(ee(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),am(o.children,t,c,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:Ev(u,o.index),routesMeta:c})};return e.forEach((o,s)=>{var l;if(o.path===""||!((l=o.path)!=null&&l.includes("?")))i(o,s);else for(let a of um(o.path))i(o,s,a)}),t}function um(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return i?[o,""]:[o];let s=um(r.join("/")),l=[];return l.push(...s.map(a=>a===""?o:[o,a].join("/"))),i&&l.push(...s),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function mv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Nv(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const gv=/^:[\w-]+$/,yv=3,vv=2,xv=1,wv=10,Sv=-2,Qf=e=>e==="*";function Ev(e,t){let n=e.split("/"),r=n.length;return n.some(Qf)&&(r+=Sv),t&&(r+=vv),n.filter(i=>!Qf(i)).reduce((i,o)=>i+(gv.test(o)?yv:o===""?xv:wv),r)}function Nv(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function kv(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},o="/",s=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,c=o==="/"?t:t.slice(o.length)||"/",f=Gf({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},c),h=a.route;if(!f&&u&&n&&!r[r.length-1].route.index&&(f=Gf({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},c)),!f)return null;Object.assign(i,f.params),s.push({params:i,pathname:nn([o,f.pathname]),pathnameBase:_v(nn([o,f.pathnameBase])),route:h}),f.pathnameBase!=="/"&&(o=nn([o,f.pathnameBase]))}return s}function Gf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Tv(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let o=i[0],s=o.replace(/(.)\/+$/,"$1"),l=i.slice(1);return{params:r.reduce((u,c,f)=>{let{paramName:h,isOptional:y}=c;if(h==="*"){let v=l[f]||"";s=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const m=l[f];return y&&!m?u[h]=void 0:u[h]=(m||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:s,pattern:e}}function Tv(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Jr(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function Cv(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Jr(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ti(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Rv(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?Fn(e):e;return{pathname:n?n.startsWith("/")?n:Dv(n,t):t,search:jv(r),hash:bv(i)}}function Dv(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function ta(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function cm(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function dc(e,t){let n=cm(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function hc(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=Fn(e):(i=me({},e),ee(!i.pathname||!i.pathname.includes("?"),ta("?","pathname","search",i)),ee(!i.pathname||!i.pathname.includes("#"),ta("#","pathname","hash",i)),ee(!i.search||!i.search.includes("#"),ta("#","search","hash",i)));let o=e===""||i.pathname==="",s=o?"/":i.pathname,l;if(s==null)l=n;else{let f=t.length-1;if(!r&&s.startsWith("..")){let h=s.split("/");for(;h[0]==="..";)h.shift(),f-=1;i.pathname=h.join("/")}l=f>=0?t[f]:"/"}let a=Rv(i,l),u=s&&s!=="/"&&s.endsWith("/"),c=(o||s===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||c)&&(a.pathname+="/"),a}const nn=e=>e.join("/").replace(/\/\/+/g,"/"),_v=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),jv=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,bv=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class $s{constructor(t,n,r,i){i===void 0&&(i=!1),this.status=t,this.statusText=n||"",this.internal=i,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function ro(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const fm=["post","put","patch","delete"],Pv=new Set(fm),Av=["get",...fm],Ov=new Set(Av),Lv=new Set([301,302,303,307,308]),Mv=new Set([307,308]),na={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Iv={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},vi={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},pc=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Fv=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),dm="remix-router-transitions";function Bv(e){const t=e.window?e.window:typeof window<"u"?window:void 0,n=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",r=!n;ee(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let i;if(e.mapRouteProperties)i=e.mapRouteProperties;else if(e.detectErrorBoundary){let w=e.detectErrorBoundary;i=E=>({hasErrorBoundary:w(E)})}else i=Fv;let o={},s=Vs(e.routes,i,void 0,o),l,a=e.basename||"/",u=e.dataStrategy||$v,c=e.patchRoutesOnNavigation,f=me({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),h=null,y=new Set,m=null,v=null,N=null,g=e.hydrationData!=null,p=Wn(s,e.history.location,a),x=!1,R=null;if(p==null&&!c){let w=nt(404,{pathname:e.history.location.pathname}),{matches:E,route:k}=sd(s);p=E,R={[k.id]:w}}p&&!e.hydrationData&&ko(p,s,e.history.location.pathname).active&&(p=null);let b;if(p)if(p.some(w=>w.route.lazy))b=!1;else if(!p.some(w=>w.route.loader))b=!0;else if(f.v7_partialHydration){let w=e.hydrationData?e.hydrationData.loaderData:null,E=e.hydrationData?e.hydrationData.errors:null;if(E){let k=p.findIndex(P=>E[P.route.id]!==void 0);b=p.slice(0,k+1).every(P=>!ou(P.route,w,E))}else b=p.every(k=>!ou(k.route,w,E))}else b=e.hydrationData!=null;else if(b=!1,p=[],f.v7_partialHydration){let w=ko(null,s,e.history.location.pathname);w.active&&w.matches&&(x=!0,p=w.matches)}let T,S={historyAction:e.history.action,location:e.history.location,matches:p,initialized:b,navigation:na,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||R,fetchers:new Map,blockers:new Map},_=De.Pop,B=!1,L,J=!1,le=new Map,pe=null,Me=!1,Rt=!1,un=[],cn=new Set,O=new Map,H=0,W=-1,ae=new Map,ue=new Set,Dt=new Map,ct=new Map,qe=new Set,Qe=new Map,vt=new Map,So;function v0(){if(h=e.history.listen(w=>{let{action:E,location:k,delta:P}=w;if(So){So(),So=void 0;return}Jr(vt.size===0||P!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let M=Pc({currentLocation:S.location,nextLocation:k,historyAction:E});if(M&&P!=null){let $=new Promise(K=>{So=K});e.history.go(P*-1),No(M,{state:"blocked",location:k,proceed(){No(M,{state:"proceeding",proceed:void 0,reset:void 0,location:k}),$.then(()=>e.history.go(P))},reset(){let K=new Map(S.blockers);K.set(M,vi),Ge({blockers:K})}});return}return Un(E,k)}),n){r1(t,le);let w=()=>i1(t,le);t.addEventListener("pagehide",w),pe=()=>t.removeEventListener("pagehide",w)}return S.initialized||Un(De.Pop,S.location,{initialHydration:!0}),T}function x0(){h&&h(),pe&&pe(),y.clear(),L&&L.abort(),S.fetchers.forEach((w,E)=>Eo(E)),S.blockers.forEach((w,E)=>bc(E))}function w0(w){return y.add(w),()=>y.delete(w)}function Ge(w,E){E===void 0&&(E={}),S=me({},S,w);let k=[],P=[];f.v7_fetcherPersist&&S.fetchers.forEach((M,$)=>{M.state==="idle"&&(qe.has($)?P.push($):k.push($))}),qe.forEach(M=>{!S.fetchers.has(M)&&!O.has(M)&&P.push(M)}),[...y].forEach(M=>M(S,{deletedFetchers:P,viewTransitionOpts:E.viewTransitionOpts,flushSync:E.flushSync===!0})),f.v7_fetcherPersist?(k.forEach(M=>S.fetchers.delete(M)),P.forEach(M=>Eo(M))):P.forEach(M=>qe.delete(M))}function pr(w,E,k){var P,M;let{flushSync:$}=k===void 0?{}:k,K=S.actionData!=null&&S.navigation.formMethod!=null&&Pt(S.navigation.formMethod)&&S.navigation.state==="loading"&&((P=w.state)==null?void 0:P._isRedirect)!==!0,U;E.actionData?Object.keys(E.actionData).length>0?U=E.actionData:U=null:K?U=S.actionData:U=null;let z=E.loaderData?id(S.loaderData,E.loaderData,E.matches||[],E.errors):S.loaderData,F=S.blockers;F.size>0&&(F=new Map(F),F.forEach((te,Ie)=>F.set(Ie,vi)));let V=B===!0||S.navigation.formMethod!=null&&Pt(S.navigation.formMethod)&&((M=w.state)==null?void 0:M._isRedirect)!==!0;l&&(s=l,l=void 0),Me||_===De.Pop||(_===De.Push?e.history.push(w,w.state):_===De.Replace&&e.history.replace(w,w.state));let G;if(_===De.Pop){let te=le.get(S.location.pathname);te&&te.has(w.pathname)?G={currentLocation:S.location,nextLocation:w}:le.has(w.pathname)&&(G={currentLocation:w,nextLocation:S.location})}else if(J){let te=le.get(S.location.pathname);te?te.add(w.pathname):(te=new Set([w.pathname]),le.set(S.location.pathname,te)),G={currentLocation:S.location,nextLocation:w}}Ge(me({},E,{actionData:U,loaderData:z,historyAction:_,location:w,initialized:!0,navigation:na,revalidation:"idle",restoreScrollPosition:Oc(w,E.matches||S.matches),preventScrollReset:V,blockers:F}),{viewTransitionOpts:G,flushSync:$===!0}),_=De.Pop,B=!1,J=!1,Me=!1,Rt=!1,un=[]}async function kc(w,E){if(typeof w=="number"){e.history.go(w);return}let k=iu(S.location,S.matches,a,f.v7_prependBasename,w,f.v7_relativeSplatPath,E==null?void 0:E.fromRouteId,E==null?void 0:E.relative),{path:P,submission:M,error:$}=Jf(f.v7_normalizeFormMethod,!1,k,E),K=S.location,U=no(S.location,P,E&&E.state);U=me({},U,e.history.encodeLocation(U));let z=E&&E.replace!=null?E.replace:void 0,F=De.Push;z===!0?F=De.Replace:z===!1||M!=null&&Pt(M.formMethod)&&M.formAction===S.location.pathname+S.location.search&&(F=De.Replace);let V=E&&"preventScrollReset"in E?E.preventScrollReset===!0:void 0,G=(E&&E.flushSync)===!0,te=Pc({currentLocation:K,nextLocation:U,historyAction:F});if(te){No(te,{state:"blocked",location:U,proceed(){No(te,{state:"proceeding",proceed:void 0,reset:void 0,location:U}),kc(w,E)},reset(){let Ie=new Map(S.blockers);Ie.set(te,vi),Ge({blockers:Ie})}});return}return await Un(F,U,{submission:M,pendingError:$,preventScrollReset:V,replace:E&&E.replace,enableViewTransition:E&&E.viewTransition,flushSync:G})}function S0(){if(Tl(),Ge({revalidation:"loading"}),S.navigation.state!=="submitting"){if(S.navigation.state==="idle"){Un(S.historyAction,S.location,{startUninterruptedRevalidation:!0});return}Un(_||S.historyAction,S.navigation.location,{overrideNavigation:S.navigation,enableViewTransition:J===!0})}}async function Un(w,E,k){L&&L.abort(),L=null,_=w,Me=(k&&k.startUninterruptedRevalidation)===!0,b0(S.location,S.matches),B=(k&&k.preventScrollReset)===!0,J=(k&&k.enableViewTransition)===!0;let P=l||s,M=k&&k.overrideNavigation,$=k!=null&&k.initialHydration&&S.matches&&S.matches.length>0&&!x?S.matches:Wn(P,E,a),K=(k&&k.flushSync)===!0;if($&&S.initialized&&!Rt&&Gv(S.location,E)&&!(k&&k.submission&&Pt(k.submission.formMethod))){pr(E,{matches:$},{flushSync:K});return}let U=ko($,P,E.pathname);if(U.active&&U.matches&&($=U.matches),!$){let{error:ce,notFoundMatches:ie,route:we}=Cl(E.pathname);pr(E,{matches:ie,loaderData:{},errors:{[we.id]:ce}},{flushSync:K});return}L=new AbortController;let z=xr(e.history,E,L.signal,k&&k.submission),F;if(k&&k.pendingError)F=[qn($).route.id,{type:oe.error,error:k.pendingError}];else if(k&&k.submission&&Pt(k.submission.formMethod)){let ce=await E0(z,E,k.submission,$,U.active,{replace:k.replace,flushSync:K});if(ce.shortCircuited)return;if(ce.pendingActionResult){let[ie,we]=ce.pendingActionResult;if(dt(we)&&ro(we.error)&&we.error.status===404){L=null,pr(E,{matches:ce.matches,loaderData:{},errors:{[ie]:we.error}});return}}$=ce.matches||$,F=ce.pendingActionResult,M=ra(E,k.submission),K=!1,U.active=!1,z=xr(e.history,z.url,z.signal)}let{shortCircuited:V,matches:G,loaderData:te,errors:Ie}=await N0(z,E,$,U.active,M,k&&k.submission,k&&k.fetcherSubmission,k&&k.replace,k&&k.initialHydration===!0,K,F);V||(L=null,pr(E,me({matches:G||$},od(F),{loaderData:te,errors:Ie})))}async function E0(w,E,k,P,M,$){$===void 0&&($={}),Tl();let K=t1(E,k);if(Ge({navigation:K},{flushSync:$.flushSync===!0}),M){let F=await To(P,E.pathname,w.signal);if(F.type==="aborted")return{shortCircuited:!0};if(F.type==="error"){let V=qn(F.partialMatches).route.id;return{matches:F.partialMatches,pendingActionResult:[V,{type:oe.error,error:F.error}]}}else if(F.matches)P=F.matches;else{let{notFoundMatches:V,error:G,route:te}=Cl(E.pathname);return{matches:V,pendingActionResult:[te.id,{type:oe.error,error:G}]}}}let U,z=Ci(P,E);if(!z.route.action&&!z.route.lazy)U={type:oe.error,error:nt(405,{method:w.method,pathname:E.pathname,routeId:z.route.id})};else if(U=(await si("action",S,w,[z],P,null))[z.route.id],w.signal.aborted)return{shortCircuited:!0};if(Yn(U)){let F;return $&&$.replace!=null?F=$.replace:F=td(U.response.headers.get("Location"),new URL(w.url),a)===S.location.pathname+S.location.search,await zn(w,U,!0,{submission:k,replace:F}),{shortCircuited:!0}}if(En(U))throw nt(400,{type:"defer-action"});if(dt(U)){let F=qn(P,z.route.id);return($&&$.replace)!==!0&&(_=De.Push),{matches:P,pendingActionResult:[F.route.id,U]}}return{matches:P,pendingActionResult:[z.route.id,U]}}async function N0(w,E,k,P,M,$,K,U,z,F,V){let G=M||ra(E,$),te=$||K||ad(G),Ie=!Me&&(!f.v7_partialHydration||!z);if(P){if(Ie){let Se=Tc(V);Ge(me({navigation:G},Se!==void 0?{actionData:Se}:{}),{flushSync:F})}let re=await To(k,E.pathname,w.signal);if(re.type==="aborted")return{shortCircuited:!0};if(re.type==="error"){let Se=qn(re.partialMatches).route.id;return{matches:re.partialMatches,loaderData:{},errors:{[Se]:re.error}}}else if(re.matches)k=re.matches;else{let{error:Se,notFoundMatches:gr,route:ui}=Cl(E.pathname);return{matches:gr,loaderData:{},errors:{[ui.id]:Se}}}}let ce=l||s,[ie,we]=Zf(e.history,S,k,te,E,f.v7_partialHydration&&z===!0,f.v7_skipActionErrorRevalidation,Rt,un,cn,qe,Dt,ue,ce,a,V);if(Rl(re=>!(k&&k.some(Se=>Se.route.id===re))||ie&&ie.some(Se=>Se.route.id===re)),W=++H,ie.length===0&&we.length===0){let re=_c();return pr(E,me({matches:k,loaderData:{},errors:V&&dt(V[1])?{[V[0]]:V[1].error}:null},od(V),re?{fetchers:new Map(S.fetchers)}:{}),{flushSync:F}),{shortCircuited:!0}}if(Ie){let re={};if(!P){re.navigation=G;let Se=Tc(V);Se!==void 0&&(re.actionData=Se)}we.length>0&&(re.fetchers=k0(we)),Ge(re,{flushSync:F})}we.forEach(re=>{dn(re.key),re.controller&&O.set(re.key,re.controller)});let mr=()=>we.forEach(re=>dn(re.key));L&&L.signal.addEventListener("abort",mr);let{loaderResults:li,fetcherResults:Qt}=await Cc(S,k,ie,we,w);if(w.signal.aborted)return{shortCircuited:!0};L&&L.signal.removeEventListener("abort",mr),we.forEach(re=>O.delete(re.key));let Bt=Ko(li);if(Bt)return await zn(w,Bt.result,!0,{replace:U}),{shortCircuited:!0};if(Bt=Ko(Qt),Bt)return ue.add(Bt.key),await zn(w,Bt.result,!0,{replace:U}),{shortCircuited:!0};let{loaderData:Dl,errors:ai}=rd(S,k,li,V,we,Qt,Qe);Qe.forEach((re,Se)=>{re.subscribe(gr=>{(gr||re.done)&&Qe.delete(Se)})}),f.v7_partialHydration&&z&&S.errors&&(ai=me({},S.errors,ai));let Vn=_c(),Co=jc(W),Ro=Vn||Co||we.length>0;return me({matches:k,loaderData:Dl,errors:ai},Ro?{fetchers:new Map(S.fetchers)}:{})}function Tc(w){if(w&&!dt(w[1]))return{[w[0]]:w[1].data};if(S.actionData)return Object.keys(S.actionData).length===0?null:S.actionData}function k0(w){return w.forEach(E=>{let k=S.fetchers.get(E.key),P=xi(void 0,k?k.data:void 0);S.fetchers.set(E.key,P)}),new Map(S.fetchers)}function T0(w,E,k,P){if(r)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");dn(w);let M=(P&&P.flushSync)===!0,$=l||s,K=iu(S.location,S.matches,a,f.v7_prependBasename,k,f.v7_relativeSplatPath,E,P==null?void 0:P.relative),U=Wn($,K,a),z=ko(U,$,K);if(z.active&&z.matches&&(U=z.matches),!U){qt(w,E,nt(404,{pathname:K}),{flushSync:M});return}let{path:F,submission:V,error:G}=Jf(f.v7_normalizeFormMethod,!0,K,P);if(G){qt(w,E,G,{flushSync:M});return}let te=Ci(U,F),Ie=(P&&P.preventScrollReset)===!0;if(V&&Pt(V.formMethod)){C0(w,E,F,te,U,z.active,M,Ie,V);return}Dt.set(w,{routeId:E,path:F}),R0(w,E,F,te,U,z.active,M,Ie,V)}async function C0(w,E,k,P,M,$,K,U,z){Tl(),Dt.delete(w);function F(Re){if(!Re.route.action&&!Re.route.lazy){let yr=nt(405,{method:z.formMethod,pathname:k,routeId:E});return qt(w,E,yr,{flushSync:K}),!0}return!1}if(!$&&F(P))return;let V=S.fetchers.get(w);fn(w,n1(z,V),{flushSync:K});let G=new AbortController,te=xr(e.history,k,G.signal,z);if($){let Re=await To(M,new URL(te.url).pathname,te.signal,w);if(Re.type==="aborted")return;if(Re.type==="error"){qt(w,E,Re.error,{flushSync:K});return}else if(Re.matches){if(M=Re.matches,P=Ci(M,k),F(P))return}else{qt(w,E,nt(404,{pathname:k}),{flushSync:K});return}}O.set(w,G);let Ie=H,ie=(await si("action",S,te,[P],M,w))[P.route.id];if(te.signal.aborted){O.get(w)===G&&O.delete(w);return}if(f.v7_fetcherPersist&&qe.has(w)){if(Yn(ie)||dt(ie)){fn(w,pn(void 0));return}}else{if(Yn(ie))if(O.delete(w),W>Ie){fn(w,pn(void 0));return}else return ue.add(w),fn(w,xi(z)),zn(te,ie,!1,{fetcherSubmission:z,preventScrollReset:U});if(dt(ie)){qt(w,E,ie.error);return}}if(En(ie))throw nt(400,{type:"defer-action"});let we=S.navigation.location||S.location,mr=xr(e.history,we,G.signal),li=l||s,Qt=S.navigation.state!=="idle"?Wn(li,S.navigation.location,a):S.matches;ee(Qt,"Didn't find any matches after fetcher action");let Bt=++H;ae.set(w,Bt);let Dl=xi(z,ie.data);S.fetchers.set(w,Dl);let[ai,Vn]=Zf(e.history,S,Qt,z,we,!1,f.v7_skipActionErrorRevalidation,Rt,un,cn,qe,Dt,ue,li,a,[P.route.id,ie]);Vn.filter(Re=>Re.key!==w).forEach(Re=>{let yr=Re.key,Lc=S.fetchers.get(yr),O0=xi(void 0,Lc?Lc.data:void 0);S.fetchers.set(yr,O0),dn(yr),Re.controller&&O.set(yr,Re.controller)}),Ge({fetchers:new Map(S.fetchers)});let Co=()=>Vn.forEach(Re=>dn(Re.key));G.signal.addEventListener("abort",Co);let{loaderResults:Ro,fetcherResults:re}=await Cc(S,Qt,ai,Vn,mr);if(G.signal.aborted)return;G.signal.removeEventListener("abort",Co),ae.delete(w),O.delete(w),Vn.forEach(Re=>O.delete(Re.key));let Se=Ko(Ro);if(Se)return zn(mr,Se.result,!1,{preventScrollReset:U});if(Se=Ko(re),Se)return ue.add(Se.key),zn(mr,Se.result,!1,{preventScrollReset:U});let{loaderData:gr,errors:ui}=rd(S,Qt,Ro,void 0,Vn,re,Qe);if(S.fetchers.has(w)){let Re=pn(ie.data);S.fetchers.set(w,Re)}jc(Bt),S.navigation.state==="loading"&&Bt>W?(ee(_,"Expected pending action"),L&&L.abort(),pr(S.navigation.location,{matches:Qt,loaderData:gr,errors:ui,fetchers:new Map(S.fetchers)})):(Ge({errors:ui,loaderData:id(S.loaderData,gr,Qt,ui),fetchers:new Map(S.fetchers)}),Rt=!1)}async function R0(w,E,k,P,M,$,K,U,z){let F=S.fetchers.get(w);fn(w,xi(z,F?F.data:void 0),{flushSync:K});let V=new AbortController,G=xr(e.history,k,V.signal);if($){let ie=await To(M,new URL(G.url).pathname,G.signal,w);if(ie.type==="aborted")return;if(ie.type==="error"){qt(w,E,ie.error,{flushSync:K});return}else if(ie.matches)M=ie.matches,P=Ci(M,k);else{qt(w,E,nt(404,{pathname:k}),{flushSync:K});return}}O.set(w,V);let te=H,ce=(await si("loader",S,G,[P],M,w))[P.route.id];if(En(ce)&&(ce=await mc(ce,G.signal,!0)||ce),O.get(w)===V&&O.delete(w),!G.signal.aborted){if(qe.has(w)){fn(w,pn(void 0));return}if(Yn(ce))if(W>te){fn(w,pn(void 0));return}else{ue.add(w),await zn(G,ce,!1,{preventScrollReset:U});return}if(dt(ce)){qt(w,E,ce.error);return}ee(!En(ce),"Unhandled fetcher deferred data"),fn(w,pn(ce.data))}}async function zn(w,E,k,P){let{submission:M,fetcherSubmission:$,preventScrollReset:K,replace:U}=P===void 0?{}:P;E.response.headers.has("X-Remix-Revalidate")&&(Rt=!0);let z=E.response.headers.get("Location");ee(z,"Expected a Location header on the redirect Response"),z=td(z,new URL(w.url),a);let F=no(S.location,z,{_isRedirect:!0});if(n){let ie=!1;if(E.response.headers.has("X-Remix-Reload-Document"))ie=!0;else if(pc.test(z)){const we=e.history.createURL(z);ie=we.origin!==t.location.origin||ti(we.pathname,a)==null}if(ie){U?t.location.replace(z):t.location.assign(z);return}}L=null;let V=U===!0||E.response.headers.has("X-Remix-Replace")?De.Replace:De.Push,{formMethod:G,formAction:te,formEncType:Ie}=S.navigation;!M&&!$&&G&&te&&Ie&&(M=ad(S.navigation));let ce=M||$;if(Mv.has(E.response.status)&&ce&&Pt(ce.formMethod))await Un(V,F,{submission:me({},ce,{formAction:z}),preventScrollReset:K||B,enableViewTransition:k?J:void 0});else{let ie=ra(F,M);await Un(V,F,{overrideNavigation:ie,fetcherSubmission:$,preventScrollReset:K||B,enableViewTransition:k?J:void 0})}}async function si(w,E,k,P,M,$){let K,U={};try{K=await Hv(u,w,E,k,P,M,$,o,i)}catch(z){return P.forEach(F=>{U[F.route.id]={type:oe.error,error:z}}),U}for(let[z,F]of Object.entries(K))if(Jv(F)){let V=F.result;U[z]={type:oe.redirect,response:qv(V,k,z,M,a,f.v7_relativeSplatPath)}}else U[z]=await Wv(F);return U}async function Cc(w,E,k,P,M){let $=w.matches,K=si("loader",w,M,k,E,null),U=Promise.all(P.map(async V=>{if(V.matches&&V.match&&V.controller){let te=(await si("loader",w,xr(e.history,V.path,V.controller.signal),[V.match],V.matches,V.key))[V.match.route.id];return{[V.key]:te}}else return Promise.resolve({[V.key]:{type:oe.error,error:nt(404,{pathname:V.path})}})})),z=await K,F=(await U).reduce((V,G)=>Object.assign(V,G),{});return await Promise.all([Xv(E,z,M.signal,$,w.loaderData),e1(E,F,P)]),{loaderResults:z,fetcherResults:F}}function Tl(){Rt=!0,un.push(...Rl()),Dt.forEach((w,E)=>{O.has(E)&&cn.add(E),dn(E)})}function fn(w,E,k){k===void 0&&(k={}),S.fetchers.set(w,E),Ge({fetchers:new Map(S.fetchers)},{flushSync:(k&&k.flushSync)===!0})}function qt(w,E,k,P){P===void 0&&(P={});let M=qn(S.matches,E);Eo(w),Ge({errors:{[M.route.id]:k},fetchers:new Map(S.fetchers)},{flushSync:(P&&P.flushSync)===!0})}function Rc(w){return ct.set(w,(ct.get(w)||0)+1),qe.has(w)&&qe.delete(w),S.fetchers.get(w)||Iv}function Eo(w){let E=S.fetchers.get(w);O.has(w)&&!(E&&E.state==="loading"&&ae.has(w))&&dn(w),Dt.delete(w),ae.delete(w),ue.delete(w),f.v7_fetcherPersist&&qe.delete(w),cn.delete(w),S.fetchers.delete(w)}function D0(w){let E=(ct.get(w)||0)-1;E<=0?(ct.delete(w),qe.add(w),f.v7_fetcherPersist||Eo(w)):ct.set(w,E),Ge({fetchers:new Map(S.fetchers)})}function dn(w){let E=O.get(w);E&&(E.abort(),O.delete(w))}function Dc(w){for(let E of w){let k=Rc(E),P=pn(k.data);S.fetchers.set(E,P)}}function _c(){let w=[],E=!1;for(let k of ue){let P=S.fetchers.get(k);ee(P,"Expected fetcher: "+k),P.state==="loading"&&(ue.delete(k),w.push(k),E=!0)}return Dc(w),E}function jc(w){let E=[];for(let[k,P]of ae)if(P<w){let M=S.fetchers.get(k);ee(M,"Expected fetcher: "+k),M.state==="loading"&&(dn(k),ae.delete(k),E.push(k))}return Dc(E),E.length>0}function _0(w,E){let k=S.blockers.get(w)||vi;return vt.get(w)!==E&&vt.set(w,E),k}function bc(w){S.blockers.delete(w),vt.delete(w)}function No(w,E){let k=S.blockers.get(w)||vi;ee(k.state==="unblocked"&&E.state==="blocked"||k.state==="blocked"&&E.state==="blocked"||k.state==="blocked"&&E.state==="proceeding"||k.state==="blocked"&&E.state==="unblocked"||k.state==="proceeding"&&E.state==="unblocked","Invalid blocker state transition: "+k.state+" -> "+E.state);let P=new Map(S.blockers);P.set(w,E),Ge({blockers:P})}function Pc(w){let{currentLocation:E,nextLocation:k,historyAction:P}=w;if(vt.size===0)return;vt.size>1&&Jr(!1,"A router only supports one blocker at a time");let M=Array.from(vt.entries()),[$,K]=M[M.length-1],U=S.blockers.get($);if(!(U&&U.state==="proceeding")&&K({currentLocation:E,nextLocation:k,historyAction:P}))return $}function Cl(w){let E=nt(404,{pathname:w}),k=l||s,{matches:P,route:M}=sd(k);return Rl(),{notFoundMatches:P,route:M,error:E}}function Rl(w){let E=[];return Qe.forEach((k,P)=>{(!w||w(P))&&(k.cancel(),E.push(P),Qe.delete(P))}),E}function j0(w,E,k){if(m=w,N=E,v=k||null,!g&&S.navigation===na){g=!0;let P=Oc(S.location,S.matches);P!=null&&Ge({restoreScrollPosition:P})}return()=>{m=null,N=null,v=null}}function Ac(w,E){return v&&v(w,E.map(P=>pv(P,S.loaderData)))||w.key}function b0(w,E){if(m&&N){let k=Ac(w,E);m[k]=N()}}function Oc(w,E){if(m){let k=Ac(w,E),P=m[k];if(typeof P=="number")return P}return null}function ko(w,E,k){if(c)if(w){if(Object.keys(w[0].params).length>0)return{active:!0,matches:fs(E,k,a,!0)}}else return{active:!0,matches:fs(E,k,a,!0)||[]};return{active:!1,matches:null}}async function To(w,E,k,P){if(!c)return{type:"success",matches:w};let M=w;for(;;){let $=l==null,K=l||s,U=o;try{await c({signal:k,path:E,matches:M,fetcherKey:P,patch:(V,G)=>{k.aborted||ed(V,G,K,U,i)}})}catch(V){return{type:"error",error:V,partialMatches:M}}finally{$&&!k.aborted&&(s=[...s])}if(k.aborted)return{type:"aborted"};let z=Wn(K,E,a);if(z)return{type:"success",matches:z};let F=fs(K,E,a,!0);if(!F||M.length===F.length&&M.every((V,G)=>V.route.id===F[G].route.id))return{type:"success",matches:null};M=F}}function P0(w){o={},l=Vs(w,i,void 0,o)}function A0(w,E){let k=l==null;ed(w,E,l||s,o,i),k&&(s=[...s],Ge({}))}return T={get basename(){return a},get future(){return f},get state(){return S},get routes(){return s},get window(){return t},initialize:v0,subscribe:w0,enableScrollRestoration:j0,navigate:kc,fetch:T0,revalidate:S0,createHref:w=>e.history.createHref(w),encodeLocation:w=>e.history.encodeLocation(w),getFetcher:Rc,deleteFetcher:D0,dispose:x0,getBlocker:_0,deleteBlocker:bc,patchRoutes:A0,_internalFetchControllers:O,_internalActiveDeferreds:Qe,_internalSetRoutes:P0},T}function Uv(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function iu(e,t,n,r,i,o,s,l){let a,u;if(s){a=[];for(let f of t)if(a.push(f),f.route.id===s){u=f;break}}else a=t,u=t[t.length-1];let c=hc(i||".",dc(a,o),ti(e.pathname,n)||e.pathname,l==="path");if(i==null&&(c.search=e.search,c.hash=e.hash),(i==null||i===""||i===".")&&u){let f=gc(c.search);if(u.route.index&&!f)c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&f){let h=new URLSearchParams(c.search),y=h.getAll("index");h.delete("index"),y.filter(v=>v).forEach(v=>h.append("index",v));let m=h.toString();c.search=m?"?"+m:""}}return r&&n!=="/"&&(c.pathname=c.pathname==="/"?n:nn([n,c.pathname])),ar(c)}function Jf(e,t,n,r){if(!r||!Uv(r))return{path:n};if(r.formMethod&&!Zv(r.formMethod))return{path:n,error:nt(405,{method:r.formMethod})};let i=()=>({path:n,error:nt(400,{type:"invalid-body"})}),o=r.formMethod||"get",s=e?o.toUpperCase():o.toLowerCase(),l=mm(n);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!Pt(s))return i();let h=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((y,m)=>{let[v,N]=m;return""+y+v+"="+N+`
`},""):String(r.body);return{path:n,submission:{formMethod:s,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:h}}}else if(r.formEncType==="application/json"){if(!Pt(s))return i();try{let h=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:n,submission:{formMethod:s,formAction:l,formEncType:r.formEncType,formData:void 0,json:h,text:void 0}}}catch{return i()}}}ee(typeof FormData=="function","FormData is not available in this environment");let a,u;if(r.formData)a=su(r.formData),u=r.formData;else if(r.body instanceof FormData)a=su(r.body),u=r.body;else if(r.body instanceof URLSearchParams)a=r.body,u=nd(a);else if(r.body==null)a=new URLSearchParams,u=new FormData;else try{a=new URLSearchParams(r.body),u=nd(a)}catch{return i()}let c={formMethod:s,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:u,json:void 0,text:void 0};if(Pt(c.formMethod))return{path:n,submission:c};let f=Fn(n);return t&&f.search&&gc(f.search)&&a.append("index",""),f.search="?"+a,{path:ar(f),submission:c}}function Yf(e,t,n){n===void 0&&(n=!1);let r=e.findIndex(i=>i.route.id===t);return r>=0?e.slice(0,n?r+1:r):e}function Zf(e,t,n,r,i,o,s,l,a,u,c,f,h,y,m,v){let N=v?dt(v[1])?v[1].error:v[1].data:void 0,g=e.createURL(t.location),p=e.createURL(i),x=n;o&&t.errors?x=Yf(n,Object.keys(t.errors)[0],!0):v&&dt(v[1])&&(x=Yf(n,v[0]));let R=v?v[1].statusCode:void 0,b=s&&R&&R>=400,T=x.filter((_,B)=>{let{route:L}=_;if(L.lazy)return!0;if(L.loader==null)return!1;if(o)return ou(L,t.loaderData,t.errors);if(zv(t.loaderData,t.matches[B],_)||a.some(pe=>pe===_.route.id))return!0;let J=t.matches[B],le=_;return Xf(_,me({currentUrl:g,currentParams:J.params,nextUrl:p,nextParams:le.params},r,{actionResult:N,actionStatus:R,defaultShouldRevalidate:b?!1:l||g.pathname+g.search===p.pathname+p.search||g.search!==p.search||hm(J,le)}))}),S=[];return f.forEach((_,B)=>{if(o||!n.some(Me=>Me.route.id===_.routeId)||c.has(B))return;let L=Wn(y,_.path,m);if(!L){S.push({key:B,routeId:_.routeId,path:_.path,matches:null,match:null,controller:null});return}let J=t.fetchers.get(B),le=Ci(L,_.path),pe=!1;h.has(B)?pe=!1:u.has(B)?(u.delete(B),pe=!0):J&&J.state!=="idle"&&J.data===void 0?pe=l:pe=Xf(le,me({currentUrl:g,currentParams:t.matches[t.matches.length-1].params,nextUrl:p,nextParams:n[n.length-1].params},r,{actionResult:N,actionStatus:R,defaultShouldRevalidate:b?!1:l})),pe&&S.push({key:B,routeId:_.routeId,path:_.path,matches:L,match:le,controller:new AbortController})}),[T,S]}function ou(e,t,n){if(e.lazy)return!0;if(!e.loader)return!1;let r=t!=null&&t[e.id]!==void 0,i=n!=null&&n[e.id]!==void 0;return!r&&i?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!r&&!i}function zv(e,t,n){let r=!t||n.route.id!==t.route.id,i=e[n.route.id]===void 0;return r||i}function hm(e,t){let n=e.route.path;return e.pathname!==t.pathname||n!=null&&n.endsWith("*")&&e.params["*"]!==t.params["*"]}function Xf(e,t){if(e.route.shouldRevalidate){let n=e.route.shouldRevalidate(t);if(typeof n=="boolean")return n}return t.defaultShouldRevalidate}function ed(e,t,n,r,i){var o;let s;if(e){let u=r[e];ee(u,"No route found to patch children into: routeId = "+e),u.children||(u.children=[]),s=u.children}else s=n;let l=t.filter(u=>!s.some(c=>pm(u,c))),a=Vs(l,i,[e||"_","patch",String(((o=s)==null?void 0:o.length)||"0")],r);s.push(...a)}function pm(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((n,r)=>{var i;return(i=t.children)==null?void 0:i.some(o=>pm(n,o))}):!1}async function Vv(e,t,n){if(!e.lazy)return;let r=await e.lazy();if(!e.lazy)return;let i=n[e.id];ee(i,"No route found in manifest");let o={};for(let s in r){let a=i[s]!==void 0&&s!=="hasErrorBoundary";Jr(!a,'Route "'+i.id+'" has a static property "'+s+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+s+'" will be ignored.')),!a&&!dv.has(s)&&(o[s]=r[s])}Object.assign(i,o),Object.assign(i,me({},t(i),{lazy:void 0}))}async function $v(e){let{matches:t}=e,n=t.filter(i=>i.shouldLoad);return(await Promise.all(n.map(i=>i.resolve()))).reduce((i,o,s)=>Object.assign(i,{[n[s].route.id]:o}),{})}async function Hv(e,t,n,r,i,o,s,l,a,u){let c=o.map(y=>y.route.lazy?Vv(y.route,a,l):void 0),f=o.map((y,m)=>{let v=c[m],N=i.some(p=>p.route.id===y.route.id);return me({},y,{shouldLoad:N,resolve:async p=>(p&&r.method==="GET"&&(y.route.lazy||y.route.loader)&&(N=!0),N?Kv(t,r,y,v,p,u):Promise.resolve({type:oe.data,result:void 0}))})}),h=await e({matches:f,request:r,params:o[0].params,fetcherKey:s,context:u});try{await Promise.all(c)}catch{}return h}async function Kv(e,t,n,r,i,o){let s,l,a=u=>{let c,f=new Promise((m,v)=>c=v);l=()=>c(),t.signal.addEventListener("abort",l);let h=m=>typeof u!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+n.route.id+"]"))):u({request:t,params:n.params,context:o},...m!==void 0?[m]:[]),y=(async()=>{try{return{type:"data",result:await(i?i(v=>h(v)):h())}}catch(m){return{type:"error",result:m}}})();return Promise.race([y,f])};try{let u=n.route[e];if(r)if(u){let c,[f]=await Promise.all([a(u).catch(h=>{c=h}),r]);if(c!==void 0)throw c;s=f}else if(await r,u=n.route[e],u)s=await a(u);else if(e==="action"){let c=new URL(t.url),f=c.pathname+c.search;throw nt(405,{method:t.method,pathname:f,routeId:n.route.id})}else return{type:oe.data,result:void 0};else if(u)s=await a(u);else{let c=new URL(t.url),f=c.pathname+c.search;throw nt(404,{pathname:f})}ee(s.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+n.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(u){return{type:oe.error,result:u}}finally{l&&t.signal.removeEventListener("abort",l)}return s}async function Wv(e){let{result:t,type:n}=e;if(gm(t)){let f;try{let h=t.headers.get("Content-Type");h&&/\bapplication\/json\b/.test(h)?t.body==null?f=null:f=await t.json():f=await t.text()}catch(h){return{type:oe.error,error:h}}return n===oe.error?{type:oe.error,error:new $s(t.status,t.statusText,f),statusCode:t.status,headers:t.headers}:{type:oe.data,data:f,statusCode:t.status,headers:t.headers}}if(n===oe.error){if(ld(t)){var r,i;if(t.data instanceof Error){var o,s;return{type:oe.error,error:t.data,statusCode:(o=t.init)==null?void 0:o.status,headers:(s=t.init)!=null&&s.headers?new Headers(t.init.headers):void 0}}return{type:oe.error,error:new $s(((r=t.init)==null?void 0:r.status)||500,void 0,t.data),statusCode:ro(t)?t.status:void 0,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}}return{type:oe.error,error:t,statusCode:ro(t)?t.status:void 0}}if(Yv(t)){var l,a;return{type:oe.deferred,deferredData:t,statusCode:(l=t.init)==null?void 0:l.status,headers:((a=t.init)==null?void 0:a.headers)&&new Headers(t.init.headers)}}if(ld(t)){var u,c;return{type:oe.data,data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(c=t.init)!=null&&c.headers?new Headers(t.init.headers):void 0}}return{type:oe.data,data:t}}function qv(e,t,n,r,i,o){let s=e.headers.get("Location");if(ee(s,"Redirects returned/thrown from loaders/actions must have a Location header"),!pc.test(s)){let l=r.slice(0,r.findIndex(a=>a.route.id===n)+1);s=iu(new URL(t.url),l,i,!0,s,o),e.headers.set("Location",s)}return e}function td(e,t,n){if(pc.test(e)){let r=e,i=r.startsWith("//")?new URL(t.protocol+r):new URL(r),o=ti(i.pathname,n)!=null;if(i.origin===t.origin&&o)return i.pathname+i.search+i.hash}return e}function xr(e,t,n,r){let i=e.createURL(mm(t)).toString(),o={signal:n};if(r&&Pt(r.formMethod)){let{formMethod:s,formEncType:l}=r;o.method=s.toUpperCase(),l==="application/json"?(o.headers=new Headers({"Content-Type":l}),o.body=JSON.stringify(r.json)):l==="text/plain"?o.body=r.text:l==="application/x-www-form-urlencoded"&&r.formData?o.body=su(r.formData):o.body=r.formData}return new Request(i,o)}function su(e){let t=new URLSearchParams;for(let[n,r]of e.entries())t.append(n,typeof r=="string"?r:r.name);return t}function nd(e){let t=new FormData;for(let[n,r]of e.entries())t.append(n,r);return t}function Qv(e,t,n,r,i){let o={},s=null,l,a=!1,u={},c=n&&dt(n[1])?n[1].error:void 0;return e.forEach(f=>{if(!(f.route.id in t))return;let h=f.route.id,y=t[h];if(ee(!Yn(y),"Cannot handle redirect results in processLoaderData"),dt(y)){let m=y.error;c!==void 0&&(m=c,c=void 0),s=s||{};{let v=qn(e,h);s[v.route.id]==null&&(s[v.route.id]=m)}o[h]=void 0,a||(a=!0,l=ro(y.error)?y.error.status:500),y.headers&&(u[h]=y.headers)}else En(y)?(r.set(h,y.deferredData),o[h]=y.deferredData.data,y.statusCode!=null&&y.statusCode!==200&&!a&&(l=y.statusCode),y.headers&&(u[h]=y.headers)):(o[h]=y.data,y.statusCode&&y.statusCode!==200&&!a&&(l=y.statusCode),y.headers&&(u[h]=y.headers))}),c!==void 0&&n&&(s={[n[0]]:c},o[n[0]]=void 0),{loaderData:o,errors:s,statusCode:l||200,loaderHeaders:u}}function rd(e,t,n,r,i,o,s){let{loaderData:l,errors:a}=Qv(t,n,r,s);return i.forEach(u=>{let{key:c,match:f,controller:h}=u,y=o[c];if(ee(y,"Did not find corresponding fetcher result"),!(h&&h.signal.aborted))if(dt(y)){let m=qn(e.matches,f==null?void 0:f.route.id);a&&a[m.route.id]||(a=me({},a,{[m.route.id]:y.error})),e.fetchers.delete(c)}else if(Yn(y))ee(!1,"Unhandled fetcher revalidation redirect");else if(En(y))ee(!1,"Unhandled fetcher deferred data");else{let m=pn(y.data);e.fetchers.set(c,m)}}),{loaderData:l,errors:a}}function id(e,t,n,r){let i=me({},t);for(let o of n){let s=o.route.id;if(t.hasOwnProperty(s)?t[s]!==void 0&&(i[s]=t[s]):e[s]!==void 0&&o.route.loader&&(i[s]=e[s]),r&&r.hasOwnProperty(s))break}return i}function od(e){return e?dt(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function qn(e,t){return(t?e.slice(0,e.findIndex(r=>r.route.id===t)+1):[...e]).reverse().find(r=>r.route.hasErrorBoundary===!0)||e[0]}function sd(e){let t=e.length===1?e[0]:e.find(n=>n.index||!n.path||n.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function nt(e,t){let{pathname:n,routeId:r,method:i,type:o,message:s}=t===void 0?{}:t,l="Unknown Server Error",a="Unknown @remix-run/router error";return e===400?(l="Bad Request",i&&n&&r?a="You made a "+i+' request to "'+n+'" but '+('did not provide a `loader` for route "'+r+'", ')+"so there is no way to handle the request.":o==="defer-action"?a="defer() is not supported in actions":o==="invalid-body"&&(a="Unable to encode submission body")):e===403?(l="Forbidden",a='Route "'+r+'" does not match URL "'+n+'"'):e===404?(l="Not Found",a='No route matches URL "'+n+'"'):e===405&&(l="Method Not Allowed",i&&n&&r?a="You made a "+i.toUpperCase()+' request to "'+n+'" but '+('did not provide an `action` for route "'+r+'", ')+"so there is no way to handle the request.":i&&(a='Invalid request method "'+i.toUpperCase()+'"')),new $s(e||500,l,new Error(a),!0)}function Ko(e){let t=Object.entries(e);for(let n=t.length-1;n>=0;n--){let[r,i]=t[n];if(Yn(i))return{key:r,result:i}}}function mm(e){let t=typeof e=="string"?Fn(e):e;return ar(me({},t,{hash:""}))}function Gv(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Jv(e){return gm(e.result)&&Lv.has(e.result.status)}function En(e){return e.type===oe.deferred}function dt(e){return e.type===oe.error}function Yn(e){return(e&&e.type)===oe.redirect}function ld(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Yv(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function gm(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function Zv(e){return Ov.has(e.toLowerCase())}function Pt(e){return Pv.has(e.toLowerCase())}async function Xv(e,t,n,r,i){let o=Object.entries(t);for(let s=0;s<o.length;s++){let[l,a]=o[s],u=e.find(h=>(h==null?void 0:h.route.id)===l);if(!u)continue;let c=r.find(h=>h.route.id===u.route.id),f=c!=null&&!hm(c,u)&&(i&&i[u.route.id])!==void 0;En(a)&&f&&await mc(a,n,!1).then(h=>{h&&(t[l]=h)})}}async function e1(e,t,n){for(let r=0;r<n.length;r++){let{key:i,routeId:o,controller:s}=n[r],l=t[i];e.find(u=>(u==null?void 0:u.route.id)===o)&&En(l)&&(ee(s,"Expected an AbortController for revalidating fetcher deferred result"),await mc(l,s.signal,!0).then(u=>{u&&(t[i]=u)}))}}async function mc(e,t,n){if(n===void 0&&(n=!1),!await e.deferredData.resolveData(t)){if(n)try{return{type:oe.data,data:e.deferredData.unwrappedData}}catch(i){return{type:oe.error,error:i}}return{type:oe.data,data:e.deferredData.data}}}function gc(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Ci(e,t){let n=typeof t=="string"?Fn(t).search:t.search;if(e[e.length-1].route.index&&gc(n||""))return e[e.length-1];let r=cm(e);return r[r.length-1]}function ad(e){let{formMethod:t,formAction:n,formEncType:r,text:i,formData:o,json:s}=e;if(!(!t||!n||!r)){if(i!=null)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:void 0,text:i};if(o!=null)return{formMethod:t,formAction:n,formEncType:r,formData:o,json:void 0,text:void 0};if(s!==void 0)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:s,text:void 0}}}function ra(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function t1(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function xi(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function n1(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function pn(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function r1(e,t){try{let n=e.sessionStorage.getItem(dm);if(n){let r=JSON.parse(n);for(let[i,o]of Object.entries(r||{}))o&&Array.isArray(o)&&t.set(i,new Set(o||[]))}}catch{}}function i1(e,t){if(t.size>0){let n={};for(let[r,i]of t)n[r]=[...i];try{e.sessionStorage.setItem(dm,JSON.stringify(n))}catch(r){Jr(!1,"Failed to save applied view transitions in sessionStorage ("+r+").")}}}/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Hs(){return Hs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hs.apply(this,arguments)}const pl=D.createContext(null),ym=D.createContext(null),dr=D.createContext(null),yc=D.createContext(null),Bn=D.createContext({outlet:null,matches:[],isDataRoute:!1}),vm=D.createContext(null);function o1(e,t){let{relative:n}=t===void 0?{}:t;po()||ee(!1);let{basename:r,navigator:i}=D.useContext(dr),{hash:o,pathname:s,search:l}=wm(e,{relative:n}),a=s;return r!=="/"&&(a=s==="/"?r:nn([r,s])),i.createHref({pathname:a,search:l,hash:o})}function po(){return D.useContext(yc)!=null}function mo(){return po()||ee(!1),D.useContext(yc).location}function xm(e){D.useContext(dr).static||D.useLayoutEffect(e)}function hr(){let{isDataRoute:e}=D.useContext(Bn);return e?x1():s1()}function s1(){po()||ee(!1);let e=D.useContext(pl),{basename:t,future:n,navigator:r}=D.useContext(dr),{matches:i}=D.useContext(Bn),{pathname:o}=mo(),s=JSON.stringify(dc(i,n.v7_relativeSplatPath)),l=D.useRef(!1);return xm(()=>{l.current=!0}),D.useCallback(function(u,c){if(c===void 0&&(c={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let f=hc(u,JSON.parse(s),o,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:nn([t,f.pathname])),(c.replace?r.replace:r.push)(f,c.state,c)},[t,r,s,o,e])}const l1=D.createContext(null);function a1(e){let t=D.useContext(Bn).outlet;return t&&D.createElement(l1.Provider,{value:e},t)}function wm(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=D.useContext(dr),{matches:i}=D.useContext(Bn),{pathname:o}=mo(),s=JSON.stringify(dc(i,r.v7_relativeSplatPath));return D.useMemo(()=>hc(e,JSON.parse(s),o,n==="path"),[e,s,o,n])}function u1(e,t,n,r){po()||ee(!1);let{navigator:i}=D.useContext(dr),{matches:o}=D.useContext(Bn),s=o[o.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let u=mo(),c;c=u;let f=c.pathname||"/",h=f;if(a!=="/"){let v=a.replace(/^\//,"").split("/");h="/"+f.replace(/^\//,"").split("/").slice(v.length).join("/")}let y=Wn(e,{pathname:h});return p1(y&&y.map(v=>Object.assign({},v,{params:Object.assign({},l,v.params),pathname:nn([a,i.encodeLocation?i.encodeLocation(v.pathname).pathname:v.pathname]),pathnameBase:v.pathnameBase==="/"?a:nn([a,i.encodeLocation?i.encodeLocation(v.pathnameBase).pathname:v.pathnameBase])})),o,n,r)}function c1(){let e=v1(),t=ro(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return D.createElement(D.Fragment,null,D.createElement("h2",null,"Unexpected Application Error!"),D.createElement("h3",{style:{fontStyle:"italic"}},t),n?D.createElement("pre",{style:i},n):null,null)}const f1=D.createElement(c1,null);class d1 extends D.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?D.createElement(Bn.Provider,{value:this.props.routeContext},D.createElement(vm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function h1(e){let{routeContext:t,match:n,children:r}=e,i=D.useContext(pl);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),D.createElement(Bn.Provider,{value:t},r)}function p1(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(i=n)==null?void 0:i.errors;if(l!=null){let c=s.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);c>=0||ee(!1),s=s.slice(0,Math.min(s.length,c+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<s.length;c++){let f=s[c];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=c),f.route.id){let{loaderData:h,errors:y}=n,m=f.route.loader&&h[f.route.id]===void 0&&(!y||y[f.route.id]===void 0);if(f.route.lazy||m){a=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((c,f,h)=>{let y,m=!1,v=null,N=null;n&&(y=l&&f.route.id?l[f.route.id]:void 0,v=f.route.errorElement||f1,a&&(u<0&&h===0?(w1("route-fallback"),m=!0,N=null):u===h&&(m=!0,N=f.route.hydrateFallbackElement||null)));let g=t.concat(s.slice(0,h+1)),p=()=>{let x;return y?x=v:m?x=N:f.route.Component?x=D.createElement(f.route.Component,null):f.route.element?x=f.route.element:x=c,D.createElement(h1,{match:f,routeContext:{outlet:c,matches:g,isDataRoute:n!=null},children:x})};return n&&(f.route.ErrorBoundary||f.route.errorElement||h===0)?D.createElement(d1,{location:n.location,revalidation:n.revalidation,component:v,error:y,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var Sm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Sm||{}),Em=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Em||{});function m1(e){let t=D.useContext(pl);return t||ee(!1),t}function g1(e){let t=D.useContext(ym);return t||ee(!1),t}function y1(e){let t=D.useContext(Bn);return t||ee(!1),t}function Nm(e){let t=y1(),n=t.matches[t.matches.length-1];return n.route.id||ee(!1),n.route.id}function v1(){var e;let t=D.useContext(vm),n=g1(Em.UseRouteError),r=Nm();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function x1(){let{router:e}=m1(Sm.UseNavigateStable),t=Nm(),n=D.useRef(!1);return xm(()=>{n.current=!0}),D.useCallback(function(i,o){o===void 0&&(o={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,Hs({fromRouteId:t},o)))},[e,t])}const ud={};function w1(e,t,n){ud[e]||(ud[e]=!0)}function S1(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function E1(e){return a1(e.context)}function N1(e){let{basename:t="/",children:n=null,location:r,navigationType:i=De.Pop,navigator:o,static:s=!1,future:l}=e;po()&&ee(!1);let a=t.replace(/^\/*/,"/"),u=D.useMemo(()=>({basename:a,navigator:o,static:s,future:Hs({v7_relativeSplatPath:!1},l)}),[a,l,o,s]);typeof r=="string"&&(r=Fn(r));let{pathname:c="/",search:f="",hash:h="",state:y=null,key:m="default"}=r,v=D.useMemo(()=>{let N=ti(c,a);return N==null?null:{location:{pathname:N,search:f,hash:h,state:y,key:m},navigationType:i}},[a,c,f,h,y,m,i]);return v==null?null:D.createElement(dr.Provider,{value:u},D.createElement(yc.Provider,{children:n,value:v}))}new Promise(()=>{});function k1(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:D.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:D.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:D.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function io(){return io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},io.apply(this,arguments)}function T1(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function C1(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function R1(e,t){return e.button===0&&(!t||t==="_self")&&!C1(e)}const D1=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],_1="6";try{window.__reactRouterVersion=_1}catch{}function j1(e,t){return Bv({basename:void 0,future:io({},void 0,{v7_prependBasename:!0}),history:uv({window:void 0}),hydrationData:b1(),routes:e,mapRouteProperties:k1,dataStrategy:void 0,patchRoutesOnNavigation:void 0,window:void 0}).initialize()}function b1(){var e;let t=(e=window)==null?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=io({},t,{errors:P1(t.errors)})),t}function P1(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,i]of t)if(i&&i.__type==="RouteErrorResponse")n[r]=new $s(i.status,i.statusText,i.data,i.internal===!0);else if(i&&i.__type==="Error"){if(i.__subType){let o=window[i.__subType];if(typeof o=="function")try{let s=new o(i.message);s.stack="",n[r]=s}catch{}}if(n[r]==null){let o=new Error(i.message);o.stack="",n[r]=o}}else n[r]=i;return n}const A1=D.createContext({isTransitioning:!1}),O1=D.createContext(new Map),L1="startTransition",cd=J0[L1],M1="flushSync",fd=av[M1];function I1(e){cd?cd(e):e()}function wi(e){fd?fd(e):e()}class F1{constructor(){this.status="pending",this.promise=new Promise((t,n)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",t(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",n(r))}})}}function B1(e){let{fallbackElement:t,router:n,future:r}=e,[i,o]=D.useState(n.state),[s,l]=D.useState(),[a,u]=D.useState({isTransitioning:!1}),[c,f]=D.useState(),[h,y]=D.useState(),[m,v]=D.useState(),N=D.useRef(new Map),{v7_startTransition:g}=r||{},p=D.useCallback(_=>{g?I1(_):_()},[g]),x=D.useCallback((_,B)=>{let{deletedFetchers:L,flushSync:J,viewTransitionOpts:le}=B;_.fetchers.forEach((Me,Rt)=>{Me.data!==void 0&&N.current.set(Rt,Me.data)}),L.forEach(Me=>N.current.delete(Me));let pe=n.window==null||n.window.document==null||typeof n.window.document.startViewTransition!="function";if(!le||pe){J?wi(()=>o(_)):p(()=>o(_));return}if(J){wi(()=>{h&&(c&&c.resolve(),h.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:le.currentLocation,nextLocation:le.nextLocation})});let Me=n.window.document.startViewTransition(()=>{wi(()=>o(_))});Me.finished.finally(()=>{wi(()=>{f(void 0),y(void 0),l(void 0),u({isTransitioning:!1})})}),wi(()=>y(Me));return}h?(c&&c.resolve(),h.skipTransition(),v({state:_,currentLocation:le.currentLocation,nextLocation:le.nextLocation})):(l(_),u({isTransitioning:!0,flushSync:!1,currentLocation:le.currentLocation,nextLocation:le.nextLocation}))},[n.window,h,c,N,p]);D.useLayoutEffect(()=>n.subscribe(x),[n,x]),D.useEffect(()=>{a.isTransitioning&&!a.flushSync&&f(new F1)},[a]),D.useEffect(()=>{if(c&&s&&n.window){let _=s,B=c.promise,L=n.window.document.startViewTransition(async()=>{p(()=>o(_)),await B});L.finished.finally(()=>{f(void 0),y(void 0),l(void 0),u({isTransitioning:!1})}),y(L)}},[p,s,c,n.window]),D.useEffect(()=>{c&&s&&i.location.key===s.location.key&&c.resolve()},[c,h,i.location,s]),D.useEffect(()=>{!a.isTransitioning&&m&&(l(m.state),u({isTransitioning:!0,flushSync:!1,currentLocation:m.currentLocation,nextLocation:m.nextLocation}),v(void 0))},[a.isTransitioning,m]),D.useEffect(()=>{},[]);let R=D.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:_=>n.navigate(_),push:(_,B,L)=>n.navigate(_,{state:B,preventScrollReset:L==null?void 0:L.preventScrollReset}),replace:(_,B,L)=>n.navigate(_,{replace:!0,state:B,preventScrollReset:L==null?void 0:L.preventScrollReset})}),[n]),b=n.basename||"/",T=D.useMemo(()=>({router:n,navigator:R,static:!1,basename:b}),[n,R,b]),S=D.useMemo(()=>({v7_relativeSplatPath:n.future.v7_relativeSplatPath}),[n.future.v7_relativeSplatPath]);return D.useEffect(()=>S1(r,n.future),[r,n.future]),D.createElement(D.Fragment,null,D.createElement(pl.Provider,{value:T},D.createElement(ym.Provider,{value:i},D.createElement(O1.Provider,{value:N.current},D.createElement(A1.Provider,{value:a},D.createElement(N1,{basename:b,location:i.location,navigationType:i.historyAction,navigator:R,future:S},i.initialized||n.future.v7_partialHydration?D.createElement(U1,{routes:n.routes,future:n.future,state:i}):t))))),null)}const U1=D.memo(z1);function z1(e){let{routes:t,future:n,state:r}=e;return u1(t,void 0,r,n)}const V1=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",$1=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,km=D.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:o,replace:s,state:l,target:a,to:u,preventScrollReset:c,viewTransition:f}=t,h=T1(t,D1),{basename:y}=D.useContext(dr),m,v=!1;if(typeof u=="string"&&$1.test(u)&&(m=u,V1))try{let x=new URL(window.location.href),R=u.startsWith("//")?new URL(x.protocol+u):new URL(u),b=ti(R.pathname,y);R.origin===x.origin&&b!=null?u=b+R.search+R.hash:v=!0}catch{}let N=o1(u,{relative:i}),g=H1(u,{replace:s,state:l,target:a,preventScrollReset:c,relative:i,viewTransition:f});function p(x){r&&r(x),x.defaultPrevented||g(x)}return D.createElement("a",io({},h,{href:m||N,onClick:v||o?r:p,ref:n,target:a}))});var dd;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(dd||(dd={}));var hd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(hd||(hd={}));function H1(e,t){let{target:n,replace:r,state:i,preventScrollReset:o,relative:s,viewTransition:l}=t===void 0?{}:t,a=hr(),u=mo(),c=wm(e,{relative:s});return D.useCallback(f=>{if(R1(f,n)){f.preventDefault();let h=r!==void 0?r:ar(u)===ar(c);a(e,{replace:h,state:i,preventScrollReset:o,relative:s,viewTransition:l})}},[u,a,c,r,i,n,e,o,s,l])}/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Tm=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var W1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q1=D.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:i="",children:o,iconNode:s,...l},a)=>D.createElement("svg",{ref:a,...W1,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Tm("lucide",i),...l},[...s.map(([u,c])=>D.createElement(u,c)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=(e,t)=>{const n=D.forwardRef(({className:r,...i},o)=>D.createElement(q1,{ref:o,iconNode:t,className:Tm(`lucide-${K1(e)}`,r),...i}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ks=ke("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oo=ke("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q1=ke("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cm=ke("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const so=ke("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G1=ke("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rm=ke("Files",[["path",{d:"M20 7h-3a2 2 0 0 1-2-2V2",key:"x099mo"}],["path",{d:"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z",key:"18t6ie"}],["path",{d:"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8",key:"1nja0z"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J1=ke("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y1=ke("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z1=ke("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X1=ke("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=ke("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=ke("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=ke("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const go=ke("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ml=ke("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nx=ke("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rx=ke("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=ke("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dm=ke("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qs=ke("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qs=ke("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function _m(e,t){return function(){return e.apply(t,arguments)}}const{toString:ox}=Object.prototype,{getPrototypeOf:vc}=Object,{iterator:gl,toStringTag:jm}=Symbol,yl=(e=>t=>{const n=ox.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Ft=e=>(e=e.toLowerCase(),t=>yl(t)===e),vl=e=>t=>typeof t===e,{isArray:ni}=Array,lo=vl("undefined");function yo(e){return e!==null&&!lo(e)&&e.constructor!==null&&!lo(e.constructor)&&at(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const bm=Ft("ArrayBuffer");function sx(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&bm(e.buffer),t}const lx=vl("string"),at=vl("function"),Pm=vl("number"),vo=e=>e!==null&&typeof e=="object",ax=e=>e===!0||e===!1,ds=e=>{if(yl(e)!=="object")return!1;const t=vc(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(jm in e)&&!(gl in e)},ux=e=>{if(!vo(e)||yo(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},cx=Ft("Date"),fx=Ft("File"),dx=Ft("Blob"),hx=Ft("FileList"),px=e=>vo(e)&&at(e.pipe),mx=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||at(e.append)&&((t=yl(e))==="formdata"||t==="object"&&at(e.toString)&&e.toString()==="[object FormData]"))},gx=Ft("URLSearchParams"),[yx,vx,xx,wx]=["ReadableStream","Request","Response","Headers"].map(Ft),Sx=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function xo(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),ni(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{if(yo(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let l;for(r=0;r<s;r++)l=o[r],t.call(null,e[l],l,e)}}function Am(e,t){if(yo(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Zn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Om=e=>!lo(e)&&e!==Zn;function lu(){const{caseless:e}=Om(this)&&this||{},t={},n=(r,i)=>{const o=e&&Am(t,i)||i;ds(t[o])&&ds(r)?t[o]=lu(t[o],r):ds(r)?t[o]=lu({},r):ni(r)?t[o]=r.slice():t[o]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&xo(arguments[r],n);return t}const Ex=(e,t,n,{allOwnKeys:r}={})=>(xo(t,(i,o)=>{n&&at(i)?e[o]=_m(i,n):e[o]=i},{allOwnKeys:r}),e),Nx=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),kx=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Tx=(e,t,n,r)=>{let i,o,s;const l={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],(!r||r(s,e,t))&&!l[s]&&(t[s]=e[s],l[s]=!0);e=n!==!1&&vc(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Cx=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Rx=e=>{if(!e)return null;if(ni(e))return e;let t=e.length;if(!Pm(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Dx=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&vc(Uint8Array)),_x=(e,t)=>{const r=(e&&e[gl]).call(e);let i;for(;(i=r.next())&&!i.done;){const o=i.value;t.call(e,o[0],o[1])}},jx=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},bx=Ft("HTMLFormElement"),Px=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),pd=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Ax=Ft("RegExp"),Lm=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};xo(n,(i,o)=>{let s;(s=t(i,o,e))!==!1&&(r[o]=s||i)}),Object.defineProperties(e,r)},Ox=e=>{Lm(e,(t,n)=>{if(at(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(at(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Lx=(e,t)=>{const n={},r=i=>{i.forEach(o=>{n[o]=!0})};return ni(e)?r(e):r(String(e).split(t)),n},Mx=()=>{},Ix=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Fx(e){return!!(e&&at(e.append)&&e[jm]==="FormData"&&e[gl])}const Bx=e=>{const t=new Array(10),n=(r,i)=>{if(vo(r)){if(t.indexOf(r)>=0)return;if(yo(r))return r;if(!("toJSON"in r)){t[i]=r;const o=ni(r)?[]:{};return xo(r,(s,l)=>{const a=n(s,i+1);!lo(a)&&(o[l]=a)}),t[i]=void 0,o}}return r};return n(e,0)},Ux=Ft("AsyncFunction"),zx=e=>e&&(vo(e)||at(e))&&at(e.then)&&at(e.catch),Mm=((e,t)=>e?setImmediate:t?((n,r)=>(Zn.addEventListener("message",({source:i,data:o})=>{i===Zn&&o===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Zn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",at(Zn.postMessage)),Vx=typeof queueMicrotask<"u"?queueMicrotask.bind(Zn):typeof process<"u"&&process.nextTick||Mm,$x=e=>e!=null&&at(e[gl]),C={isArray:ni,isArrayBuffer:bm,isBuffer:yo,isFormData:mx,isArrayBufferView:sx,isString:lx,isNumber:Pm,isBoolean:ax,isObject:vo,isPlainObject:ds,isEmptyObject:ux,isReadableStream:yx,isRequest:vx,isResponse:xx,isHeaders:wx,isUndefined:lo,isDate:cx,isFile:fx,isBlob:dx,isRegExp:Ax,isFunction:at,isStream:px,isURLSearchParams:gx,isTypedArray:Dx,isFileList:hx,forEach:xo,merge:lu,extend:Ex,trim:Sx,stripBOM:Nx,inherits:kx,toFlatObject:Tx,kindOf:yl,kindOfTest:Ft,endsWith:Cx,toArray:Rx,forEachEntry:_x,matchAll:jx,isHTMLForm:bx,hasOwnProperty:pd,hasOwnProp:pd,reduceDescriptors:Lm,freezeMethods:Ox,toObjectSet:Lx,toCamelCase:Px,noop:Mx,toFiniteNumber:Ix,findKey:Am,global:Zn,isContextDefined:Om,isSpecCompliantForm:Fx,toJSONObject:Bx,isAsyncFn:Ux,isThenable:zx,setImmediate:Mm,asap:Vx,isIterable:$x};function Q(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}C.inherits(Q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.status}}});const Im=Q.prototype,Fm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Fm[e]={value:e}});Object.defineProperties(Q,Fm);Object.defineProperty(Im,"isAxiosError",{value:!0});Q.from=(e,t,n,r,i,o)=>{const s=Object.create(Im);return C.toFlatObject(e,s,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),Q.call(s,e.message,t,n,r,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};const Hx=null;function au(e){return C.isPlainObject(e)||C.isArray(e)}function Bm(e){return C.endsWith(e,"[]")?e.slice(0,-2):e}function md(e,t,n){return e?e.concat(t).map(function(i,o){return i=Bm(i),!n&&o?"["+i+"]":i}).join(n?".":""):t}function Kx(e){return C.isArray(e)&&!e.some(au)}const Wx=C.toFlatObject(C,{},null,function(t){return/^is[A-Z]/.test(t)});function xl(e,t,n){if(!C.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=C.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,N){return!C.isUndefined(N[v])});const r=n.metaTokens,i=n.visitor||c,o=n.dots,s=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&C.isSpecCompliantForm(t);if(!C.isFunction(i))throw new TypeError("visitor must be a function");function u(m){if(m===null)return"";if(C.isDate(m))return m.toISOString();if(C.isBoolean(m))return m.toString();if(!a&&C.isBlob(m))throw new Q("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(m)||C.isTypedArray(m)?a&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function c(m,v,N){let g=m;if(m&&!N&&typeof m=="object"){if(C.endsWith(v,"{}"))v=r?v:v.slice(0,-2),m=JSON.stringify(m);else if(C.isArray(m)&&Kx(m)||(C.isFileList(m)||C.endsWith(v,"[]"))&&(g=C.toArray(m)))return v=Bm(v),g.forEach(function(x,R){!(C.isUndefined(x)||x===null)&&t.append(s===!0?md([v],R,o):s===null?v:v+"[]",u(x))}),!1}return au(m)?!0:(t.append(md(N,v,o),u(m)),!1)}const f=[],h=Object.assign(Wx,{defaultVisitor:c,convertValue:u,isVisitable:au});function y(m,v){if(!C.isUndefined(m)){if(f.indexOf(m)!==-1)throw Error("Circular reference detected in "+v.join("."));f.push(m),C.forEach(m,function(g,p){(!(C.isUndefined(g)||g===null)&&i.call(t,g,C.isString(p)?p.trim():p,v,h))===!0&&y(g,v?v.concat(p):[p])}),f.pop()}}if(!C.isObject(e))throw new TypeError("data must be an object");return y(e),t}function gd(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function xc(e,t){this._pairs=[],e&&xl(e,this,t)}const Um=xc.prototype;Um.append=function(t,n){this._pairs.push([t,n])};Um.toString=function(t){const n=t?function(r){return t.call(this,r,gd)}:gd;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function qx(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function zm(e,t,n){if(!t)return e;const r=n&&n.encode||qx;C.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(i?o=i(t,n):o=C.isURLSearchParams(t)?t.toString():new xc(t,n).toString(r),o){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class yd{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){C.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Vm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Qx=typeof URLSearchParams<"u"?URLSearchParams:xc,Gx=typeof FormData<"u"?FormData:null,Jx=typeof Blob<"u"?Blob:null,Yx={isBrowser:!0,classes:{URLSearchParams:Qx,FormData:Gx,Blob:Jx},protocols:["http","https","file","blob","url","data"]},wc=typeof window<"u"&&typeof document<"u",uu=typeof navigator=="object"&&navigator||void 0,Zx=wc&&(!uu||["ReactNative","NativeScript","NS"].indexOf(uu.product)<0),Xx=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ew=wc&&window.location.href||"http://localhost",tw=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:wc,hasStandardBrowserEnv:Zx,hasStandardBrowserWebWorkerEnv:Xx,navigator:uu,origin:ew},Symbol.toStringTag,{value:"Module"})),Ke={...tw,...Yx};function nw(e,t){return xl(e,new Ke.classes.URLSearchParams,{visitor:function(n,r,i,o){return Ke.isNode&&C.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function rw(e){return C.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function iw(e){const t={},n=Object.keys(e);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],t[o]=e[o];return t}function $m(e){function t(n,r,i,o){let s=n[o++];if(s==="__proto__")return!0;const l=Number.isFinite(+s),a=o>=n.length;return s=!s&&C.isArray(i)?i.length:s,a?(C.hasOwnProp(i,s)?i[s]=[i[s],r]:i[s]=r,!l):((!i[s]||!C.isObject(i[s]))&&(i[s]=[]),t(n,r,i[s],o)&&C.isArray(i[s])&&(i[s]=iw(i[s])),!l)}if(C.isFormData(e)&&C.isFunction(e.entries)){const n={};return C.forEachEntry(e,(r,i)=>{t(rw(r),i,n,0)}),n}return null}function ow(e,t,n){if(C.isString(e))try{return(t||JSON.parse)(e),C.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const wo={transitional:Vm,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,o=C.isObject(t);if(o&&C.isHTMLForm(t)&&(t=new FormData(t)),C.isFormData(t))return i?JSON.stringify($m(t)):t;if(C.isArrayBuffer(t)||C.isBuffer(t)||C.isStream(t)||C.isFile(t)||C.isBlob(t)||C.isReadableStream(t))return t;if(C.isArrayBufferView(t))return t.buffer;if(C.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return nw(t,this.formSerializer).toString();if((l=C.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return xl(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||i?(n.setContentType("application/json",!1),ow(t)):t}],transformResponse:[function(t){const n=this.transitional||wo.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(C.isResponse(t)||C.isReadableStream(t))return t;if(t&&C.isString(t)&&(r&&!this.responseType||i)){const s=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(l){if(s)throw l.name==="SyntaxError"?Q.from(l,Q.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ke.classes.FormData,Blob:Ke.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};C.forEach(["delete","get","head","post","put","patch"],e=>{wo.headers[e]={}});const sw=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),lw=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(s){i=s.indexOf(":"),n=s.substring(0,i).trim().toLowerCase(),r=s.substring(i+1).trim(),!(!n||t[n]&&sw[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},vd=Symbol("internals");function Si(e){return e&&String(e).trim().toLowerCase()}function hs(e){return e===!1||e==null?e:C.isArray(e)?e.map(hs):String(e)}function aw(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const uw=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ia(e,t,n,r,i){if(C.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!C.isString(t)){if(C.isString(r))return t.indexOf(r)!==-1;if(C.isRegExp(r))return r.test(t)}}function cw(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function fw(e,t){const n=C.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,o,s){return this[r].call(this,t,i,o,s)},configurable:!0})})}let ut=class{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function o(l,a,u){const c=Si(a);if(!c)throw new Error("header name must be a non-empty string");const f=C.findKey(i,c);(!f||i[f]===void 0||u===!0||u===void 0&&i[f]!==!1)&&(i[f||a]=hs(l))}const s=(l,a)=>C.forEach(l,(u,c)=>o(u,c,a));if(C.isPlainObject(t)||t instanceof this.constructor)s(t,n);else if(C.isString(t)&&(t=t.trim())&&!uw(t))s(lw(t),n);else if(C.isObject(t)&&C.isIterable(t)){let l={},a,u;for(const c of t){if(!C.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?C.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}s(l,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=Si(t),t){const r=C.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return aw(i);if(C.isFunction(n))return n.call(this,i,r);if(C.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Si(t),t){const r=C.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ia(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function o(s){if(s=Si(s),s){const l=C.findKey(r,s);l&&(!n||ia(r,r[l],l,n))&&(delete r[l],i=!0)}}return C.isArray(t)?t.forEach(o):o(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const o=n[r];(!t||ia(this,this[o],o,t,!0))&&(delete this[o],i=!0)}return i}normalize(t){const n=this,r={};return C.forEach(this,(i,o)=>{const s=C.findKey(r,o);if(s){n[s]=hs(i),delete n[o];return}const l=t?cw(o):String(o).trim();l!==o&&delete n[o],n[l]=hs(i),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return C.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&C.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[vd]=this[vd]={accessors:{}}).accessors,i=this.prototype;function o(s){const l=Si(s);r[l]||(fw(i,s),r[l]=!0)}return C.isArray(t)?t.forEach(o):o(t),this}};ut.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);C.reduceDescriptors(ut.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});C.freezeMethods(ut);function oa(e,t){const n=this||wo,r=t||n,i=ut.from(r.headers);let o=r.data;return C.forEach(e,function(l){o=l.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function Hm(e){return!!(e&&e.__CANCEL__)}function ri(e,t,n){Q.call(this,e??"canceled",Q.ERR_CANCELED,t,n),this.name="CanceledError"}C.inherits(ri,Q,{__CANCEL__:!0});function Km(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Q("Request failed with status code "+n.status,[Q.ERR_BAD_REQUEST,Q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function dw(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function hw(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,o=0,s;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=r[o];s||(s=u),n[i]=a,r[i]=u;let f=o,h=0;for(;f!==i;)h+=n[f++],f=f%e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),u-s<t)return;const y=c&&u-c;return y?Math.round(h*1e3/y):void 0}}function pw(e,t){let n=0,r=1e3/t,i,o;const s=(u,c=Date.now())=>{n=c,i=null,o&&(clearTimeout(o),o=null),e(...u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=r?s(u,c):(i=u,o||(o=setTimeout(()=>{o=null,s(i)},r-f)))},()=>i&&s(i)]}const Gs=(e,t,n=3)=>{let r=0;const i=hw(50,250);return pw(o=>{const s=o.loaded,l=o.lengthComputable?o.total:void 0,a=s-r,u=i(a),c=s<=l;r=s;const f={loaded:s,total:l,progress:l?s/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-s)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},xd=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},wd=e=>(...t)=>C.asap(()=>e(...t)),mw=Ke.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ke.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ke.origin),Ke.navigator&&/(msie|trident)/i.test(Ke.navigator.userAgent)):()=>!0,gw=Ke.hasStandardBrowserEnv?{write(e,t,n,r,i,o){const s=[e+"="+encodeURIComponent(t)];C.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),C.isString(r)&&s.push("path="+r),C.isString(i)&&s.push("domain="+i),o===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function yw(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function vw(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Wm(e,t,n){let r=!yw(t);return e&&(r||n==!1)?vw(e,t):t}const Sd=e=>e instanceof ut?{...e}:e;function ur(e,t){t=t||{};const n={};function r(u,c,f,h){return C.isPlainObject(u)&&C.isPlainObject(c)?C.merge.call({caseless:h},u,c):C.isPlainObject(c)?C.merge({},c):C.isArray(c)?c.slice():c}function i(u,c,f,h){if(C.isUndefined(c)){if(!C.isUndefined(u))return r(void 0,u,f,h)}else return r(u,c,f,h)}function o(u,c){if(!C.isUndefined(c))return r(void 0,c)}function s(u,c){if(C.isUndefined(c)){if(!C.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function l(u,c,f){if(f in t)return r(u,c);if(f in e)return r(void 0,u)}const a={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:l,headers:(u,c,f)=>i(Sd(u),Sd(c),f,!0)};return C.forEach(Object.keys({...e,...t}),function(c){const f=a[c]||i,h=f(e[c],t[c],c);C.isUndefined(h)&&f!==l||(n[c]=h)}),n}const qm=e=>{const t=ur({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:o,headers:s,auth:l}=t;t.headers=s=ut.from(s),t.url=zm(Wm(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(C.isFormData(n)){if(Ke.hasStandardBrowserEnv||Ke.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((a=s.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];s.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ke.hasStandardBrowserEnv&&(r&&C.isFunction(r)&&(r=r(t)),r||r!==!1&&mw(t.url))){const u=i&&o&&gw.read(o);u&&s.set(i,u)}return t},xw=typeof XMLHttpRequest<"u",ww=xw&&function(e){return new Promise(function(n,r){const i=qm(e);let o=i.data;const s=ut.from(i.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=i,c,f,h,y,m;function v(){y&&y(),m&&m(),i.cancelToken&&i.cancelToken.unsubscribe(c),i.signal&&i.signal.removeEventListener("abort",c)}let N=new XMLHttpRequest;N.open(i.method.toUpperCase(),i.url,!0),N.timeout=i.timeout;function g(){if(!N)return;const x=ut.from("getAllResponseHeaders"in N&&N.getAllResponseHeaders()),b={data:!l||l==="text"||l==="json"?N.responseText:N.response,status:N.status,statusText:N.statusText,headers:x,config:e,request:N};Km(function(S){n(S),v()},function(S){r(S),v()},b),N=null}"onloadend"in N?N.onloadend=g:N.onreadystatechange=function(){!N||N.readyState!==4||N.status===0&&!(N.responseURL&&N.responseURL.indexOf("file:")===0)||setTimeout(g)},N.onabort=function(){N&&(r(new Q("Request aborted",Q.ECONNABORTED,e,N)),N=null)},N.onerror=function(){r(new Q("Network Error",Q.ERR_NETWORK,e,N)),N=null},N.ontimeout=function(){let R=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const b=i.transitional||Vm;i.timeoutErrorMessage&&(R=i.timeoutErrorMessage),r(new Q(R,b.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,N)),N=null},o===void 0&&s.setContentType(null),"setRequestHeader"in N&&C.forEach(s.toJSON(),function(R,b){N.setRequestHeader(b,R)}),C.isUndefined(i.withCredentials)||(N.withCredentials=!!i.withCredentials),l&&l!=="json"&&(N.responseType=i.responseType),u&&([h,m]=Gs(u,!0),N.addEventListener("progress",h)),a&&N.upload&&([f,y]=Gs(a),N.upload.addEventListener("progress",f),N.upload.addEventListener("loadend",y)),(i.cancelToken||i.signal)&&(c=x=>{N&&(r(!x||x.type?new ri(null,e,N):x),N.abort(),N=null)},i.cancelToken&&i.cancelToken.subscribe(c),i.signal&&(i.signal.aborted?c():i.signal.addEventListener("abort",c)));const p=dw(i.url);if(p&&Ke.protocols.indexOf(p)===-1){r(new Q("Unsupported protocol "+p+":",Q.ERR_BAD_REQUEST,e));return}N.send(o||null)})},Sw=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const o=function(u){if(!i){i=!0,l();const c=u instanceof Error?u:this.reason;r.abort(c instanceof Q?c:new ri(c instanceof Error?c.message:c))}};let s=t&&setTimeout(()=>{s=null,o(new Q(`timeout ${t} of ms exceeded`,Q.ETIMEDOUT))},t);const l=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=r;return a.unsubscribe=()=>C.asap(l),a}},Ew=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},Nw=async function*(e,t){for await(const n of kw(e))yield*Ew(n,t)},kw=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Ed=(e,t,n,r)=>{const i=Nw(e,t);let o=0,s,l=a=>{s||(s=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await i.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let h=o+=f;n(h)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),i.return()}},{highWaterMark:2})},wl=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qm=wl&&typeof ReadableStream=="function",Tw=wl&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Gm=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Cw=Qm&&Gm(()=>{let e=!1;const t=new Request(Ke.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Nd=64*1024,cu=Qm&&Gm(()=>C.isReadableStream(new Response("").body)),Js={stream:cu&&(e=>e.body)};wl&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Js[t]&&(Js[t]=C.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new Q(`Response type '${t}' is not supported`,Q.ERR_NOT_SUPPORT,r)})})})(new Response);const Rw=async e=>{if(e==null)return 0;if(C.isBlob(e))return e.size;if(C.isSpecCompliantForm(e))return(await new Request(Ke.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(C.isArrayBufferView(e)||C.isArrayBuffer(e))return e.byteLength;if(C.isURLSearchParams(e)&&(e=e+""),C.isString(e))return(await Tw(e)).byteLength},Dw=async(e,t)=>{const n=C.toFiniteNumber(e.getContentLength());return n??Rw(t)},_w=wl&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:o,timeout:s,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:h}=qm(e);u=u?(u+"").toLowerCase():"text";let y=Sw([i,o&&o.toAbortSignal()],s),m;const v=y&&y.unsubscribe&&(()=>{y.unsubscribe()});let N;try{if(a&&Cw&&n!=="get"&&n!=="head"&&(N=await Dw(c,r))!==0){let b=new Request(t,{method:"POST",body:r,duplex:"half"}),T;if(C.isFormData(r)&&(T=b.headers.get("content-type"))&&c.setContentType(T),b.body){const[S,_]=xd(N,Gs(wd(a)));r=Ed(b.body,Nd,S,_)}}C.isString(f)||(f=f?"include":"omit");const g="credentials"in Request.prototype;m=new Request(t,{...h,signal:y,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:g?f:void 0});let p=await fetch(m,h);const x=cu&&(u==="stream"||u==="response");if(cu&&(l||x&&v)){const b={};["status","statusText","headers"].forEach(B=>{b[B]=p[B]});const T=C.toFiniteNumber(p.headers.get("content-length")),[S,_]=l&&xd(T,Gs(wd(l),!0))||[];p=new Response(Ed(p.body,Nd,S,()=>{_&&_(),v&&v()}),b)}u=u||"text";let R=await Js[C.findKey(Js,u)||"text"](p,e);return!x&&v&&v(),await new Promise((b,T)=>{Km(b,T,{data:R,headers:ut.from(p.headers),status:p.status,statusText:p.statusText,config:e,request:m})})}catch(g){throw v&&v(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new Q("Network Error",Q.ERR_NETWORK,e,m),{cause:g.cause||g}):Q.from(g,g&&g.code,e,m)}}),fu={http:Hx,xhr:ww,fetch:_w};C.forEach(fu,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const kd=e=>`- ${e}`,jw=e=>C.isFunction(e)||e===null||e===!1,Jm={getAdapter:e=>{e=C.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let o=0;o<t;o++){n=e[o];let s;if(r=n,!jw(n)&&(r=fu[(s=String(n)).toLowerCase()],r===void 0))throw new Q(`Unknown adapter '${s}'`);if(r)break;i[s||"#"+o]=r}if(!r){const o=Object.entries(i).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let s=t?o.length>1?`since :
`+o.map(kd).join(`
`):" "+kd(o[0]):"as no adapter specified";throw new Q("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return r},adapters:fu};function sa(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ri(null,e)}function Td(e){return sa(e),e.headers=ut.from(e.headers),e.data=oa.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Jm.getAdapter(e.adapter||wo.adapter)(e).then(function(r){return sa(e),r.data=oa.call(e,e.transformResponse,r),r.headers=ut.from(r.headers),r},function(r){return Hm(r)||(sa(e),r&&r.response&&(r.response.data=oa.call(e,e.transformResponse,r.response),r.response.headers=ut.from(r.response.headers))),Promise.reject(r)})}const Ym="1.11.0",Sl={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Sl[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Cd={};Sl.transitional=function(t,n,r){function i(o,s){return"[Axios v"+Ym+"] Transitional option '"+o+"'"+s+(r?". "+r:"")}return(o,s,l)=>{if(t===!1)throw new Q(i(s," has been removed"+(n?" in "+n:"")),Q.ERR_DEPRECATED);return n&&!Cd[s]&&(Cd[s]=!0,console.warn(i(s," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,s,l):!0}};Sl.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function bw(e,t,n){if(typeof e!="object")throw new Q("options must be an object",Q.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const o=r[i],s=t[o];if(s){const l=e[o],a=l===void 0||s(l,o,e);if(a!==!0)throw new Q("option "+o+" must be "+a,Q.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Q("Unknown option "+o,Q.ERR_BAD_OPTION)}}const ps={assertOptions:bw,validators:Sl},zt=ps.validators;let tr=class{constructor(t){this.defaults=t||{},this.interceptors={request:new yd,response:new yd}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const o=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=ur(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:o}=n;r!==void 0&&ps.assertOptions(r,{silentJSONParsing:zt.transitional(zt.boolean),forcedJSONParsing:zt.transitional(zt.boolean),clarifyTimeoutError:zt.transitional(zt.boolean)},!1),i!=null&&(C.isFunction(i)?n.paramsSerializer={serialize:i}:ps.assertOptions(i,{encode:zt.function,serialize:zt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ps.assertOptions(n,{baseUrl:zt.spelling("baseURL"),withXsrfToken:zt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let s=o&&C.merge(o.common,o[n.method]);o&&C.forEach(["delete","get","head","post","put","patch","common"],m=>{delete o[m]}),n.headers=ut.concat(s,o);const l=[];let a=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(a=a&&v.synchronous,l.unshift(v.fulfilled,v.rejected))});const u=[];this.interceptors.response.forEach(function(v){u.push(v.fulfilled,v.rejected)});let c,f=0,h;if(!a){const m=[Td.bind(this),void 0];for(m.unshift(...l),m.push(...u),h=m.length,c=Promise.resolve(n);f<h;)c=c.then(m[f++],m[f++]);return c}h=l.length;let y=n;for(f=0;f<h;){const m=l[f++],v=l[f++];try{y=m(y)}catch(N){v.call(this,N);break}}try{c=Td.call(this,y)}catch(m){return Promise.reject(m)}for(f=0,h=u.length;f<h;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=ur(this.defaults,t);const n=Wm(t.baseURL,t.url,t.allowAbsoluteUrls);return zm(n,t.params,t.paramsSerializer)}};C.forEach(["delete","get","head","options"],function(t){tr.prototype[t]=function(n,r){return this.request(ur(r||{},{method:t,url:n,data:(r||{}).data}))}});C.forEach(["post","put","patch"],function(t){function n(r){return function(o,s,l){return this.request(ur(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:s}))}}tr.prototype[t]=n(),tr.prototype[t+"Form"]=n(!0)});let Pw=class Zm{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(i=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](i);r._listeners=null}),this.promise.then=i=>{let o;const s=new Promise(l=>{r.subscribe(l),o=l}).then(i);return s.cancel=function(){r.unsubscribe(o)},s},t(function(o,s,l){r.reason||(r.reason=new ri(o,s,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Zm(function(i){t=i}),cancel:t}}};function Aw(e){return function(n){return e.apply(null,n)}}function Ow(e){return C.isObject(e)&&e.isAxiosError===!0}const du={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(du).forEach(([e,t])=>{du[t]=e});function Xm(e){const t=new tr(e),n=_m(tr.prototype.request,t);return C.extend(n,tr.prototype,t,{allOwnKeys:!0}),C.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return Xm(ur(e,i))},n}const Ce=Xm(wo);Ce.Axios=tr;Ce.CanceledError=ri;Ce.CancelToken=Pw;Ce.isCancel=Hm;Ce.VERSION=Ym;Ce.toFormData=xl;Ce.AxiosError=Q;Ce.Cancel=Ce.CanceledError;Ce.all=function(t){return Promise.all(t)};Ce.spread=Aw;Ce.isAxiosError=Ow;Ce.mergeConfig=ur;Ce.AxiosHeaders=ut;Ce.formToJSON=e=>$m(C.isHTMLForm(e)?new FormData(e):e);Ce.getAdapter=Jm.getAdapter;Ce.HttpStatusCode=du;Ce.default=Ce;const{Axios:ZS,AxiosError:XS,CanceledError:e2,isCancel:t2,CancelToken:n2,VERSION:r2,all:i2,Cancel:o2,isAxiosError:s2,spread:l2,toFormData:a2,AxiosHeaders:u2,HttpStatusCode:c2,formToJSON:f2,getAdapter:d2,mergeConfig:h2}=Ce,Gt={TOKEN:"token",AUTHORIZATION:"Authorization",USER_INFO:"userInfo"},St={getToken:()=>localStorage.getItem(Gt.TOKEN),setToken:e=>{localStorage.setItem(Gt.TOKEN,e)},getAuthorization:()=>localStorage.getItem(Gt.AUTHORIZATION),setAuthorization:e=>{localStorage.setItem(Gt.AUTHORIZATION,e)},getUserInfo:()=>{const e=localStorage.getItem(Gt.USER_INFO);return e?JSON.parse(e):null},setUserInfo:e=>{localStorage.setItem(Gt.USER_INFO,JSON.stringify(e))},setAuthData:e=>{e.token&&St.setToken(e.token),e.authorization&&St.setAuthorization(e.authorization),e.userInfo&&St.setUserInfo(e.userInfo)},clearAuth:()=>{localStorage.removeItem(Gt.TOKEN),localStorage.removeItem(Gt.AUTHORIZATION),localStorage.removeItem(Gt.USER_INFO)},isAuthenticated:()=>!!(St.getToken()||St.getAuthorization())},El=Ce.create({timeout:3e5,headers:{"Content-Type":"application/json"}});El.interceptors.request.use(e=>{const t=St.getAuthorization(),n=St.getToken();return e.headers.skipToken||(t?e.headers.Authorization=t:n&&(e.headers.Authorization=`Bearer ${n}`)),e},e=>Promise.reject(e));El.interceptors.response.use(e=>e,e=>{var t,n,r;return((t=e.response)==null?void 0:t.status)===401?(St.clearAuth(),console.warn("Unauthorized access - auth data cleared")):((n=e.response)==null?void 0:n.status)===403?console.warn("Forbidden access"):(r=e.response)!=null&&r.status&&e.response.status>=500?console.error("Server error:",e.response.status):(e.code==="NETWORK_ERROR"||e.message==="Network Error")&&console.error("Network error - check if backend is running"),Promise.reject(e)});const Nl=async(e,t,n,r)=>{try{return(await El({method:e,url:t,data:n,...r})).data}catch(i){throw console.error(`API call failed: ${e} ${t}`,i),i}},nr=(e,t)=>Nl("GET",e,void 0,t),ms=(e,t,n)=>Nl("POST",e,t,n),Lw=(e,t,n)=>Nl("PUT",e,t,n),Rd=(e,t)=>Nl("DELETE",e,void 0,t);let Y="/v1";const Ze={login:`${Y}/user/login`,logout:`${Y}/user/logout`,register:`${Y}/user/register`,setting:`${Y}/user/setting`,user_info:`${Y}/user/info`,tenant_info:`${Y}/user/tenant_info`,set_tenant_info:`${Y}/user/set_tenant_info`,login_channels:`${Y}/user/login/channels`,login_channel:e=>`${Y}/user/login/${e}`,dataset:`${Y}/dataset`,dataset_list:`${Y}/dataset/list`,dataset_detail:e=>`${Y}/dataset/${e}`,dataset_rename:e=>`${Y}/dataset/${e}`,dataset_delete:e=>`${Y}/dataset/${e}`,dataset_upload:e=>`${Y}/dataset/${e}/document`,dataset_document_list:e=>`${Y}/dataset/${e}/document/list`,dataset_document_delete:(e,t)=>`${Y}/dataset/${e}/document/${t}`,dataset_document_rename:(e,t)=>`${Y}/dataset/${e}/document/${t}`,dataset_document_run:(e,t)=>`${Y}/dataset/${e}/document/${t}/run`,dataset_document_stop:(e,t)=>`${Y}/dataset/${e}/document/${t}/stop`,dataset_document_chunk_list:(e,t)=>`${Y}/dataset/${e}/document/${t}/chunk`,dataset_document_chunk_create:(e,t)=>`${Y}/dataset/${e}/document/${t}/chunk`,dataset_document_chunk_delete:(e,t,n)=>`${Y}/dataset/${e}/document/${t}/chunk/${n}`,dataset_document_chunk_edit:(e,t,n)=>`${Y}/dataset/${e}/document/${t}/chunk/${n}`,dataset_retrieval_test:e=>`${Y}/dataset/${e}/retrieval`,chat:`${Y}/chat`,chat_list:`${Y}/chat/list`,chat_detail:e=>`${Y}/chat/${e}`,chat_delete:e=>`${Y}/chat/${e}`,chat_rename:e=>`${Y}/chat/${e}`,chat_conversation_list:e=>`${Y}/chat/${e}/conversation`,chat_conversation_create:e=>`${Y}/chat/${e}/conversation`,chat_conversation_delete:(e,t)=>`${Y}/chat/${e}/conversation/${t}`,chat_conversation_completion:(e,t)=>`${Y}/chat/${e}/conversation/${t}/completion`,file_upload:`${Y}/file/upload`,file_list:`${Y}/file/list`,file_delete:e=>`${Y}/file/${e}`,file_rename:e=>`${Y}/file/${e}`,system_status:`${Y}/system/status`,system_version:`${Y}/system/version`},e0="3.7.8",Mw=e0,ii=typeof Buffer=="function",Dd=typeof TextDecoder=="function"?new TextDecoder:void 0,_d=typeof TextEncoder=="function"?new TextEncoder:void 0,Iw="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ri=Array.prototype.slice.call(Iw),Wo=(e=>{let t={};return e.forEach((n,r)=>t[n]=r),t})(Ri),Fw=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Be=String.fromCharCode.bind(String),jd=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),t0=e=>e.replace(/=/g,"").replace(/[+\/]/g,t=>t=="+"?"-":"_"),n0=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),r0=e=>{let t,n,r,i,o="";const s=e.length%3;for(let l=0;l<e.length;){if((n=e.charCodeAt(l++))>255||(r=e.charCodeAt(l++))>255||(i=e.charCodeAt(l++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|i,o+=Ri[t>>18&63]+Ri[t>>12&63]+Ri[t>>6&63]+Ri[t&63]}return s?o.slice(0,s-3)+"===".substring(s):o},Sc=typeof btoa=="function"?e=>btoa(e):ii?e=>Buffer.from(e,"binary").toString("base64"):r0,hu=ii?e=>Buffer.from(e).toString("base64"):e=>{let n=[];for(let r=0,i=e.length;r<i;r+=4096)n.push(Be.apply(null,e.subarray(r,r+4096)));return Sc(n.join(""))},gs=(e,t=!1)=>t?t0(hu(e)):hu(e),Bw=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?Be(192|t>>>6)+Be(128|t&63):Be(224|t>>>12&15)+Be(128|t>>>6&63)+Be(128|t&63)}else{var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return Be(240|t>>>18&7)+Be(128|t>>>12&63)+Be(128|t>>>6&63)+Be(128|t&63)}},Uw=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,i0=e=>e.replace(Uw,Bw),bd=ii?e=>Buffer.from(e,"utf8").toString("base64"):_d?e=>hu(_d.encode(e)):e=>Sc(i0(e)),zr=(e,t=!1)=>t?t0(bd(e)):bd(e),Pd=e=>zr(e,!0),zw=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Vw=e=>{switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),n=t-65536;return Be((n>>>10)+55296)+Be((n&1023)+56320);case 3:return Be((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Be((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},o0=e=>e.replace(zw,Vw),s0=e=>{if(e=e.replace(/\s+/g,""),!Fw.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(e.length&3));let t,n,r,i=[];for(let o=0;o<e.length;)t=Wo[e.charAt(o++)]<<18|Wo[e.charAt(o++)]<<12|(n=Wo[e.charAt(o++)])<<6|(r=Wo[e.charAt(o++)]),n===64?i.push(Be(t>>16&255)):r===64?i.push(Be(t>>16&255,t>>8&255)):i.push(Be(t>>16&255,t>>8&255,t&255));return i.join("")},Ec=typeof atob=="function"?e=>atob(n0(e)):ii?e=>Buffer.from(e,"base64").toString("binary"):s0,l0=ii?e=>jd(Buffer.from(e,"base64")):e=>jd(Ec(e).split("").map(t=>t.charCodeAt(0))),a0=e=>l0(u0(e)),$w=ii?e=>Buffer.from(e,"base64").toString("utf8"):Dd?e=>Dd.decode(l0(e)):e=>o0(Ec(e)),u0=e=>n0(e.replace(/[-_]/g,t=>t=="-"?"+":"/")),pu=e=>$w(u0(e)),Hw=e=>{if(typeof e!="string")return!1;const t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},c0=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),f0=function(){const e=(t,n)=>Object.defineProperty(String.prototype,t,c0(n));e("fromBase64",function(){return pu(this)}),e("toBase64",function(t){return zr(this,t)}),e("toBase64URI",function(){return zr(this,!0)}),e("toBase64URL",function(){return zr(this,!0)}),e("toUint8Array",function(){return a0(this)})},d0=function(){const e=(t,n)=>Object.defineProperty(Uint8Array.prototype,t,c0(n));e("toBase64",function(t){return gs(this,t)}),e("toBase64URI",function(){return gs(this,!0)}),e("toBase64URL",function(){return gs(this,!0)})},Kw=()=>{f0(),d0()},Ww={version:e0,VERSION:Mw,atob:Ec,atobPolyfill:s0,btoa:Sc,btoaPolyfill:r0,fromBase64:pu,toBase64:zr,encode:zr,encodeURI:Pd,encodeURL:Pd,utob:i0,btou:o0,decode:pu,isValid:Hw,fromUint8Array:gs,toUint8Array:a0,extendString:f0,extendUint8Array:d0,extendBuiltins:Kw};var qw="0123456789abcdefghijklmnopqrstuvwxyz";function Yt(e){return qw.charAt(e)}function Qw(e,t){return e&t}function qo(e,t){return e|t}function Ad(e,t){return e^t}function Od(e,t){return e&~t}function Gw(e){if(e==0)return-1;var t=0;return e&65535||(e>>=16,t+=16),e&255||(e>>=8,t+=8),e&15||(e>>=4,t+=4),e&3||(e>>=2,t+=2),e&1||++t,t}function Jw(e){for(var t=0;e!=0;)e&=e-1,++t;return t}var Er="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h0="=";function Fi(e){var t,n,r="";for(t=0;t+3<=e.length;t+=3)n=parseInt(e.substring(t,t+3),16),r+=Er.charAt(n>>6)+Er.charAt(n&63);for(t+1==e.length?(n=parseInt(e.substring(t,t+1),16),r+=Er.charAt(n<<2)):t+2==e.length&&(n=parseInt(e.substring(t,t+2),16),r+=Er.charAt(n>>2)+Er.charAt((n&3)<<4));(r.length&3)>0;)r+=h0;return r}function Ld(e){var t="",n,r=0,i=0;for(n=0;n<e.length&&e.charAt(n)!=h0;++n){var o=Er.indexOf(e.charAt(n));o<0||(r==0?(t+=Yt(o>>2),i=o&3,r=1):r==1?(t+=Yt(i<<2|o>>4),i=o&15,r=2):r==2?(t+=Yt(i),t+=Yt(o>>2),i=o&3,r=3):(t+=Yt(i<<2|o>>4),t+=Yt(o&15),r=0))}return r==1&&(t+=Yt(i<<2)),t}var wr,Yw={decode:function(e){var t;if(wr===void 0){var n="0123456789ABCDEF",r=` \f
\r	 \u2028\u2029`;for(wr={},t=0;t<16;++t)wr[n.charAt(t)]=t;for(n=n.toLowerCase(),t=10;t<16;++t)wr[n.charAt(t)]=t;for(t=0;t<r.length;++t)wr[r.charAt(t)]=-1}var i=[],o=0,s=0;for(t=0;t<e.length;++t){var l=e.charAt(t);if(l=="=")break;if(l=wr[l],l!=-1){if(l===void 0)throw new Error("Illegal character at offset "+t);o|=l,++s>=2?(i[i.length]=o,o=0,s=0):o<<=4}}if(s)throw new Error("Hex encoding incomplete: 4 bits missing");return i}},$n,mu={decode:function(e){var t;if($n===void 0){var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=`= \f
\r	 \u2028\u2029`;for($n=Object.create(null),t=0;t<64;++t)$n[n.charAt(t)]=t;for($n["-"]=62,$n._=63,t=0;t<r.length;++t)$n[r.charAt(t)]=-1}var i=[],o=0,s=0;for(t=0;t<e.length;++t){var l=e.charAt(t);if(l=="=")break;if(l=$n[l],l!=-1){if(l===void 0)throw new Error("Illegal character at offset "+t);o|=l,++s>=4?(i[i.length]=o>>16,i[i.length]=o>>8&255,i[i.length]=o&255,o=0,s=0):o<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=o>>10;break;case 3:i[i.length]=o>>16,i[i.length]=o>>8&255;break}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(e){var t=mu.re.exec(e);if(t)if(t[1])e=t[1];else if(t[2])e=t[2];else throw new Error("RegExp out of sync");return mu.decode(e)}},Sr=1e13,Di=function(){function e(t){this.buf=[+t||0]}return e.prototype.mulAdd=function(t,n){var r=this.buf,i=r.length,o,s;for(o=0;o<i;++o)s=r[o]*t+n,s<Sr?n=0:(n=0|s/Sr,s-=n*Sr),r[o]=s;n>0&&(r[o]=n)},e.prototype.sub=function(t){var n=this.buf,r=n.length,i,o;for(i=0;i<r;++i)o=n[i]-t,o<0?(o+=Sr,t=1):t=0,n[i]=o;for(;n[n.length-1]===0;)n.pop()},e.prototype.toString=function(t){if((t||10)!=10)throw new Error("only base 10 is supported");for(var n=this.buf,r=n[n.length-1].toString(),i=n.length-2;i>=0;--i)r+=(Sr+n[i]).toString().substring(1);return r},e.prototype.valueOf=function(){for(var t=this.buf,n=0,r=t.length-1;r>=0;--r)n=n*Sr+t[r];return n},e.prototype.simplify=function(){var t=this.buf;return t.length==1?t[0]:this},e}(),p0="…",Zw=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,Xw=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function Vr(e,t){return e.length>t&&(e=e.substring(0,t)+p0),e}var la=function(){function e(t,n){this.hexDigits="0123456789ABCDEF",t instanceof e?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=n)}return e.prototype.get=function(t){if(t===void 0&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return typeof this.enc=="string"?this.enc.charCodeAt(t):this.enc[t]},e.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(t&15)},e.prototype.hexDump=function(t,n,r){for(var i="",o=t;o<n;++o)if(i+=this.hexByte(this.get(o)),r!==!0)switch(o&15){case 7:i+="  ";break;case 15:i+=`
`;break;default:i+=" "}return i},e.prototype.isASCII=function(t,n){for(var r=t;r<n;++r){var i=this.get(r);if(i<32||i>176)return!1}return!0},e.prototype.parseStringISO=function(t,n){for(var r="",i=t;i<n;++i)r+=String.fromCharCode(this.get(i));return r},e.prototype.parseStringUTF=function(t,n){for(var r="",i=t;i<n;){var o=this.get(i++);o<128?r+=String.fromCharCode(o):o>191&&o<224?r+=String.fromCharCode((o&31)<<6|this.get(i++)&63):r+=String.fromCharCode((o&15)<<12|(this.get(i++)&63)<<6|this.get(i++)&63)}return r},e.prototype.parseStringBMP=function(t,n){for(var r="",i,o,s=t;s<n;)i=this.get(s++),o=this.get(s++),r+=String.fromCharCode(i<<8|o);return r},e.prototype.parseTime=function(t,n,r){var i=this.parseStringISO(t,n),o=(r?Zw:Xw).exec(i);return o?(r&&(o[1]=+o[1],o[1]+=+o[1]<70?2e3:1900),i=o[1]+"-"+o[2]+"-"+o[3]+" "+o[4],o[5]&&(i+=":"+o[5],o[6]&&(i+=":"+o[6],o[7]&&(i+="."+o[7]))),o[8]&&(i+=" UTC",o[8]!="Z"&&(i+=o[8],o[9]&&(i+=":"+o[9]))),i):"Unrecognized time: "+i},e.prototype.parseInteger=function(t,n){for(var r=this.get(t),i=r>127,o=i?255:0,s,l="";r==o&&++t<n;)r=this.get(t);if(s=n-t,s===0)return i?-1:0;if(s>4){for(l=r,s<<=3;!((+l^o)&128);)l=+l<<1,--s;l="("+s+` bit)
`}i&&(r=r-256);for(var a=new Di(r),u=t+1;u<n;++u)a.mulAdd(256,this.get(u));return l+a.toString()},e.prototype.parseBitString=function(t,n,r){for(var i=this.get(t),o=(n-t-1<<3)-i,s="("+o+` bit)
`,l="",a=t+1;a<n;++a){for(var u=this.get(a),c=a==n-1?i:0,f=7;f>=c;--f)l+=u>>f&1?"1":"0";if(l.length>r)return s+Vr(l,r)}return s+l},e.prototype.parseOctetString=function(t,n,r){if(this.isASCII(t,n))return Vr(this.parseStringISO(t,n),r);var i=n-t,o="("+i+` byte)
`;r/=2,i>r&&(n=t+r);for(var s=t;s<n;++s)o+=this.hexByte(this.get(s));return i>r&&(o+=p0),o},e.prototype.parseOID=function(t,n,r){for(var i="",o=new Di,s=0,l=t;l<n;++l){var a=this.get(l);if(o.mulAdd(128,a&127),s+=7,!(a&128)){if(i==="")if(o=o.simplify(),o instanceof Di)o.sub(80),i="2."+o.toString();else{var u=o<80?o<40?0:1:2;i=u+"."+(o-u*40)}else i+="."+o.toString();if(i.length>r)return Vr(i,r);o=new Di,s=0}}return s>0&&(i+=".incomplete"),i},e}(),eS=function(){function e(t,n,r,i,o){if(!(i instanceof Md))throw new Error("Invalid tag value.");this.stream=t,this.header=n,this.length=r,this.tag=i,this.sub=o}return e.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},e.prototype.content=function(t){if(this.tag===void 0)return null;t===void 0&&(t=1/0);var n=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return this.sub!==null?"("+this.sub.length+" elem)":this.stream.parseOctetString(n,n+r,t);switch(this.tag.tagNumber){case 1:return this.stream.get(n)===0?"false":"true";case 2:return this.stream.parseInteger(n,n+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(n,n+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(n,n+r,t);case 6:return this.stream.parseOID(n,n+r,t);case 16:case 17:return this.sub!==null?"("+this.sub.length+" elem)":"(no elem)";case 12:return Vr(this.stream.parseStringUTF(n,n+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return Vr(this.stream.parseStringISO(n,n+r),t);case 30:return Vr(this.stream.parseStringBMP(n,n+r),t);case 23:case 24:return this.stream.parseTime(n,n+r,this.tag.tagNumber==23)}return null},e.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(this.sub===null?"null":this.sub.length)+"]"},e.prototype.toPrettyString=function(t){t===void 0&&(t="");var n=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(n+="+"),n+=this.length,this.tag.tagConstructed?n+=" (constructed)":this.tag.isUniversal()&&(this.tag.tagNumber==3||this.tag.tagNumber==4)&&this.sub!==null&&(n+=" (encapsulates)"),n+=`
`,this.sub!==null){t+="  ";for(var r=0,i=this.sub.length;r<i;++r)n+=this.sub[r].toPrettyString(t)}return n},e.prototype.posStart=function(){return this.stream.pos},e.prototype.posContent=function(){return this.stream.pos+this.header},e.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},e.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},e.decodeLength=function(t){var n=t.get(),r=n&127;if(r==n)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(r===0)return null;n=0;for(var i=0;i<r;++i)n=n*256+t.get();return n},e.prototype.getHexStringValue=function(){var t=this.toHexString(),n=this.header*2,r=this.length*2;return t.substring(n,n+r)},e.decode=function(t){var n;t instanceof la?n=t:n=new la(t,0);var r=new la(n),i=new Md(n),o=e.decodeLength(n),s=n.pos,l=s-r.pos,a=null,u=function(){var f=[];if(o!==null){for(var h=s+o;n.pos<h;)f[f.length]=e.decode(n);if(n.pos!=h)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var y=e.decode(n);if(y.tag.isEOC())break;f[f.length]=y}o=s-n.pos}catch(m){throw new Error("Exception while decoding undefined length content: "+m)}return f};if(i.tagConstructed)a=u();else if(i.isUniversal()&&(i.tagNumber==3||i.tagNumber==4))try{if(i.tagNumber==3&&n.get()!=0)throw new Error("BIT STRINGs with unused bits cannot encapsulate.");a=u();for(var c=0;c<a.length;++c)if(a[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch{a=null}if(a===null){if(o===null)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);n.pos=s+Math.abs(o)}return new e(r,l,o,i,a)},e}(),Md=function(){function e(t){var n=t.get();if(this.tagClass=n>>6,this.tagConstructed=(n&32)!==0,this.tagNumber=n&31,this.tagNumber==31){var r=new Di;do n=t.get(),r.mulAdd(128,n&127);while(n&128);this.tagNumber=r.simplify()}}return e.prototype.isUniversal=function(){return this.tagClass===0},e.prototype.isEOC=function(){return this.tagClass===0&&this.tagNumber===0},e}(),Pn,tS=0xdeadbeefcafe,Id=(tS&16777215)==15715070,Je=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],nS=(1<<26)/Je[Je.length-1],q=function(){function e(t,n,r){t!=null&&(typeof t=="number"?this.fromNumber(t,n,r):n==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,n))}return e.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var n;if(t==16)n=4;else if(t==8)n=3;else if(t==2)n=1;else if(t==32)n=5;else if(t==4)n=2;else return this.toRadix(t);var r=(1<<n)-1,i,o=!1,s="",l=this.t,a=this.DB-l*this.DB%n;if(l-- >0)for(a<this.DB&&(i=this[l]>>a)>0&&(o=!0,s=Yt(i));l>=0;)a<n?(i=(this[l]&(1<<a)-1)<<n-a,i|=this[--l]>>(a+=this.DB-n)):(i=this[l]>>(a-=n)&r,a<=0&&(a+=this.DB,--l)),i>0&&(o=!0),o&&(s+=Yt(i));return o?s:"0"},e.prototype.negate=function(){var t=Z();return e.ZERO.subTo(this,t),t},e.prototype.abs=function(){return this.s<0?this.negate():this},e.prototype.compareTo=function(t){var n=this.s-t.s;if(n!=0)return n;var r=this.t;if(n=r-t.t,n!=0)return this.s<0?-n:n;for(;--r>=0;)if((n=this[r]-t[r])!=0)return n;return 0},e.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+Qo(this[this.t-1]^this.s&this.DM)},e.prototype.mod=function(t){var n=Z();return this.abs().divRemTo(t,null,n),this.s<0&&n.compareTo(e.ZERO)>0&&t.subTo(n,n),n},e.prototype.modPowInt=function(t,n){var r;return t<256||n.isEven()?r=new Fd(n):r=new Bd(n),this.exp(t,r)},e.prototype.clone=function(){var t=Z();return this.copyTo(t),t},e.prototype.intValue=function(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},e.prototype.byteValue=function(){return this.t==0?this.s:this[0]<<24>>24},e.prototype.shortValue=function(){return this.t==0?this.s:this[0]<<16>>16},e.prototype.signum=function(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1},e.prototype.toByteArray=function(){var t=this.t,n=[];n[0]=this.s;var r=this.DB-t*this.DB%8,i,o=0;if(t-- >0)for(r<this.DB&&(i=this[t]>>r)!=(this.s&this.DM)>>r&&(n[o++]=i|this.s<<this.DB-r);t>=0;)r<8?(i=(this[t]&(1<<r)-1)<<8-r,i|=this[--t]>>(r+=this.DB-8)):(i=this[t]>>(r-=8)&255,r<=0&&(r+=this.DB,--t)),i&128&&(i|=-256),o==0&&(this.s&128)!=(i&128)&&++o,(o>0||i!=this.s)&&(n[o++]=i);return n},e.prototype.equals=function(t){return this.compareTo(t)==0},e.prototype.min=function(t){return this.compareTo(t)<0?this:t},e.prototype.max=function(t){return this.compareTo(t)>0?this:t},e.prototype.and=function(t){var n=Z();return this.bitwiseTo(t,Qw,n),n},e.prototype.or=function(t){var n=Z();return this.bitwiseTo(t,qo,n),n},e.prototype.xor=function(t){var n=Z();return this.bitwiseTo(t,Ad,n),n},e.prototype.andNot=function(t){var n=Z();return this.bitwiseTo(t,Od,n),n},e.prototype.not=function(){for(var t=Z(),n=0;n<this.t;++n)t[n]=this.DM&~this[n];return t.t=this.t,t.s=~this.s,t},e.prototype.shiftLeft=function(t){var n=Z();return t<0?this.rShiftTo(-t,n):this.lShiftTo(t,n),n},e.prototype.shiftRight=function(t){var n=Z();return t<0?this.lShiftTo(-t,n):this.rShiftTo(t,n),n},e.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+Gw(this[t]);return this.s<0?this.t*this.DB:-1},e.prototype.bitCount=function(){for(var t=0,n=this.s&this.DM,r=0;r<this.t;++r)t+=Jw(this[r]^n);return t},e.prototype.testBit=function(t){var n=Math.floor(t/this.DB);return n>=this.t?this.s!=0:(this[n]&1<<t%this.DB)!=0},e.prototype.setBit=function(t){return this.changeBit(t,qo)},e.prototype.clearBit=function(t){return this.changeBit(t,Od)},e.prototype.flipBit=function(t){return this.changeBit(t,Ad)},e.prototype.add=function(t){var n=Z();return this.addTo(t,n),n},e.prototype.subtract=function(t){var n=Z();return this.subTo(t,n),n},e.prototype.multiply=function(t){var n=Z();return this.multiplyTo(t,n),n},e.prototype.divide=function(t){var n=Z();return this.divRemTo(t,n,null),n},e.prototype.remainder=function(t){var n=Z();return this.divRemTo(t,null,n),n},e.prototype.divideAndRemainder=function(t){var n=Z(),r=Z();return this.divRemTo(t,n,r),[n,r]},e.prototype.modPow=function(t,n){var r=t.bitLength(),i,o=yn(1),s;if(r<=0)return o;r<18?i=1:r<48?i=3:r<144?i=4:r<768?i=5:i=6,r<8?s=new Fd(n):n.isEven()?s=new iS(n):s=new Bd(n);var l=[],a=3,u=i-1,c=(1<<i)-1;if(l[1]=s.convert(this),i>1){var f=Z();for(s.sqrTo(l[1],f);a<=c;)l[a]=Z(),s.mulTo(f,l[a-2],l[a]),a+=2}var h=t.t-1,y,m=!0,v=Z(),N;for(r=Qo(t[h])-1;h>=0;){for(r>=u?y=t[h]>>r-u&c:(y=(t[h]&(1<<r+1)-1)<<u-r,h>0&&(y|=t[h-1]>>this.DB+r-u)),a=i;!(y&1);)y>>=1,--a;if((r-=a)<0&&(r+=this.DB,--h),m)l[y].copyTo(o),m=!1;else{for(;a>1;)s.sqrTo(o,v),s.sqrTo(v,o),a-=2;a>0?s.sqrTo(o,v):(N=o,o=v,v=N),s.mulTo(v,l[y],o)}for(;h>=0&&!(t[h]&1<<r);)s.sqrTo(o,v),N=o,o=v,v=N,--r<0&&(r=this.DB-1,--h)}return s.revert(o)},e.prototype.modInverse=function(t){var n=t.isEven();if(this.isEven()&&n||t.signum()==0)return e.ZERO;for(var r=t.clone(),i=this.clone(),o=yn(1),s=yn(0),l=yn(0),a=yn(1);r.signum()!=0;){for(;r.isEven();)r.rShiftTo(1,r),n?((!o.isEven()||!s.isEven())&&(o.addTo(this,o),s.subTo(t,s)),o.rShiftTo(1,o)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;i.isEven();)i.rShiftTo(1,i),n?((!l.isEven()||!a.isEven())&&(l.addTo(this,l),a.subTo(t,a)),l.rShiftTo(1,l)):a.isEven()||a.subTo(t,a),a.rShiftTo(1,a);r.compareTo(i)>=0?(r.subTo(i,r),n&&o.subTo(l,o),s.subTo(a,s)):(i.subTo(r,i),n&&l.subTo(o,l),a.subTo(s,a))}if(i.compareTo(e.ONE)!=0)return e.ZERO;if(a.compareTo(t)>=0)return a.subtract(t);if(a.signum()<0)a.addTo(t,a);else return a;return a.signum()<0?a.add(t):a},e.prototype.pow=function(t){return this.exp(t,new rS)},e.prototype.gcd=function(t){var n=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(n.compareTo(r)<0){var i=n;n=r,r=i}var o=n.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return n;for(o<s&&(s=o),s>0&&(n.rShiftTo(s,n),r.rShiftTo(s,r));n.signum()>0;)(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),n.compareTo(r)>=0?(n.subTo(r,n),n.rShiftTo(1,n)):(r.subTo(n,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},e.prototype.isProbablePrime=function(t){var n,r=this.abs();if(r.t==1&&r[0]<=Je[Je.length-1]){for(n=0;n<Je.length;++n)if(r[0]==Je[n])return!0;return!1}if(r.isEven())return!1;for(n=1;n<Je.length;){for(var i=Je[n],o=n+1;o<Je.length&&i<nS;)i*=Je[o++];for(i=r.modInt(i);n<o;)if(i%Je[n++]==0)return!1}return r.millerRabin(t)},e.prototype.copyTo=function(t){for(var n=this.t-1;n>=0;--n)t[n]=this[n];t.t=this.t,t.s=this.s},e.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},e.prototype.fromString=function(t,n){var r;if(n==16)r=4;else if(n==8)r=3;else if(n==256)r=8;else if(n==2)r=1;else if(n==32)r=5;else if(n==4)r=2;else{this.fromRadix(t,n);return}this.t=0,this.s=0;for(var i=t.length,o=!1,s=0;--i>=0;){var l=r==8?+t[i]&255:zd(t,i);if(l<0){t.charAt(i)=="-"&&(o=!0);continue}o=!1,s==0?this[this.t++]=l:s+r>this.DB?(this[this.t-1]|=(l&(1<<this.DB-s)-1)<<s,this[this.t++]=l>>this.DB-s):this[this.t-1]|=l<<s,s+=r,s>=this.DB&&(s-=this.DB)}r==8&&+t[0]&128&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),o&&e.ZERO.subTo(this,this)},e.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},e.prototype.dlShiftTo=function(t,n){var r;for(r=this.t-1;r>=0;--r)n[r+t]=this[r];for(r=t-1;r>=0;--r)n[r]=0;n.t=this.t+t,n.s=this.s},e.prototype.drShiftTo=function(t,n){for(var r=t;r<this.t;++r)n[r-t]=this[r];n.t=Math.max(this.t-t,0),n.s=this.s},e.prototype.lShiftTo=function(t,n){for(var r=t%this.DB,i=this.DB-r,o=(1<<i)-1,s=Math.floor(t/this.DB),l=this.s<<r&this.DM,a=this.t-1;a>=0;--a)n[a+s+1]=this[a]>>i|l,l=(this[a]&o)<<r;for(var a=s-1;a>=0;--a)n[a]=0;n[s]=l,n.t=this.t+s+1,n.s=this.s,n.clamp()},e.prototype.rShiftTo=function(t,n){n.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t){n.t=0;return}var i=t%this.DB,o=this.DB-i,s=(1<<i)-1;n[0]=this[r]>>i;for(var l=r+1;l<this.t;++l)n[l-r-1]|=(this[l]&s)<<o,n[l-r]=this[l]>>i;i>0&&(n[this.t-r-1]|=(this.s&s)<<o),n.t=this.t-r,n.clamp()},e.prototype.subTo=function(t,n){for(var r=0,i=0,o=Math.min(t.t,this.t);r<o;)i+=this[r]-t[r],n[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;r<this.t;)i+=this[r],n[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i-=t[r],n[r++]=i&this.DM,i>>=this.DB;i-=t.s}n.s=i<0?-1:0,i<-1?n[r++]=this.DV+i:i>0&&(n[r++]=i),n.t=r,n.clamp()},e.prototype.multiplyTo=function(t,n){var r=this.abs(),i=t.abs(),o=r.t;for(n.t=o+i.t;--o>=0;)n[o]=0;for(o=0;o<i.t;++o)n[o+r.t]=r.am(0,i[o],n,o,0,r.t);n.s=0,n.clamp(),this.s!=t.s&&e.ZERO.subTo(n,n)},e.prototype.squareTo=function(t){for(var n=this.abs(),r=t.t=2*n.t;--r>=0;)t[r]=0;for(r=0;r<n.t-1;++r){var i=n.am(r,n[r],t,2*r,0,1);(t[r+n.t]+=n.am(r+1,2*n[r],t,2*r+1,i,n.t-r-1))>=n.DV&&(t[r+n.t]-=n.DV,t[r+n.t+1]=1)}t.t>0&&(t[t.t-1]+=n.am(r,n[r],t,2*r,0,1)),t.s=0,t.clamp()},e.prototype.divRemTo=function(t,n,r){var i=t.abs();if(!(i.t<=0)){var o=this.abs();if(o.t<i.t){n!=null&&n.fromInt(0),r!=null&&this.copyTo(r);return}r==null&&(r=Z());var s=Z(),l=this.s,a=t.s,u=this.DB-Qo(i[i.t-1]);u>0?(i.lShiftTo(u,s),o.lShiftTo(u,r)):(i.copyTo(s),o.copyTo(r));var c=s.t,f=s[c-1];if(f!=0){var h=f*(1<<this.F1)+(c>1?s[c-2]>>this.F2:0),y=this.FV/h,m=(1<<this.F1)/h,v=1<<this.F2,N=r.t,g=N-c,p=n??Z();for(s.dlShiftTo(g,p),r.compareTo(p)>=0&&(r[r.t++]=1,r.subTo(p,r)),e.ONE.dlShiftTo(c,p),p.subTo(s,s);s.t<c;)s[s.t++]=0;for(;--g>=0;){var x=r[--N]==f?this.DM:Math.floor(r[N]*y+(r[N-1]+v)*m);if((r[N]+=s.am(0,x,r,g,0,c))<x)for(s.dlShiftTo(g,p),r.subTo(p,r);r[N]<--x;)r.subTo(p,r)}n!=null&&(r.drShiftTo(c,n),l!=a&&e.ZERO.subTo(n,n)),r.t=c,r.clamp(),u>0&&r.rShiftTo(u,r),l<0&&e.ZERO.subTo(r,r)}}},e.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(t&1))return 0;var n=t&3;return n=n*(2-(t&15)*n)&15,n=n*(2-(t&255)*n)&255,n=n*(2-((t&65535)*n&65535))&65535,n=n*(2-t*n%this.DV)%this.DV,n>0?this.DV-n:-n},e.prototype.isEven=function(){return(this.t>0?this[0]&1:this.s)==0},e.prototype.exp=function(t,n){if(t>4294967295||t<1)return e.ONE;var r=Z(),i=Z(),o=n.convert(this),s=Qo(t)-1;for(o.copyTo(r);--s>=0;)if(n.sqrTo(r,i),(t&1<<s)>0)n.mulTo(i,o,r);else{var l=r;r=i,i=l}return n.revert(r)},e.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},e.prototype.toRadix=function(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var n=this.chunkSize(t),r=Math.pow(t,n),i=yn(r),o=Z(),s=Z(),l="";for(this.divRemTo(i,o,s);o.signum()>0;)l=(r+s.intValue()).toString(t).substring(1)+l,o.divRemTo(i,o,s);return s.intValue().toString(t)+l},e.prototype.fromRadix=function(t,n){this.fromInt(0),n==null&&(n=10);for(var r=this.chunkSize(n),i=Math.pow(n,r),o=!1,s=0,l=0,a=0;a<t.length;++a){var u=zd(t,a);if(u<0){t.charAt(a)=="-"&&this.signum()==0&&(o=!0);continue}l=n*l+u,++s>=r&&(this.dMultiply(i),this.dAddOffset(l,0),s=0,l=0)}s>0&&(this.dMultiply(Math.pow(n,s)),this.dAddOffset(l,0)),o&&e.ZERO.subTo(this,this)},e.prototype.fromNumber=function(t,n,r){if(typeof n=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),qo,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(n);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(e.ONE.shiftLeft(t-1),this);else{var i=[],o=t&7;i.length=(t>>3)+1,n.nextBytes(i),o>0?i[0]&=(1<<o)-1:i[0]=0,this.fromString(i,256)}},e.prototype.bitwiseTo=function(t,n,r){var i,o,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=n(this[i],t[i]);if(t.t<this.t){for(o=t.s&this.DM,i=s;i<this.t;++i)r[i]=n(this[i],o);r.t=this.t}else{for(o=this.s&this.DM,i=s;i<t.t;++i)r[i]=n(o,t[i]);r.t=t.t}r.s=n(this.s,t.s),r.clamp()},e.prototype.changeBit=function(t,n){var r=e.ONE.shiftLeft(t);return this.bitwiseTo(r,n,r),r},e.prototype.addTo=function(t,n){for(var r=0,i=0,o=Math.min(t.t,this.t);r<o;)i+=this[r]+t[r],n[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;r<this.t;)i+=this[r],n[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i+=t[r],n[r++]=i&this.DM,i>>=this.DB;i+=t.s}n.s=i<0?-1:0,i>0?n[r++]=i:i<-1&&(n[r++]=this.DV+i),n.t=r,n.clamp()},e.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},e.prototype.dAddOffset=function(t,n){if(t!=0){for(;this.t<=n;)this[this.t++]=0;for(this[n]+=t;this[n]>=this.DV;)this[n]-=this.DV,++n>=this.t&&(this[this.t++]=0),++this[n]}},e.prototype.multiplyLowerTo=function(t,n,r){var i=Math.min(this.t+t.t,n);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(var o=r.t-this.t;i<o;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(var o=Math.min(t.t,n);i<o;++i)this.am(0,t[i],r,i,0,n-i);r.clamp()},e.prototype.multiplyUpperTo=function(t,n,r){--n;var i=r.t=this.t+t.t-n;for(r.s=0;--i>=0;)r[i]=0;for(i=Math.max(n-this.t,0);i<t.t;++i)r[this.t+i-n]=this.am(n-i,t[i],r,0,0,this.t+i-n);r.clamp(),r.drShiftTo(1,r)},e.prototype.modInt=function(t){if(t<=0)return 0;var n=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(n==0)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(n*r+this[i])%t;return r},e.prototype.millerRabin=function(t){var n=this.subtract(e.ONE),r=n.getLowestSetBit();if(r<=0)return!1;var i=n.shiftRight(r);t=t+1>>1,t>Je.length&&(t=Je.length);for(var o=Z(),s=0;s<t;++s){o.fromInt(Je[Math.floor(Math.random()*Je.length)]);var l=o.modPow(i,this);if(l.compareTo(e.ONE)!=0&&l.compareTo(n)!=0){for(var a=1;a++<r&&l.compareTo(n)!=0;)if(l=l.modPowInt(2,this),l.compareTo(e.ONE)==0)return!1;if(l.compareTo(n)!=0)return!1}}return!0},e.prototype.square=function(){var t=Z();return this.squareTo(t),t},e.prototype.gcda=function(t,n){var r=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(r.compareTo(i)<0){var o=r;r=i,i=o}var s=r.getLowestSetBit(),l=i.getLowestSetBit();if(l<0){n(r);return}s<l&&(l=s),l>0&&(r.rShiftTo(l,r),i.rShiftTo(l,i));var a=function(){(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),r.compareTo(i)>=0?(r.subTo(i,r),r.rShiftTo(1,r)):(i.subTo(r,i),i.rShiftTo(1,i)),r.signum()>0?setTimeout(a,0):(l>0&&i.lShiftTo(l,i),setTimeout(function(){n(i)},0))};setTimeout(a,10)},e.prototype.fromNumberAsync=function(t,n,r,i){if(typeof n=="number")if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),qo,this),this.isEven()&&this.dAddOffset(1,0);var o=this,s=function(){o.dAddOffset(2,0),o.bitLength()>t&&o.subTo(e.ONE.shiftLeft(t-1),o),o.isProbablePrime(n)?setTimeout(function(){i()},0):setTimeout(s,0)};setTimeout(s,0)}else{var l=[],a=t&7;l.length=(t>>3)+1,n.nextBytes(l),a>0?l[0]&=(1<<a)-1:l[0]=0,this.fromString(l,256)}},e}(),rS=function(){function e(){}return e.prototype.convert=function(t){return t},e.prototype.revert=function(t){return t},e.prototype.mulTo=function(t,n,r){t.multiplyTo(n,r)},e.prototype.sqrTo=function(t,n){t.squareTo(n)},e}(),Fd=function(){function e(t){this.m=t}return e.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},e.prototype.revert=function(t){return t},e.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},e.prototype.mulTo=function(t,n,r){t.multiplyTo(n,r),this.reduce(r)},e.prototype.sqrTo=function(t,n){t.squareTo(n),this.reduce(n)},e}(),Bd=function(){function e(t){this.m=t,this.mp=t.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return e.prototype.convert=function(t){var n=Z();return t.abs().dlShiftTo(this.m.t,n),n.divRemTo(this.m,null,n),t.s<0&&n.compareTo(q.ZERO)>0&&this.m.subTo(n,n),n},e.prototype.revert=function(t){var n=Z();return t.copyTo(n),this.reduce(n),n},e.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var n=0;n<this.m.t;++n){var r=t[n]&32767,i=r*this.mpl+((r*this.mph+(t[n]>>15)*this.mpl&this.um)<<15)&t.DM;for(r=n+this.m.t,t[r]+=this.m.am(0,i,t,n,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},e.prototype.mulTo=function(t,n,r){t.multiplyTo(n,r),this.reduce(r)},e.prototype.sqrTo=function(t,n){t.squareTo(n),this.reduce(n)},e}(),iS=function(){function e(t){this.m=t,this.r2=Z(),this.q3=Z(),q.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return e.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var n=Z();return t.copyTo(n),this.reduce(n),n},e.prototype.revert=function(t){return t},e.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},e.prototype.mulTo=function(t,n,r){t.multiplyTo(n,r),this.reduce(r)},e.prototype.sqrTo=function(t,n){t.squareTo(n),this.reduce(n)},e}();function Z(){return new q(null)}function Ee(e,t){return new q(e,t)}var Ud=typeof navigator<"u";Ud&&Id&&navigator.appName=="Microsoft Internet Explorer"?(q.prototype.am=function(t,n,r,i,o,s){for(var l=n&32767,a=n>>15;--s>=0;){var u=this[t]&32767,c=this[t++]>>15,f=a*u+c*l;u=l*u+((f&32767)<<15)+r[i]+(o&**********),o=(u>>>30)+(f>>>15)+a*c+(o>>>30),r[i++]=u&**********}return o},Pn=30):Ud&&Id&&navigator.appName!="Netscape"?(q.prototype.am=function(t,n,r,i,o,s){for(;--s>=0;){var l=n*this[t++]+r[i]+o;o=Math.floor(l/67108864),r[i++]=l&67108863}return o},Pn=26):(q.prototype.am=function(t,n,r,i,o,s){for(var l=n&16383,a=n>>14;--s>=0;){var u=this[t]&16383,c=this[t++]>>14,f=a*u+c*l;u=l*u+((f&16383)<<14)+r[i]+o,o=(u>>28)+(f>>14)+a*c,r[i++]=u&268435455}return o},Pn=28);q.prototype.DB=Pn;q.prototype.DM=(1<<Pn)-1;q.prototype.DV=1<<Pn;var Nc=52;q.prototype.FV=Math.pow(2,Nc);q.prototype.F1=Nc-Pn;q.prototype.F2=2*Pn-Nc;var kl=[],oi,Nt;oi=48;for(Nt=0;Nt<=9;++Nt)kl[oi++]=Nt;oi=97;for(Nt=10;Nt<36;++Nt)kl[oi++]=Nt;oi=65;for(Nt=10;Nt<36;++Nt)kl[oi++]=Nt;function zd(e,t){var n=kl[e.charCodeAt(t)];return n??-1}function yn(e){var t=Z();return t.fromInt(e),t}function Qo(e){var t=1,n;return(n=e>>>16)!=0&&(e=n,t+=16),(n=e>>8)!=0&&(e=n,t+=8),(n=e>>4)!=0&&(e=n,t+=4),(n=e>>2)!=0&&(e=n,t+=2),(n=e>>1)!=0&&(e=n,t+=1),t}q.ZERO=yn(0);q.ONE=yn(1);var oS=function(){function e(){this.i=0,this.j=0,this.S=[]}return e.prototype.init=function(t){var n,r,i;for(n=0;n<256;++n)this.S[n]=n;for(r=0,n=0;n<256;++n)r=r+this.S[n]+t[n%t.length]&255,i=this.S[n],this.S[n]=this.S[r],this.S[r]=i;this.i=0,this.j=0},e.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},e}();function sS(){return new oS}var m0=256,Go,Nn=null,At;if(Nn==null){Nn=[],At=0;var Jo=void 0;if(typeof window<"u"&&self.crypto&&self.crypto.getRandomValues){var aa=new Uint32Array(256);for(self.crypto.getRandomValues(aa),Jo=0;Jo<aa.length;++Jo)Nn[At++]=aa[Jo]&255}var Yo=0,Zo=function(e){if(Yo=Yo||0,Yo>=256||At>=m0){self.removeEventListener?self.removeEventListener("mousemove",Zo,!1):self.detachEvent&&self.detachEvent("onmousemove",Zo);return}try{var t=e.x+e.y;Nn[At++]=t&255,Yo+=1}catch{}};typeof window<"u"&&(self.addEventListener?self.addEventListener("mousemove",Zo,!1):self.attachEvent&&self.attachEvent("onmousemove",Zo))}function lS(){if(Go==null){for(Go=sS();At<m0;){var e=Math.floor(65536*Math.random());Nn[At++]=e&255}for(Go.init(Nn),At=0;At<Nn.length;++At)Nn[At]=0;At=0}return Go.next()}var Ys=function(){function e(){}return e.prototype.nextBytes=function(t){for(var n=0;n<t.length;++n)t[n]=lS()},e}();function gu(e){return uS(yS(aS(e),e.length*8))}function Vd(e){for(var t="0123456789abcdef",n="",r=0;r<e.length;r++){var i=e.charCodeAt(r);n+=t.charAt(i>>>4&15)+t.charAt(i&15)}return n}function aS(e){for(var t=Array(e.length>>2),n=0;n<t.length;n++)t[n]=0;for(var n=0;n<e.length*8;n+=8)t[n>>5]|=(e.charCodeAt(n/8)&255)<<24-n%32;return t}function uS(e){for(var t="",n=0;n<e.length*32;n+=8)t+=String.fromCharCode(e[n>>5]>>>24-n%32&255);return t}function Wt(e,t){return e>>>t|e<<32-t}function g0(e,t){return e>>>t}function cS(e,t,n){return e&t^~e&n}function fS(e,t,n){return e&t^e&n^t&n}function dS(e){return Wt(e,2)^Wt(e,13)^Wt(e,22)}function hS(e){return Wt(e,6)^Wt(e,11)^Wt(e,25)}function pS(e){return Wt(e,7)^Wt(e,18)^g0(e,3)}function mS(e){return Wt(e,17)^Wt(e,19)^g0(e,10)}var gS=new Array(1116352408,1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075,-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716,-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986,-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259,-1564481375,-1474664885,-1035236496,-949202525,-778901479,-694614492,-200395387,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998);function yS(e,t){var n=new Array(1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225),r=new Array(64),i,o,s,l,a,u,c,f,h,y,m,v;for(e[t>>5]|=128<<24-t%32,e[(t+64>>9<<4)+15]=t,h=0;h<e.length;h+=16){for(i=n[0],o=n[1],s=n[2],l=n[3],a=n[4],u=n[5],c=n[6],f=n[7],y=0;y<64;y++)y<16?r[y]=e[y+h]:r[y]=Ae(Ae(Ae(mS(r[y-2]),r[y-7]),pS(r[y-15])),r[y-16]),m=Ae(Ae(Ae(Ae(f,hS(a)),cS(a,u,c)),gS[y]),r[y]),v=Ae(dS(i),fS(i,o,s)),f=c,c=u,u=a,a=Ae(l,m),l=s,s=o,o=i,i=Ae(m,v);n[0]=Ae(i,n[0]),n[1]=Ae(o,n[1]),n[2]=Ae(s,n[2]),n[3]=Ae(l,n[3]),n[4]=Ae(a,n[4]),n[5]=Ae(u,n[5]),n[6]=Ae(c,n[6]),n[7]=Ae(f,n[7])}return n}function Ae(e,t){var n=(e&65535)+(t&65535),r=(e>>16)+(t>>16)+(n>>16);return r<<16|n&65535}function vS(e,t){if(t<e.length+22)return console.error("Message too long for RSA"),null;for(var n=t-e.length-6,r="",i=0;i<n;i+=2)r+="ff";var o="0001"+r+"00"+e;return Ee(o,16)}function xS(e,t){if(t<e.length+11)return console.error("Message too long for RSA"),null;for(var n=[],r=e.length-1;r>=0&&t>0;){var i=e.charCodeAt(r--);i<128?n[--t]=i:i>127&&i<2048?(n[--t]=i&63|128,n[--t]=i>>6|192):(n[--t]=i&63|128,n[--t]=i>>6&63|128,n[--t]=i>>12|224)}n[--t]=0;for(var o=new Ys,s=[];t>2;){for(s[0]=0;s[0]==0;)o.nextBytes(s);n[--t]=s[0]}return n[--t]=2,n[--t]=0,new q(n)}function $d(e,t,n){for(var r="",i=0;r.length<t;)r+=n(String.fromCharCode.apply(String,e.concat([(i&4278190080)>>24,(i&16711680)>>16,(i&65280)>>8,i&255]))),i+=1;return r}var wS=32;function SS(e,t){var n=wS,r=gu;if(e.length+2*n+2>t)throw"Message too long for RSA";var i="",o;for(o=0;o<t-e.length-2*n-2;o+=1)i+="\0";var s=r("")+i+""+e,l=new Array(n);new Ys().nextBytes(l);var a=$d(l,s.length,r),u=[];for(o=0;o<s.length;o+=1)u[o]=s.charCodeAt(o)^a.charCodeAt(o);var c=$d(u,l.length,r),f=[0];for(o=0;o<l.length;o+=1)f[o+1]=l[o]^c.charCodeAt(o);return new q(f.concat(u))}var ES=function(){function e(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return e.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},e.prototype.doPrivate=function(t){if(this.p==null||this.q==null)return t.modPow(this.d,this.n);for(var n=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);n.compareTo(r)<0;)n=n.add(this.p);return n.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},e.prototype.setPublic=function(t,n){t!=null&&n!=null&&t.length>0&&n.length>0?(this.n=Ee(t,16),this.e=parseInt(n,16)):console.error("Invalid RSA public key")},e.prototype.encrypt=function(t,n){typeof n>"u"&&(n=xS);var r=this.n.bitLength()+7>>3,i=n(t,r);if(i==null)return null;var o=this.doPublic(i);if(o==null)return null;for(var s=o.toString(16),l=s.length,a=0;a<r*2-l;a++)s="0"+s;return s},e.prototype.setPrivate=function(t,n,r){t!=null&&n!=null&&t.length>0&&n.length>0?(this.n=Ee(t,16),this.e=parseInt(n,16),this.d=Ee(r,16)):console.error("Invalid RSA private key")},e.prototype.setPrivateEx=function(t,n,r,i,o,s,l,a){t!=null&&n!=null&&t.length>0&&n.length>0?(this.n=Ee(t,16),this.e=parseInt(n,16),this.d=Ee(r,16),this.p=Ee(i,16),this.q=Ee(o,16),this.dmp1=Ee(s,16),this.dmq1=Ee(l,16),this.coeff=Ee(a,16)):console.error("Invalid RSA private key")},e.prototype.generate=function(t,n){var r=new Ys,i=t>>1;this.e=parseInt(n,16);for(var o=new q(n,16);;){for(;this.p=new q(t-i,1,r),!(this.p.subtract(q.ONE).gcd(o).compareTo(q.ONE)==0&&this.p.isProbablePrime(10)););for(;this.q=new q(i,1,r),!(this.q.subtract(q.ONE).gcd(o).compareTo(q.ONE)==0&&this.q.isProbablePrime(10)););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var l=this.p.subtract(q.ONE),a=this.q.subtract(q.ONE),u=l.multiply(a);if(u.gcd(o).compareTo(q.ONE)==0){this.n=this.p.multiply(this.q),this.d=o.modInverse(u),this.dmp1=this.d.mod(l),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},e.prototype.decrypt=function(t){var n=Ee(t,16),r=this.doPrivate(n);return r==null?null:NS(r,this.n.bitLength()+7>>3)},e.prototype.generateAsync=function(t,n,r){var i=new Ys,o=t>>1;this.e=parseInt(n,16);var s=new q(n,16),l=this,a=function(){var u=function(){if(l.p.compareTo(l.q)<=0){var h=l.p;l.p=l.q,l.q=h}var y=l.p.subtract(q.ONE),m=l.q.subtract(q.ONE),v=y.multiply(m);v.gcd(s).compareTo(q.ONE)==0?(l.n=l.p.multiply(l.q),l.d=s.modInverse(v),l.dmp1=l.d.mod(y),l.dmq1=l.d.mod(m),l.coeff=l.q.modInverse(l.p),setTimeout(function(){r()},0)):setTimeout(a,0)},c=function(){l.q=Z(),l.q.fromNumberAsync(o,1,i,function(){l.q.subtract(q.ONE).gcda(s,function(h){h.compareTo(q.ONE)==0&&l.q.isProbablePrime(10)?setTimeout(u,0):setTimeout(c,0)})})},f=function(){l.p=Z(),l.p.fromNumberAsync(t-o,1,i,function(){l.p.subtract(q.ONE).gcda(s,function(h){h.compareTo(q.ONE)==0&&l.p.isProbablePrime(10)?setTimeout(c,0):setTimeout(f,0)})})};setTimeout(f,0)};setTimeout(a,0)},e.prototype.sign=function(t,n,r){var i=kS(r),o=i+n(t).toString(),s=this.n.bitLength()/4,l=vS(o,s);if(l==null)return null;var a=this.doPrivate(l);if(a==null)return null;for(var u=a.toString(16),c=u.length,f=0;f<s-c;f++)u="0"+u;return u},e.prototype.verify=function(t,n,r){var i=Ee(n,16),o=this.doPublic(i);if(o==null)return null;var s=o.toString(16).replace(/^1f+00/,""),l=TS(s);return l==r(t).toString()},e}();function NS(e,t){for(var n=e.toByteArray(),r=0;r<n.length&&n[r]==0;)++r;if(n.length-r!=t-1||n[r]!=2)return null;for(++r;n[r]!=0;)if(++r>=n.length)return null;for(var i="";++r<n.length;){var o=n[r]&255;o<128?i+=String.fromCharCode(o):o>191&&o<224?(i+=String.fromCharCode((o&31)<<6|n[r+1]&63),++r):(i+=String.fromCharCode((o&15)<<12|(n[r+1]&63)<<6|n[r+2]&63),r+=2)}return i}var ys={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};function kS(e){return ys[e]||""}function TS(e){for(var t in ys)if(ys.hasOwnProperty(t)){var n=ys[t],r=n.length;if(e.substring(0,r)==n)return e.substring(r)}return e}function je(e,t,n){if(!t||!e)throw new Error("extend failed, please check that all dependencies are included.");var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e,e.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t)}/**
 * @fileOverview
 * @name asn1-1.0.js
 * <AUTHOR>
 * @version asn1 1.0.13 (2017-Jun-02)
 * @since jsrsasign 2.1
 * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
 */var j={};(typeof j.asn1>"u"||!j.asn1)&&(j.asn1={});j.asn1.ASN1Util=new function(){this.integerToByteHex=function(e){var t=e.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(e){var t=e.toString(16);if(t.substring(0,1)!="-")t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var n=t.substring(1),r=n.length;r%2==1?r+=1:t.match(/^[0-7]/)||(r+=2);for(var i="",o=0;o<r;o++)i+="f";var s=new q(i,16),l=s.xor(e).add(q.ONE);t=l.toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(e,t){return hextopem(e,t)},this.newObject=function(e){var t=j,n=t.asn1,r=n.DERBoolean,i=n.DERInteger,o=n.DERBitString,s=n.DEROctetString,l=n.DERNull,a=n.DERObjectIdentifier,u=n.DEREnumerated,c=n.DERUTF8String,f=n.DERNumericString,h=n.DERPrintableString,y=n.DERTeletexString,m=n.DERIA5String,v=n.DERUTCTime,N=n.DERGeneralizedTime,g=n.DERSequence,p=n.DERSet,x=n.DERTaggedObject,R=n.ASN1Util.newObject,b=Object.keys(e);if(b.length!=1)throw"key of param shall be only one.";var T=b[0];if(":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+T+":")==-1)throw"undefined key: "+T;if(T=="bool")return new r(e[T]);if(T=="int")return new i(e[T]);if(T=="bitstr")return new o(e[T]);if(T=="octstr")return new s(e[T]);if(T=="null")return new l(e[T]);if(T=="oid")return new a(e[T]);if(T=="enum")return new u(e[T]);if(T=="utf8str")return new c(e[T]);if(T=="numstr")return new f(e[T]);if(T=="prnstr")return new h(e[T]);if(T=="telstr")return new y(e[T]);if(T=="ia5str")return new m(e[T]);if(T=="utctime")return new v(e[T]);if(T=="gentime")return new N(e[T]);if(T=="seq"){for(var S=e[T],_=[],B=0;B<S.length;B++){var L=R(S[B]);_.push(L)}return new g({array:_})}if(T=="set"){for(var S=e[T],_=[],B=0;B<S.length;B++){var L=R(S[B]);_.push(L)}return new p({array:_})}if(T=="tag"){var J=e[T];if(Object.prototype.toString.call(J)==="[object Array]"&&J.length==3){var le=R(J[2]);return new x({tag:J[0],explicit:J[1],obj:le})}else{var pe={};if(J.explicit!==void 0&&(pe.explicit=J.explicit),J.tag!==void 0&&(pe.tag=J.tag),J.obj===void 0)throw"obj shall be specified for 'tag'.";return pe.obj=R(J.obj),new x(pe)}}},this.jsonToASN1HEX=function(e){var t=this.newObject(e);return t.getEncodedHex()}};j.asn1.ASN1Util.oidHexToInt=function(e){for(var i="",t=parseInt(e.substring(0,2),16),n=Math.floor(t/40),r=t%40,i=n+"."+r,o="",s=2;s<e.length;s+=2){var l=parseInt(e.substring(s,s+2),16),a=("00000000"+l.toString(2)).slice(-8);if(o=o+a.substring(1,8),a.substring(0,1)=="0"){var u=new q(o,2);i=i+"."+u.toString(10),o=""}}return i};j.asn1.ASN1Util.oidIntToHex=function(e){var t=function(l){var a=l.toString(16);return a.length==1&&(a="0"+a),a},n=function(l){var a="",u=new q(l,10),c=u.toString(2),f=7-c.length%7;f==7&&(f=0);for(var h="",y=0;y<f;y++)h+="0";c=h+c;for(var y=0;y<c.length-1;y+=7){var m=c.substring(y,y+7);y!=c.length-7&&(m="1"+m),a+=t(parseInt(m,2))}return a};if(!e.match(/^[0-9.]+$/))throw"malformed oid string: "+e;var r="",i=e.split("."),o=parseInt(i[0])*40+parseInt(i[1]);r+=t(o),i.splice(0,2);for(var s=0;s<i.length;s++)r+=n(i[s]);return r};j.asn1.ASN1Object=function(){var e="";this.getLengthHexFromValue=function(){if(typeof this.hV>"u"||this.hV==null)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+e.length+",v="+this.hV;var t=this.hV.length/2,n=t.toString(16);if(n.length%2==1&&(n="0"+n),t<128)return n;var r=n.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);var i=128+r;return i.toString(16)+n},this.getEncodedHex=function(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}};j.asn1.DERAbstractString=function(e){j.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e=="string"?this.setString(e):typeof e.str<"u"?this.setString(e.str):typeof e.hex<"u"&&this.setStringHex(e.hex))};je(j.asn1.DERAbstractString,j.asn1.ASN1Object);j.asn1.DERAbstractTime=function(e){j.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){utc=t.getTime()+t.getTimezoneOffset()*6e4;var n=new Date(utc);return n},this.formatDate=function(t,n,r){var i=this.zeroPadding,o=this.localDateToUTC(t),s=String(o.getFullYear());n=="utc"&&(s=s.substring(2,4));var l=i(String(o.getMonth()+1),2),a=i(String(o.getDate()),2),u=i(String(o.getHours()),2),c=i(String(o.getMinutes()),2),f=i(String(o.getSeconds()),2),h=s+l+a+u+c+f;if(r===!0){var y=o.getMilliseconds();if(y!=0){var m=i(String(y),3);m=m.replace(/[0]+$/,""),h=h+"."+m}}return h+"Z"},this.zeroPadding=function(t,n){return t.length>=n?t:new Array(n-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,n,r,i,o,s){var l=new Date(Date.UTC(t,n-1,r,i,o,s,0));this.setByDate(l)},this.getFreshValueHex=function(){return this.hV}};je(j.asn1.DERAbstractTime,j.asn1.ASN1Object);j.asn1.DERAbstractStructured=function(e){j.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,typeof e<"u"&&typeof e.array<"u"&&(this.asn1Array=e.array)};je(j.asn1.DERAbstractStructured,j.asn1.ASN1Object);j.asn1.DERBoolean=function(){j.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"};je(j.asn1.DERBoolean,j.asn1.ASN1Object);j.asn1.DERInteger=function(e){j.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=j.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var n=new q(String(t),10);this.setByBigInteger(n)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e.bigint<"u"?this.setByBigInteger(e.bigint):typeof e.int<"u"?this.setByInteger(e.int):typeof e=="number"?this.setByInteger(e):typeof e.hex<"u"&&this.setValueHex(e.hex))};je(j.asn1.DERInteger,j.asn1.ASN1Object);j.asn1.DERBitString=function(e){if(e!==void 0&&typeof e.obj<"u"){var t=j.asn1.ASN1Util.newObject(e.obj);e.hex="00"+t.getEncodedHex()}j.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(n){this.hTLV=null,this.isModified=!0,this.hV=n},this.setUnusedBitsAndHexValue=function(n,r){if(n<0||7<n)throw"unused bits shall be from 0 to 7: u = "+n;var i="0"+n;this.hTLV=null,this.isModified=!0,this.hV=i+r},this.setByBinaryString=function(n){n=n.replace(/0+$/,"");var r=8-n.length%8;r==8&&(r=0);for(var i=0;i<=r;i++)n+="0";for(var o="",i=0;i<n.length-1;i+=8){var s=n.substring(i,i+8),l=parseInt(s,2).toString(16);l.length==1&&(l="0"+l),o+=l}this.hTLV=null,this.isModified=!0,this.hV="0"+r+o},this.setByBooleanArray=function(n){for(var r="",i=0;i<n.length;i++)n[i]==!0?r+="1":r+="0";this.setByBinaryString(r)},this.newFalseArray=function(n){for(var r=new Array(n),i=0;i<n;i++)r[i]=!1;return r},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e=="string"&&e.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(e):typeof e.hex<"u"?this.setHexValueIncludingUnusedBits(e.hex):typeof e.bin<"u"?this.setByBinaryString(e.bin):typeof e.array<"u"&&this.setByBooleanArray(e.array))};je(j.asn1.DERBitString,j.asn1.ASN1Object);j.asn1.DEROctetString=function(e){if(e!==void 0&&typeof e.obj<"u"){var t=j.asn1.ASN1Util.newObject(e.obj);e.hex=t.getEncodedHex()}j.asn1.DEROctetString.superclass.constructor.call(this,e),this.hT="04"};je(j.asn1.DEROctetString,j.asn1.DERAbstractString);j.asn1.DERNull=function(){j.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"};je(j.asn1.DERNull,j.asn1.ASN1Object);j.asn1.DERObjectIdentifier=function(e){var t=function(r){var i=r.toString(16);return i.length==1&&(i="0"+i),i},n=function(r){var i="",o=new q(r,10),s=o.toString(2),l=7-s.length%7;l==7&&(l=0);for(var a="",u=0;u<l;u++)a+="0";s=a+s;for(var u=0;u<s.length-1;u+=7){var c=s.substring(u,u+7);u!=s.length-7&&(c="1"+c),i+=t(parseInt(c,2))}return i};j.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(r){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=r},this.setValueOidString=function(r){if(!r.match(/^[0-9.]+$/))throw"malformed oid string: "+r;var i="",o=r.split("."),s=parseInt(o[0])*40+parseInt(o[1]);i+=t(s),o.splice(0,2);for(var l=0;l<o.length;l++)i+=n(o[l]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(r){var i=j.asn1.x509.OID.name2oid(r);if(i!=="")this.setValueOidString(i);else throw"DERObjectIdentifier oidName undefined: "+r},this.getFreshValueHex=function(){return this.hV},e!==void 0&&(typeof e=="string"?e.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(e):this.setValueName(e):e.oid!==void 0?this.setValueOidString(e.oid):e.hex!==void 0?this.setValueHex(e.hex):e.name!==void 0&&this.setValueName(e.name))};je(j.asn1.DERObjectIdentifier,j.asn1.ASN1Object);j.asn1.DEREnumerated=function(e){j.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=j.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var n=new q(String(t),10);this.setByBigInteger(n)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e.int<"u"?this.setByInteger(e.int):typeof e=="number"?this.setByInteger(e):typeof e.hex<"u"&&this.setValueHex(e.hex))};je(j.asn1.DEREnumerated,j.asn1.ASN1Object);j.asn1.DERUTF8String=function(e){j.asn1.DERUTF8String.superclass.constructor.call(this,e),this.hT="0c"};je(j.asn1.DERUTF8String,j.asn1.DERAbstractString);j.asn1.DERNumericString=function(e){j.asn1.DERNumericString.superclass.constructor.call(this,e),this.hT="12"};je(j.asn1.DERNumericString,j.asn1.DERAbstractString);j.asn1.DERPrintableString=function(e){j.asn1.DERPrintableString.superclass.constructor.call(this,e),this.hT="13"};je(j.asn1.DERPrintableString,j.asn1.DERAbstractString);j.asn1.DERTeletexString=function(e){j.asn1.DERTeletexString.superclass.constructor.call(this,e),this.hT="14"};je(j.asn1.DERTeletexString,j.asn1.DERAbstractString);j.asn1.DERIA5String=function(e){j.asn1.DERIA5String.superclass.constructor.call(this,e),this.hT="16"};je(j.asn1.DERIA5String,j.asn1.DERAbstractString);j.asn1.DERUTCTime=function(e){j.asn1.DERUTCTime.superclass.constructor.call(this,e),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return typeof this.date>"u"&&typeof this.s>"u"&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},e!==void 0&&(e.str!==void 0?this.setString(e.str):typeof e=="string"&&e.match(/^[0-9]{12}Z$/)?this.setString(e):e.hex!==void 0?this.setStringHex(e.hex):e.date!==void 0&&this.setByDate(e.date))};je(j.asn1.DERUTCTime,j.asn1.DERAbstractTime);j.asn1.DERGeneralizedTime=function(e){j.asn1.DERGeneralizedTime.superclass.constructor.call(this,e),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},e!==void 0&&(e.str!==void 0?this.setString(e.str):typeof e=="string"&&e.match(/^[0-9]{14}Z$/)?this.setString(e):e.hex!==void 0?this.setStringHex(e.hex):e.date!==void 0&&this.setByDate(e.date),e.millis===!0&&(this.withMillis=!0))};je(j.asn1.DERGeneralizedTime,j.asn1.DERAbstractTime);j.asn1.DERSequence=function(e){j.asn1.DERSequence.superclass.constructor.call(this,e),this.hT="30",this.getFreshValueHex=function(){for(var t="",n=0;n<this.asn1Array.length;n++){var r=this.asn1Array[n];t+=r.getEncodedHex()}return this.hV=t,this.hV}};je(j.asn1.DERSequence,j.asn1.DERAbstractStructured);j.asn1.DERSet=function(e){j.asn1.DERSet.superclass.constructor.call(this,e),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,n=0;n<this.asn1Array.length;n++){var r=this.asn1Array[n];t.push(r.getEncodedHex())}return this.sortFlag==!0&&t.sort(),this.hV=t.join(""),this.hV},typeof e<"u"&&typeof e.sortflag<"u"&&e.sortflag==!1&&(this.sortFlag=!1)};je(j.asn1.DERSet,j.asn1.DERAbstractStructured);j.asn1.DERTaggedObject=function(e){j.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,n,r){this.hT=n,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,n),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e.tag<"u"&&(this.hT=e.tag),typeof e.explicit<"u"&&(this.isExplicit=e.explicit),typeof e.obj<"u"&&(this.asn1Object=e.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))};je(j.asn1.DERTaggedObject,j.asn1.ASN1Object);var CS=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Hd=function(e){CS(t,e);function t(n){var r=e.call(this)||this;return n&&(typeof n=="string"?r.parseKey(n):(t.hasPrivateKeyProperty(n)||t.hasPublicKeyProperty(n))&&r.parsePropertiesFrom(n)),r}return t.prototype.parseKey=function(n){try{var r=0,i=0,o=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/,s=o.test(n)?Yw.decode(n):mu.unarmor(n),l=eS.decode(s);if(l.sub.length===3&&(l=l.sub[2].sub[0]),l.sub.length===9){r=l.sub[1].getHexStringValue(),this.n=Ee(r,16),i=l.sub[2].getHexStringValue(),this.e=parseInt(i,16);var a=l.sub[3].getHexStringValue();this.d=Ee(a,16);var u=l.sub[4].getHexStringValue();this.p=Ee(u,16);var c=l.sub[5].getHexStringValue();this.q=Ee(c,16);var f=l.sub[6].getHexStringValue();this.dmp1=Ee(f,16);var h=l.sub[7].getHexStringValue();this.dmq1=Ee(h,16);var y=l.sub[8].getHexStringValue();this.coeff=Ee(y,16)}else if(l.sub.length===2)if(l.sub[0].sub){var m=l.sub[1],v=m.sub[0];r=v.sub[0].getHexStringValue(),this.n=Ee(r,16),i=v.sub[1].getHexStringValue(),this.e=parseInt(i,16)}else r=l.sub[0].getHexStringValue(),this.n=Ee(r,16),i=l.sub[1].getHexStringValue(),this.e=parseInt(i,16);else return!1;return!0}catch{return!1}},t.prototype.getPrivateBaseKey=function(){var n={array:[new j.asn1.DERInteger({int:0}),new j.asn1.DERInteger({bigint:this.n}),new j.asn1.DERInteger({int:this.e}),new j.asn1.DERInteger({bigint:this.d}),new j.asn1.DERInteger({bigint:this.p}),new j.asn1.DERInteger({bigint:this.q}),new j.asn1.DERInteger({bigint:this.dmp1}),new j.asn1.DERInteger({bigint:this.dmq1}),new j.asn1.DERInteger({bigint:this.coeff})]},r=new j.asn1.DERSequence(n);return r.getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return Fi(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var n=new j.asn1.DERSequence({array:[new j.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new j.asn1.DERNull]}),r=new j.asn1.DERSequence({array:[new j.asn1.DERInteger({bigint:this.n}),new j.asn1.DERInteger({int:this.e})]}),i=new j.asn1.DERBitString({hex:"00"+r.getEncodedHex()}),o=new j.asn1.DERSequence({array:[n,i]});return o.getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return Fi(this.getPublicBaseKey())},t.wordwrap=function(n,r){if(r=r||64,!n)return n;var i="(.{1,"+r+`})( +|$
?)|(.{1,`+r+"})";return n.match(RegExp(i,"g")).join(`
`)},t.prototype.getPrivateKey=function(){var n=`-----BEGIN RSA PRIVATE KEY-----
`;return n+=t.wordwrap(this.getPrivateBaseKeyB64())+`
`,n+="-----END RSA PRIVATE KEY-----",n},t.prototype.getPublicKey=function(){var n=`-----BEGIN PUBLIC KEY-----
`;return n+=t.wordwrap(this.getPublicBaseKeyB64())+`
`,n+="-----END PUBLIC KEY-----",n},t.hasPublicKeyProperty=function(n){return n=n||{},n.hasOwnProperty("n")&&n.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(n){return n=n||{},n.hasOwnProperty("n")&&n.hasOwnProperty("e")&&n.hasOwnProperty("d")&&n.hasOwnProperty("p")&&n.hasOwnProperty("q")&&n.hasOwnProperty("dmp1")&&n.hasOwnProperty("dmq1")&&n.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(n){this.n=n.n,this.e=n.e,n.hasOwnProperty("d")&&(this.d=n.d,this.p=n.p,this.q=n.q,this.dmp1=n.dmp1,this.dmq1=n.dmq1,this.coeff=n.coeff)},t}(ES),RS={},ua,DS=typeof process<"u"?(ua=RS)===null||ua===void 0?void 0:ua.npm_package_version:void 0,_S=function(){function e(t){t===void 0&&(t={}),this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=t.key||null}return e.prototype.setKey=function(t){t?(this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Hd(t)):!this.key&&this.log&&console.error("A key was not set.")},e.prototype.setPrivateKey=function(t){this.setKey(t)},e.prototype.setPublicKey=function(t){this.setKey(t)},e.prototype.decrypt=function(t){try{return this.getKey().decrypt(Ld(t))}catch{return!1}},e.prototype.encrypt=function(t){try{return Fi(this.getKey().encrypt(t))}catch{return!1}},e.prototype.encryptOAEP=function(t){try{return Fi(this.getKey().encrypt(t,SS))}catch{return!1}},e.prototype.sign=function(t,n,r){n===void 0&&(n=function(i){return i}),r===void 0&&(r="");try{return Fi(this.getKey().sign(t,n,r))}catch{return!1}},e.prototype.signSha256=function(t){return this.sign(t,function(n){return Vd(gu(n))},"sha256")},e.prototype.verify=function(t,n,r){r===void 0&&(r=function(i){return i});try{return this.getKey().verify(t,Ld(n),r)}catch{return!1}},e.prototype.verifySha256=function(t,n){return this.verify(t,n,function(r){return Vd(gu(r))})},e.prototype.getKey=function(t){if(!this.key){if(this.key=new Hd,t&&{}.toString.call(t)==="[object Function]"){this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);return}this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},e.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},e.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},e.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},e.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},e.version=DS,e}();const jS="-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOOUEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVKRNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs2wIDAQAB-----END PUBLIC KEY-----",Kd=e=>{try{const t=new _S;t.setPublicKey(jS);const n=Ww.encode(e);return t.encrypt(n)}catch(t){return console.error("Password encryption failed:",t),!1}},Yr={login:async e=>{var n;const t=Kd(e.password);if(!t)throw new Error("Failed to encrypt password");try{const r=await El.post(Ze.login,{email:e.email,password:t},{headers:{skipToken:!0}}),i=r.data;if(i.code===0){const{data:o}=i,s={email:o.email,nickname:o.nickname,avatar:o.avatar},l=r.headers.authorization;St.setAuthData({token:o.access_token,authorization:l,userInfo:s})}return i}catch(r){if((n=r.response)!=null&&n.data)return r.response.data;throw r}},register:async e=>{const t=Kd(e.password);if(!t)throw new Error("Failed to encrypt password");return ms(Ze.register,{email:e.email,nickname:e.nickname,password:t},{headers:{skipToken:!0}})},logout:async()=>{try{await nr(Ze.logout)}catch(e){console.warn("Logout API call failed:",e)}finally{St.clearAuth()}},getUserInfo:async()=>nr(Ze.user_info),isAuthenticated:()=>St.isAuthenticated(),getStoredUserInfo:()=>St.getUserInfo()},bS=[{name:"Home",href:"/",icon:Y1},{name:"Datasets",href:"/datasets",icon:so},{name:"Chat",href:"/chat",icon:Ws},{name:"Search",href:"/search",icon:ml},{name:"Agents",href:"/agents",icon:oo},{name:"Files",href:"/files",icon:Rm}];function PS(){const e=mo(),t=hr(),[n,r]=D.useState(!1),[i,o]=D.useState(!1),s=Yr.getStoredUserInfo(),l=()=>{r(!n)},a=async()=>{await Yr.logout(),t("/login")};return d.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:d.jsx("div",{className:"container mx-auto px-4",children:d.jsxs("div",{className:"flex items-center justify-between h-16",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:d.jsx("span",{className:"text-white font-bold text-sm",children:"AQ"})}),d.jsx("span",{className:"text-xl font-bold text-gray-900",children:"AgentQuest"})]}),d.jsx("nav",{className:"hidden md:flex items-center space-x-1",children:bS.map(u=>{const c=u.icon,f=e.pathname===u.href;return d.jsxs(km,{to:u.href,className:`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${f?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[d.jsx(c,{className:"w-4 h-4"}),u.name]},u.name)})}),d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("button",{onClick:l,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:n?d.jsx(ix,{className:"w-5 h-5"}):d.jsx(tx,{className:"w-5 h-5"})}),d.jsxs("div",{className:"relative",children:[d.jsxs("button",{onClick:()=>o(!i),className:"flex items-center gap-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[d.jsx("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:d.jsx(qs,{className:"w-4 h-4 text-primary-600"})}),d.jsx("span",{className:"text-sm font-medium hidden sm:block",children:(s==null?void 0:s.nickname)||(s==null?void 0:s.email)||"User"}),d.jsx(Q1,{className:"w-4 h-4"})]}),i&&d.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:[d.jsxs("div",{className:"px-4 py-2 border-b border-gray-100",children:[d.jsx("p",{className:"text-sm font-medium text-gray-900",children:(s==null?void 0:s.nickname)||"User"}),d.jsx("p",{className:"text-xs text-gray-500",children:s==null?void 0:s.email})]}),d.jsxs("button",{onClick:a,className:"w-full flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:[d.jsx(ex,{className:"w-4 h-4"}),"Sign Out"]})]})]})]})]})})})}function AS(){const e=hr();return D.useEffect(()=>{Yr.isAuthenticated()||e("/login")},[e]),Yr.isAuthenticated()?d.jsxs("div",{className:"min-h-screen bg-gray-50",children:[d.jsx(PS,{}),d.jsx("main",{className:"container mx-auto px-4 py-8",children:d.jsx(E1,{})})]}):null}function OS(){const e=hr();return d.jsxs("section",{className:"relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-blue-800 rounded-2xl p-8 text-white",children:[d.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"}),d.jsx("div",{className:"absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"}),d.jsx("div",{className:"absolute bottom-4 left-4 w-16 h-16 bg-purple-300/20 rounded-full blur-lg"}),d.jsxs("div",{className:"relative z-10",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-6",children:[d.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:d.jsx(rx,{className:"w-5 h-5"})}),d.jsx("span",{className:"text-white/90 text-sm font-medium",children:"AI-Powered Knowledge Platform"})]}),d.jsxs("h1",{className:"text-5xl font-bold mb-6 leading-tight",children:["Welcome to"," ",d.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"AgentQuest"})]}),d.jsx("p",{className:"text-xl text-white/90 mb-8 max-w-2xl",children:"Transform your documents into intelligent conversations. Build powerful RAG applications with cutting-edge AI technology."}),d.jsxs("div",{className:"flex flex-wrap gap-4",children:[d.jsxs("button",{onClick:()=>e("/datasets"),className:"flex items-center gap-2 px-6 py-3 bg-white text-primary-600 font-semibold rounded-xl hover:bg-white/90 transition-colors shadow-lg",children:["Get Started",d.jsx(Ks,{className:"w-4 h-4"})]}),d.jsx("button",{onClick:()=>window.open("https://github.com/your-repo/agentquest","_blank"),className:"px-6 py-3 bg-white/20 text-white font-semibold rounded-xl hover:bg-white/30 transition-colors backdrop-blur-sm border border-white/20",children:"Learn More"})]})]})]})}const LS=[{id:1,name:"Product Documentation",documents:45,status:"active"},{id:2,name:"Customer Support",documents:128,status:"active"},{id:3,name:"Technical Manuals",documents:67,status:"processing"}];function MS(){const e=hr();return d.jsxs("section",{children:[d.jsxs("div",{className:"flex items-center justify-between mb-8",children:[d.jsxs("div",{className:"flex items-center gap-4",children:[d.jsx("div",{className:"p-3 bg-orange-100 rounded-xl",children:d.jsx(so,{className:"w-6 h-6 text-orange-600"})}),d.jsxs("div",{children:[d.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Datasets"}),d.jsx("p",{className:"text-gray-600",children:"Manage your knowledge repositories"})]})]}),d.jsxs("button",{onClick:()=>e("/datasets"),className:"flex items-center gap-2 px-4 py-2 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors",children:[d.jsx(go,{className:"w-4 h-4"}),"Create Dataset"]})]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[LS.map(t=>d.jsxs("div",{onClick:()=>e("/datasets"),className:"card hover:shadow-md transition-shadow cursor-pointer group",children:[d.jsxs("div",{className:"flex items-start justify-between mb-4",children:[d.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:d.jsx(so,{className:"w-5 h-5 text-orange-600"})}),d.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${t.status==="active"?"bg-green-100 text-green-700":"bg-yellow-100 text-yellow-700"}`,children:t.status})]}),d.jsx("h3",{className:"font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:t.name}),d.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:[t.documents," documents"]}),d.jsxs("div",{className:"flex items-center text-sm text-primary-600 font-medium",children:["View details",d.jsx(Ks,{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"})]})]},t.id)),d.jsx(km,{to:"/datasets",className:"card border-dashed border-2 border-gray-300 hover:border-primary-400 hover:bg-primary-50 transition-colors flex items-center justify-center min-h-[200px] group",children:d.jsxs("div",{className:"text-center",children:[d.jsx(Ks,{className:"w-8 h-8 text-gray-400 group-hover:text-primary-600 mx-auto mb-2 transition-colors"}),d.jsx("span",{className:"text-gray-600 group-hover:text-primary-600 font-medium transition-colors",children:"View All Datasets"})]})})]})]})}const Wd=[{id:"chat",name:"Chat",icon:Ws,href:"/chat"},{id:"search",name:"Search",icon:ml,href:"/search"},{id:"agents",name:"Agents",icon:oo,href:"/agents"}],IS={chat:[{id:1,name:"Customer Support Bot",messages:1250,status:"active"},{id:2,name:"Product Q&A",messages:890,status:"active"}],search:[{id:1,name:"Document Search",queries:2340,status:"active"},{id:2,name:"Knowledge Base Search",queries:1560,status:"active"}],agents:[{id:1,name:"Sales Assistant",interactions:450,status:"active"},{id:2,name:"Technical Support",interactions:320,status:"draft"}]};function FS(){const[e,t]=D.useState("chat"),n=hr(),r=Wd.find(o=>o.id===e),i=IS[e];return d.jsxs("section",{children:[d.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 mb-8",children:[d.jsxs("div",{className:"flex items-center gap-4",children:[d.jsx("div",{className:"p-3 bg-blue-100 rounded-xl",children:d.jsx(r.icon,{className:"w-6 h-6 text-blue-600"})}),d.jsxs("div",{children:[d.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:r.name}),d.jsx("p",{className:"text-gray-600",children:"Build and manage your AI applications"})]})]}),d.jsxs("div",{className:"flex items-center gap-4",children:[d.jsx("div",{className:"flex bg-gray-100 rounded-lg p-1",children:Wd.map(o=>{const s=o.icon;return d.jsxs("button",{onClick:()=>t(o.id),className:`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${e===o.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[d.jsx(s,{className:"w-4 h-4"}),o.name]},o.id)})}),d.jsx("button",{onClick:()=>n(r.href),className:"px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"View All"})]})]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[i.map(o=>d.jsxs("div",{onClick:()=>n(r.href),className:"card hover:shadow-md transition-shadow cursor-pointer group",children:[d.jsxs("div",{className:"flex items-start justify-between mb-4",children:[d.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:d.jsx(r.icon,{className:"w-5 h-5 text-blue-600"})}),d.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${o.status==="active"?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:o.status})]}),d.jsx("h3",{className:"font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:o.name}),d.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:["messages"in o&&`${o.messages} messages`,"queries"in o&&`${o.queries} queries`,"interactions"in o&&`${o.interactions} interactions`]}),d.jsxs("div",{className:"flex items-center text-sm text-primary-600 font-medium",children:["Open application",d.jsx(Ks,{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"})]})]},o.id)),d.jsx("div",{onClick:()=>n(r.href),className:"card border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-colors flex items-center justify-center min-h-[200px] group cursor-pointer",children:d.jsxs("div",{className:"text-center",children:[d.jsx(r.icon,{className:"w-8 h-8 text-gray-400 group-hover:text-blue-600 mx-auto mb-2 transition-colors"}),d.jsxs("span",{className:"text-gray-600 group-hover:text-blue-600 font-medium transition-colors",children:["Create New ",r.name]})]})})]})]})}function BS(){return d.jsxs("div",{className:"space-y-12",children:[d.jsx(OS,{}),d.jsx(MS,{}),d.jsx(FS,{})]})}const ca={list:async()=>nr(Ze.dataset_list),create:async e=>ms(Ze.dataset,e),get:async e=>nr(Ze.dataset_detail(e)),update:async(e,t)=>Lw(Ze.dataset_rename(e),t),delete:async e=>Rd(Ze.dataset_delete(e)),uploadDocument:async(e,t)=>{const n=new FormData;return n.append("file",t),ms(Ze.dataset_upload(e),n,{headers:{"Content-Type":"multipart/form-data"}})},getDocuments:async e=>nr(Ze.dataset_document_list(e)),deleteDocument:async(e,t)=>Rd(Ze.dataset_document_delete(e,t)),testRetrieval:async(e,t)=>ms(Ze.dataset_retrieval_test(e),{query:t})};function US(){const[e,t]=D.useState([]),[n,r]=D.useState(!0),[i,o]=D.useState(null),[s,l]=D.useState(""),[a,u]=D.useState(!1),[c,f]=D.useState(!1),[h,y]=D.useState(""),[m,v]=D.useState(""),[N,g]=D.useState(!1);D.useEffect(()=>{p()},[]);const p=async()=>{var T,S;try{r(!0),o(null);const _=await ca.list();t(_.data||[])}catch(_){console.error("Failed to load datasets:",_),o(((S=(T=_.response)==null?void 0:T.data)==null?void 0:S.message)||"Failed to load datasets. Please check if the backend is running.")}finally{r(!1)}},x=e.filter(T=>T.name.toLowerCase().includes(s.toLowerCase())),R=async()=>{var T,S;if(h.trim())try{g(!0);const _=await ca.create({name:h.trim(),description:m.trim()||void 0});t([...e,_.data]),y(""),v(""),u(!1)}catch(_){console.error("Failed to create dataset:",_),alert("Failed to create dataset: "+(((S=(T=_.response)==null?void 0:T.data)==null?void 0:S.message)||_.message))}finally{g(!1)}},b=async T=>{var S,_;if(confirm("Are you sure you want to delete this dataset?"))try{await ca.delete(T),t(e.filter(B=>B.id!==T))}catch(B){console.error("Failed to delete dataset:",B),alert("Failed to delete dataset: "+(((_=(S=B.response)==null?void 0:S.data)==null?void 0:_.message)||B.message))}};return n?d.jsxs("div",{className:"space-y-6",children:[d.jsx("div",{className:"flex items-center justify-between",children:d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Datasets"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your knowledge repositories"})]})}),d.jsx("div",{className:"flex items-center justify-center py-12",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),d.jsx("p",{className:"text-gray-600",children:"Loading datasets..."})]})})]}):i?d.jsxs("div",{className:"space-y-6",children:[d.jsx("div",{className:"flex items-center justify-between",children:d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Datasets"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your knowledge repositories"})]})}),d.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(Cm,{className:"w-5 h-5 text-red-500"}),d.jsxs("div",{children:[d.jsx("h3",{className:"font-medium text-red-800",children:"Connection Error"}),d.jsx("p",{className:"text-red-700 mt-1",children:i}),d.jsx("button",{onClick:p,className:"mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Retry"})]})]})})]}):d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Datasets"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your knowledge repositories"})]}),d.jsxs("button",{onClick:()=>u(!0),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[d.jsx(go,{className:"w-4 h-4"}),"Create Dataset"]})]}),d.jsxs("div",{className:"flex items-center gap-4",children:[d.jsxs("div",{className:"relative flex-1 max-w-md",children:[d.jsx(ml,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),d.jsx("input",{type:"text",placeholder:"Search datasets...",value:s,onChange:T=>l(T.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),d.jsxs("button",{onClick:()=>f(!c),className:`flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors ${c?"bg-primary-50 border-primary-300":"border-gray-300 hover:bg-gray-50"}`,children:[d.jsx(J1,{className:"w-4 h-4"}),"Filter"]})]}),c&&d.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[d.jsx("h4",{className:"font-medium mb-3",children:"Filter Options"}),d.jsxs("div",{className:"flex gap-4",children:[d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"checkbox",className:"mr-2"}),"Active datasets"]}),d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"checkbox",className:"mr-2"}),"Processing datasets"]})]})]}),a&&d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsx("h3",{className:"text-lg font-semibold",children:"Create New Dataset"}),d.jsx("button",{onClick:()=>u(!1),children:d.jsx(Qs,{className:"w-5 h-5"})})]}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium mb-2",children:"Dataset Name"}),d.jsx("input",{type:"text",value:h,onChange:T=>y(T.target.value),placeholder:"Enter dataset name...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",disabled:N})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium mb-2",children:"Description (Optional)"}),d.jsx("textarea",{value:m,onChange:T=>v(T.target.value),placeholder:"Enter dataset description...",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",disabled:N})]}),d.jsxs("div",{className:"flex gap-3",children:[d.jsx("button",{onClick:R,disabled:N||!h.trim(),className:"flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:N?"Creating...":"Create Dataset"}),d.jsx("button",{onClick:()=>{u(!1),y(""),v("")},disabled:N,className:"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50",children:"Cancel"})]})]})]})}),x.length>0?d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:x.map(T=>d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[d.jsxs("div",{className:"flex items-start justify-between mb-4",children:[d.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:d.jsx(so,{className:"w-5 h-5 text-orange-600"})}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${T.status==="completed"?"bg-green-100 text-green-700":T.status==="processing"?"bg-yellow-100 text-yellow-700":"bg-red-100 text-red-700"}`,children:T.status}),d.jsx("button",{onClick:()=>b(T.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:d.jsx(Qs,{className:"w-4 h-4"})})]})]}),d.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:T.name}),T.description&&d.jsx("p",{className:"text-sm text-gray-600 mb-2",children:T.description}),d.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[T.document_count," documents"]}),d.jsxs("p",{className:"text-xs text-gray-500",children:["Created: ",new Date(T.created_time).toLocaleDateString()]}),d.jsxs("div",{className:"mt-4 flex gap-2",children:[d.jsxs("button",{className:"flex-1 text-sm bg-primary-50 text-primary-600 py-2 rounded-lg hover:bg-primary-100 transition-colors",children:[d.jsx(G1,{className:"w-4 h-4 inline mr-1"}),"View"]}),d.jsxs("button",{className:"flex-1 text-sm bg-gray-50 text-gray-600 py-2 rounded-lg hover:bg-gray-100 transition-colors",children:[d.jsx(Dm,{className:"w-4 h-4 inline mr-1"}),"Upload"]})]})]},T.id))}):d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[d.jsx(so,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:s?"No datasets found":"No datasets yet"}),d.jsx("p",{className:"text-gray-600 mb-6",children:s?`No datasets match "${s}"`:"Create your first dataset to get started with AgentQuest"}),!s&&d.jsx("button",{onClick:()=>u(!0),className:"btn-primary",children:"Create Your First Dataset"})]})]})}const zS=[{id:1,name:"Customer Support Bot",messages:1250,status:"active",lastUsed:"2 hours ago"},{id:2,name:"Product Q&A",messages:890,status:"active",lastUsed:"1 day ago"},{id:3,name:"Technical Support",messages:456,status:"draft",lastUsed:"3 days ago"}],VS=[{id:1,type:"user",content:"Hello! Can you help me with product information?"},{id:2,type:"bot",content:"Hello! I'd be happy to help you with product information. What specific product are you interested in?"},{id:3,type:"user",content:"I'm looking for information about your pricing plans."},{id:4,type:"bot",content:"We offer several pricing plans to fit different needs. Our basic plan starts at $29/month and includes core features like document processing and basic chat functionality. Would you like me to explain the different tiers?"}];function $S(){const[e,t]=D.useState(zS),[n,r]=D.useState(null),[i,o]=D.useState(!1),[s,l]=D.useState(""),[a,u]=D.useState(VS),[c,f]=D.useState(""),h=()=>{if(s.trim()){const v={id:e.length+1,name:s,messages:0,status:"active",lastUsed:"Just now"};t([...e,v]),l(""),o(!1)}},y=()=>{if(c.trim()){const v={id:a.length+1,type:"user",content:c},N={id:a.length+2,type:"bot",content:"Thank you for your message! This is a demo response. In a real implementation, this would be powered by your AI model and knowledge base."};u([...a,v,N]),f("")}},m=e.find(v=>v.id===n);return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Chat"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage chat applications"})]}),d.jsxs("button",{onClick:()=>o(!0),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[d.jsx(go,{className:"w-4 h-4"}),"Create Chat"]})]}),i&&d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsx("h3",{className:"text-lg font-semibold",children:"Create New Chat"}),d.jsx("button",{onClick:()=>o(!1),children:d.jsx(Qs,{className:"w-5 h-5"})})]}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium mb-2",children:"Chat Application Name"}),d.jsx("input",{type:"text",value:s,onChange:v=>l(v.target.value),placeholder:"Enter chat name...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),d.jsxs("div",{className:"flex gap-3",children:[d.jsx("button",{onClick:h,className:"flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors",children:"Create Chat"}),d.jsx("button",{onClick:()=>o(!1),className:"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"Cancel"})]})]})]})}),n?d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 h-96 flex flex-col",children:[d.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(oo,{className:"w-6 h-6 text-blue-600"}),d.jsxs("div",{children:[d.jsx("h3",{className:"font-semibold",children:m==null?void 0:m.name}),d.jsxs("p",{className:"text-sm text-gray-500",children:[m==null?void 0:m.messages," messages"]})]})]}),d.jsx("button",{onClick:()=>r(null),className:"text-gray-400 hover:text-gray-600",children:d.jsx(Qs,{className:"w-5 h-5"})})]}),d.jsx("div",{className:"flex-1 p-4 overflow-y-auto space-y-4",children:a.map(v=>d.jsx("div",{className:`flex ${v.type==="user"?"justify-end":"justify-start"}`,children:d.jsxs("div",{className:`flex items-start gap-2 max-w-xs ${v.type==="user"?"flex-row-reverse":""}`,children:[d.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${v.type==="user"?"bg-primary-100":"bg-gray-100"}`,children:v.type==="user"?d.jsx(qs,{className:"w-4 h-4"}):d.jsx(oo,{className:"w-4 h-4"})}),d.jsx("div",{className:`p-3 rounded-lg ${v.type==="user"?"bg-primary-600 text-white":"bg-gray-100 text-gray-900"}`,children:v.content})]})},v.id))}),d.jsx("div",{className:"p-4 border-t border-gray-200",children:d.jsxs("div",{className:"flex gap-2",children:[d.jsx("input",{type:"text",value:c,onChange:v=>f(v.target.value),onKeyPress:v=>v.key==="Enter"&&y(),placeholder:"Type your message...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),d.jsx("button",{onClick:y,className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:d.jsx(nx,{className:"w-4 h-4"})})]})})]}):e.length>0?d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(v=>d.jsxs("div",{onClick:()=>r(v.id),className:"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer",children:[d.jsxs("div",{className:"flex items-start justify-between mb-4",children:[d.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:d.jsx(Ws,{className:"w-5 h-5 text-blue-600"})}),d.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${v.status==="active"?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:v.status})]}),d.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:v.name}),d.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[v.messages," messages"]}),d.jsxs("p",{className:"text-xs text-gray-500",children:["Last used: ",v.lastUsed]}),d.jsx("button",{className:"mt-4 w-full text-sm bg-primary-50 text-primary-600 py-2 rounded-lg hover:bg-primary-100 transition-colors",children:"Open Chat"})]},v.id))}):d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[d.jsx(Ws,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No chat applications yet"}),d.jsx("p",{className:"text-gray-600 mb-6",children:"Create your first chat application to start conversations with your data"}),d.jsx("button",{onClick:()=>o(!0),className:"btn-primary",children:"Create Your First Chat"})]})]})}function HS(){return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Search"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage search applications"})]}),d.jsxs("button",{onClick:()=>alert("Create Search - This will open the search application creation form"),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[d.jsx(go,{className:"w-4 h-4"}),"Create Search"]})]}),d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[d.jsx(ml,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No search applications yet"}),d.jsx("p",{className:"text-gray-600 mb-6",children:"Create your first search application to enable intelligent document search"}),d.jsx("button",{onClick:()=>alert("Create Your First Search - This will open the search creation wizard"),className:"btn-primary",children:"Create Your First Search"})]})]})}function KS(){return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Agents"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage AI agents"})]}),d.jsxs("button",{onClick:()=>alert("Create Agent - This will open the AI agent creation form"),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[d.jsx(go,{className:"w-4 h-4"}),"Create Agent"]})]}),d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[d.jsx(oo,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No agents yet"}),d.jsx("p",{className:"text-gray-600 mb-6",children:"Create your first AI agent to automate tasks and workflows"}),d.jsx("button",{onClick:()=>alert("Create Your First Agent - This will open the agent creation wizard"),className:"btn-primary",children:"Create Your First Agent"})]})]})}function WS(){return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Files"}),d.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your documents and files"})]}),d.jsxs("button",{onClick:()=>alert("Upload Files - This will open the file upload dialog"),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[d.jsx(Dm,{className:"w-4 h-4"}),"Upload Files"]})]}),d.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[d.jsx(Rm,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No files yet"}),d.jsx("p",{className:"text-gray-600 mb-6",children:"Upload your first files to start building your knowledge base"}),d.jsx("button",{onClick:()=>alert("Upload Your First Files - This will open the file upload wizard"),className:"btn-primary",children:"Upload Your First Files"})]})]})}function qS(){const e=hr(),[t,n]=D.useState(!0),[r,i]=D.useState(!1),[o,s]=D.useState(null),[l,a]=D.useState({email:"",password:"",nickname:""}),u=f=>{const{name:h,value:y}=f.target;a(m=>({...m,[h]:y})),s(null)},c=async f=>{var h,y;f.preventDefault(),i(!0),s(null);try{if(t){const m=await Yr.login({email:l.email,password:l.password});m.code===0?e("/"):s(m.message||"Login failed")}else{const m=await Yr.register({email:l.email,password:l.password,nickname:l.nickname});m.code===0?(n(!0),a({email:"",password:"",nickname:""}),s(null),alert("Registration successful! Please login.")):s(m.message||"Registration failed")}}catch(m){console.error("Auth error:",m),s(((y=(h=m.response)==null?void 0:h.data)==null?void 0:y.message)||m.message||"An error occurred")}finally{i(!1)}};return d.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4",children:d.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8 w-full max-w-md",children:[d.jsxs("div",{className:"text-center mb-8",children:[d.jsx("div",{className:"mx-auto w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mb-4",children:d.jsx(X1,{className:"w-8 h-8 text-white"})}),d.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"AgentQuest"}),d.jsx("p",{className:"text-gray-600 mt-2",children:t?"Sign in to your account":"Create your account"})]}),o&&d.jsxs("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3",children:[d.jsx(Cm,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),d.jsx("p",{className:"text-red-700 text-sm",children:o})]}),d.jsxs("form",{onSubmit:c,className:"space-y-6",children:[!t&&d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),d.jsxs("div",{className:"relative",children:[d.jsx(qs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),d.jsx("input",{type:"text",name:"nickname",value:l.nickname,onChange:u,required:!t,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter your full name"})]})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),d.jsxs("div",{className:"relative",children:[d.jsx(qs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),d.jsx("input",{type:"email",name:"email",value:l.email,onChange:u,required:!0,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter your email"})]})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),d.jsxs("div",{className:"relative",children:[d.jsx(Z1,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),d.jsx("input",{type:"password",name:"password",value:l.password,onChange:u,required:!0,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter your password"})]})]}),d.jsx("button",{type:"submit",disabled:r,className:"w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium",children:r?"Please wait...":t?"Sign In":"Create Account"})]}),d.jsx("div",{className:"mt-6 text-center",children:d.jsx("button",{onClick:()=>{n(!t),s(null),a({email:"",password:"",nickname:""})},className:"text-primary-600 hover:text-primary-700 font-medium",children:t?"Don't have an account? Sign up":"Already have an account? Sign in"})}),d.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200 text-center",children:d.jsx("p",{className:"text-sm text-gray-500",children:"For testing, you can try creating a new account or use existing credentials."})})]})})}const QS=j1([{path:"/login",element:d.jsx(qS,{})},{path:"/",element:d.jsx(AS,{}),children:[{index:!0,element:d.jsx(BS,{})},{path:"datasets",element:d.jsx(US,{})},{path:"chat",element:d.jsx($S,{})},{path:"search",element:d.jsx(HS,{})},{path:"agents",element:d.jsx(KS,{})},{path:"files",element:d.jsx(WS,{})}]}]),y0={getStatus:async()=>nr(Ze.system_status),getVersion:async()=>nr(Ze.system_version),testConnection:async()=>{try{return await y0.getStatus(),!0}catch(e){return console.error("Backend connection test failed:",e),!1}}};function GS(){const[e,t]=D.useState(null);return D.useEffect(()=>{(async()=>{try{const r=await y0.testConnection();t(r),r?console.log("✅ Backend connection successful"):console.warn("❌ Backend connection failed")}catch(r){console.error("❌ Backend connection test error:",r),t(!1)}})()},[]),D.useEffect(()=>{e!==null&&console.log(`Backend Status: ${e?"Connected":"Disconnected"}`)},[e]),d.jsx(B1,{router:QS})}fa.createRoot(document.getElementById("root")).render(d.jsx(oh.StrictMode,{children:d.jsx(GS,{})}));
//# sourceMappingURL=index-DT0DKPdh.js.map
