function Ad(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const l in r)if(l!=="default"&&!(l in e)){const o=Object.getOwnPropertyDescriptor(r,l);o&&Object.defineProperty(e,l,o.get?o:{enumerable:!0,get:()=>r[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function Fd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var zd={exports:{}},yi={},Ud={exports:{}},Y={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zl=Symbol.for("react.element"),am=Symbol.for("react.portal"),sm=Symbol.for("react.fragment"),um=Symbol.for("react.strict_mode"),cm=Symbol.for("react.profiler"),dm=Symbol.for("react.provider"),fm=Symbol.for("react.context"),hm=Symbol.for("react.forward_ref"),pm=Symbol.for("react.suspense"),mm=Symbol.for("react.memo"),ym=Symbol.for("react.lazy"),Hu=Symbol.iterator;function gm(e){return e===null||typeof e!="object"?null:(e=Hu&&e[Hu]||e["@@iterator"],typeof e=="function"?e:null)}var bd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Id=Object.assign,$d={};function Lr(e,t,n){this.props=e,this.context=t,this.refs=$d,this.updater=n||bd}Lr.prototype.isReactComponent={};Lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Bd(){}Bd.prototype=Lr.prototype;function js(e,t,n){this.props=e,this.context=t,this.refs=$d,this.updater=n||bd}var Rs=js.prototype=new Bd;Rs.constructor=js;Id(Rs,Lr.prototype);Rs.isPureReactComponent=!0;var Vu=Array.isArray,Hd=Object.prototype.hasOwnProperty,_s={current:null},Vd={key:!0,ref:!0,__self:!0,__source:!0};function Wd(e,t,n){var r,l={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Hd.call(t,r)&&!Vd.hasOwnProperty(r)&&(l[r]=t[r]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var s=Array(a),u=0;u<a;u++)s[u]=arguments[u+2];l.children=s}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)l[r]===void 0&&(l[r]=a[r]);return{$$typeof:zl,type:e,key:o,ref:i,props:l,_owner:_s.current}}function vm(e,t){return{$$typeof:zl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ps(e){return typeof e=="object"&&e!==null&&e.$$typeof===zl}function xm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Wu=/\/+/g;function Wi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?xm(""+e.key):t.toString(36)}function So(e,t,n,r,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case zl:case am:i=!0}}if(i)return i=e,l=l(i),e=r===""?"."+Wi(i,0):r,Vu(l)?(n="",e!=null&&(n=e.replace(Wu,"$&/")+"/"),So(l,t,n,"",function(u){return u})):l!=null&&(Ps(l)&&(l=vm(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Wu,"$&/")+"/")+e)),t.push(l)),1;if(i=0,r=r===""?".":r+":",Vu(e))for(var a=0;a<e.length;a++){o=e[a];var s=r+Wi(o,a);i+=So(o,t,n,s,l)}else if(s=gm(e),typeof s=="function")for(e=s.call(e),a=0;!(o=e.next()).done;)o=o.value,s=r+Wi(o,a++),i+=So(o,t,n,s,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function no(e,t,n){if(e==null)return e;var r=[],l=0;return So(e,r,"","",function(o){return t.call(n,o,l++)}),r}function wm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ke={current:null},Eo={transition:null},Sm={ReactCurrentDispatcher:Ke,ReactCurrentBatchConfig:Eo,ReactCurrentOwner:_s};function Kd(){throw Error("act(...) is not supported in production builds of React.")}Y.Children={map:no,forEach:function(e,t,n){no(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return no(e,function(){t++}),t},toArray:function(e){return no(e,function(t){return t})||[]},only:function(e){if(!Ps(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Y.Component=Lr;Y.Fragment=sm;Y.Profiler=cm;Y.PureComponent=js;Y.StrictMode=um;Y.Suspense=pm;Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Sm;Y.act=Kd;Y.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Id({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=_s.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(s in t)Hd.call(t,s)&&!Vd.hasOwnProperty(s)&&(r[s]=t[s]===void 0&&a!==void 0?a[s]:t[s])}var s=arguments.length-2;if(s===1)r.children=n;else if(1<s){a=Array(s);for(var u=0;u<s;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:zl,type:e.type,key:l,ref:o,props:r,_owner:i}};Y.createContext=function(e){return e={$$typeof:fm,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:dm,_context:e},e.Consumer=e};Y.createElement=Wd;Y.createFactory=function(e){var t=Wd.bind(null,e);return t.type=e,t};Y.createRef=function(){return{current:null}};Y.forwardRef=function(e){return{$$typeof:hm,render:e}};Y.isValidElement=Ps;Y.lazy=function(e){return{$$typeof:ym,_payload:{_status:-1,_result:e},_init:wm}};Y.memo=function(e,t){return{$$typeof:mm,type:e,compare:t===void 0?null:t}};Y.startTransition=function(e){var t=Eo.transition;Eo.transition={};try{e()}finally{Eo.transition=t}};Y.unstable_act=Kd;Y.useCallback=function(e,t){return Ke.current.useCallback(e,t)};Y.useContext=function(e){return Ke.current.useContext(e)};Y.useDebugValue=function(){};Y.useDeferredValue=function(e){return Ke.current.useDeferredValue(e)};Y.useEffect=function(e,t){return Ke.current.useEffect(e,t)};Y.useId=function(){return Ke.current.useId()};Y.useImperativeHandle=function(e,t,n){return Ke.current.useImperativeHandle(e,t,n)};Y.useInsertionEffect=function(e,t){return Ke.current.useInsertionEffect(e,t)};Y.useLayoutEffect=function(e,t){return Ke.current.useLayoutEffect(e,t)};Y.useMemo=function(e,t){return Ke.current.useMemo(e,t)};Y.useReducer=function(e,t,n){return Ke.current.useReducer(e,t,n)};Y.useRef=function(e){return Ke.current.useRef(e)};Y.useState=function(e){return Ke.current.useState(e)};Y.useSyncExternalStore=function(e,t,n){return Ke.current.useSyncExternalStore(e,t,n)};Y.useTransition=function(){return Ke.current.useTransition()};Y.version="18.3.1";Ud.exports=Y;var R=Ud.exports;const Qd=Fd(R),Em=Ad({__proto__:null,default:Qd},[R]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km=R,Nm=Symbol.for("react.element"),Cm=Symbol.for("react.fragment"),jm=Object.prototype.hasOwnProperty,Rm=km.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,_m={key:!0,ref:!0,__self:!0,__source:!0};function qd(e,t,n){var r,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)jm.call(t,r)&&!_m.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Nm,type:e,key:o,ref:i,props:l,_owner:Rm.current}}yi.Fragment=Cm;yi.jsx=qd;yi.jsxs=qd;zd.exports=yi;var c=zd.exports,Na={},Yd={exports:{}},st={},Jd={exports:{}},Xd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(D,H){var W=D.length;D.push(H);e:for(;0<W;){var le=W-1>>>1,oe=D[le];if(0<l(oe,H))D[le]=H,D[W]=oe,W=le;else break e}}function n(D){return D.length===0?null:D[0]}function r(D){if(D.length===0)return null;var H=D[0],W=D.pop();if(W!==H){D[0]=W;e:for(var le=0,oe=D.length,xt=oe>>>1;le<xt;){var nt=2*(le+1)-1,Ie=D[nt],$e=nt+1,ct=D[$e];if(0>l(Ie,W))$e<oe&&0>l(ct,Ie)?(D[le]=ct,D[$e]=W,le=$e):(D[le]=Ie,D[nt]=W,le=nt);else if($e<oe&&0>l(ct,W))D[le]=ct,D[$e]=W,le=$e;else break e}}return H}function l(D,H){var W=D.sortIndex-H.sortIndex;return W!==0?W:D.id-H.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var s=[],u=[],d=1,f=null,p=3,S=!1,x=!1,v=!1,C=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(D){for(var H=n(u);H!==null;){if(H.callback===null)r(u);else if(H.startTime<=D)r(u),H.sortIndex=H.expirationTime,t(s,H);else break;H=n(u)}}function j(D){if(v=!1,y(D),!x)if(n(s)!==null)x=!0,Jt(P);else{var H=n(u);H!==null&&Xt(j,H.startTime-D)}}function P(D,H){x=!1,v&&(v=!1,m(_),_=-1),S=!0;var W=p;try{for(y(H),f=n(s);f!==null&&(!(f.expirationTime>H)||D&&!re());){var le=f.callback;if(typeof le=="function"){f.callback=null,p=f.priorityLevel;var oe=le(f.expirationTime<=H);H=e.unstable_now(),typeof oe=="function"?f.callback=oe:f===n(s)&&r(s),y(H)}else r(s);f=n(s)}if(f!==null)var xt=!0;else{var nt=n(u);nt!==null&&Xt(j,nt.startTime-H),xt=!1}return xt}finally{f=null,p=W,S=!1}}var T=!1,w=null,_=-1,$=5,A=-1;function re(){return!(e.unstable_now()-A<$)}function ie(){if(w!==null){var D=e.unstable_now();A=D;var H=!0;try{H=w(!0,D)}finally{H?Ce():(T=!1,w=null)}}else T=!1}var Ce;if(typeof h=="function")Ce=function(){h(ie)};else if(typeof MessageChannel<"u"){var Te=new MessageChannel,vt=Te.port2;Te.port1.onmessage=ie,Ce=function(){vt.postMessage(null)}}else Ce=function(){C(ie,0)};function Jt(D){w=D,T||(T=!0,Ce())}function Xt(D,H){_=C(function(){D(e.unstable_now())},H)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(D){D.callback=null},e.unstable_continueExecution=function(){x||S||(x=!0,Jt(P))},e.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<D?Math.floor(1e3/D):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(D){switch(p){case 1:case 2:case 3:var H=3;break;default:H=p}var W=p;p=H;try{return D()}finally{p=W}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(D,H){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var W=p;p=D;try{return H()}finally{p=W}},e.unstable_scheduleCallback=function(D,H,W){var le=e.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?le+W:le):W=le,D){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=**********;break;case 4:oe=1e4;break;default:oe=5e3}return oe=W+oe,D={id:d++,callback:H,priorityLevel:D,startTime:W,expirationTime:oe,sortIndex:-1},W>le?(D.sortIndex=W,t(u,D),n(s)===null&&D===n(u)&&(v?(m(_),_=-1):v=!0,Xt(j,W-le))):(D.sortIndex=oe,t(s,D),x||S||(x=!0,Jt(P))),D},e.unstable_shouldYield=re,e.unstable_wrapCallback=function(D){var H=p;return function(){var W=p;p=H;try{return D.apply(this,arguments)}finally{p=W}}}})(Xd);Jd.exports=Xd;var Pm=Jd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tm=R,at=Pm;function O(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Gd=new Set,pl={};function qn(e,t){Er(e,t),Er(e+"Capture",t)}function Er(e,t){for(pl[e]=t,e=0;e<t.length;e++)Gd.add(t[e])}var Wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ca=Object.prototype.hasOwnProperty,Lm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ku={},Qu={};function Om(e){return Ca.call(Qu,e)?!0:Ca.call(Ku,e)?!1:Lm.test(e)?Qu[e]=!0:(Ku[e]=!0,!1)}function Dm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Mm(e,t,n,r){if(t===null||typeof t>"u"||Dm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Qe(e,t,n,r,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Me={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Me[e]=new Qe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Me[t]=new Qe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Me[e]=new Qe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Me[e]=new Qe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Me[e]=new Qe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Me[e]=new Qe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Me[e]=new Qe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Me[e]=new Qe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Me[e]=new Qe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ts=/[\-:]([a-z])/g;function Ls(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ts,Ls);Me[t]=new Qe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ts,Ls);Me[t]=new Qe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ts,Ls);Me[t]=new Qe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Me[e]=new Qe(e,1,!1,e.toLowerCase(),null,!1,!1)});Me.xlinkHref=new Qe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Me[e]=new Qe(e,1,!1,e.toLowerCase(),null,!0,!0)});function Os(e,t,n,r){var l=Me.hasOwnProperty(t)?Me[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Mm(t,n,l,r)&&(n=null),r||l===null?Om(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Yt=Tm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ro=Symbol.for("react.element"),lr=Symbol.for("react.portal"),or=Symbol.for("react.fragment"),Ds=Symbol.for("react.strict_mode"),ja=Symbol.for("react.profiler"),Zd=Symbol.for("react.provider"),ef=Symbol.for("react.context"),Ms=Symbol.for("react.forward_ref"),Ra=Symbol.for("react.suspense"),_a=Symbol.for("react.suspense_list"),As=Symbol.for("react.memo"),nn=Symbol.for("react.lazy"),tf=Symbol.for("react.offscreen"),qu=Symbol.iterator;function $r(e){return e===null||typeof e!="object"?null:(e=qu&&e[qu]||e["@@iterator"],typeof e=="function"?e:null)}var me=Object.assign,Ki;function Zr(e){if(Ki===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ki=t&&t[1]||""}return`
`+Ki+e}var Qi=!1;function qi(e,t){if(!e||Qi)return"";Qi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var l=u.stack.split(`
`),o=r.stack.split(`
`),i=l.length-1,a=o.length-1;1<=i&&0<=a&&l[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(l[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||l[i]!==o[a]){var s=`
`+l[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=i&&0<=a);break}}}finally{Qi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Zr(e):""}function Am(e){switch(e.tag){case 5:return Zr(e.type);case 16:return Zr("Lazy");case 13:return Zr("Suspense");case 19:return Zr("SuspenseList");case 0:case 2:case 15:return e=qi(e.type,!1),e;case 11:return e=qi(e.type.render,!1),e;case 1:return e=qi(e.type,!0),e;default:return""}}function Pa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case or:return"Fragment";case lr:return"Portal";case ja:return"Profiler";case Ds:return"StrictMode";case Ra:return"Suspense";case _a:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ef:return(e.displayName||"Context")+".Consumer";case Zd:return(e._context.displayName||"Context")+".Provider";case Ms:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case As:return t=e.displayName||null,t!==null?t:Pa(e.type)||"Memo";case nn:t=e._payload,e=e._init;try{return Pa(e(t))}catch{}}return null}function Fm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Pa(t);case 8:return t===Ds?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function vn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function nf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function zm(e){var t=nf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function lo(e){e._valueTracker||(e._valueTracker=zm(e))}function rf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=nf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function zo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ta(e,t){var n=t.checked;return me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Yu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=vn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function lf(e,t){t=t.checked,t!=null&&Os(e,"checked",t,!1)}function La(e,t){lf(e,t);var n=vn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Oa(e,t.type,n):t.hasOwnProperty("defaultValue")&&Oa(e,t.type,vn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ju(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Oa(e,t,n){(t!=="number"||zo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var el=Array.isArray;function yr(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+vn(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Da(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(O(91));return me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(O(92));if(el(n)){if(1<n.length)throw Error(O(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:vn(n)}}function of(e,t){var n=vn(t.value),r=vn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Gu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function af(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ma(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?af(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var oo,sf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(oo=oo||document.createElement("div"),oo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=oo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ml(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ll={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Um=["Webkit","ms","Moz","O"];Object.keys(ll).forEach(function(e){Um.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ll[t]=ll[e]})});function uf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ll.hasOwnProperty(e)&&ll[e]?(""+t).trim():t+"px"}function cf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=uf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var bm=me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Aa(e,t){if(t){if(bm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(O(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(O(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(O(61))}if(t.style!=null&&typeof t.style!="object")throw Error(O(62))}}function Fa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var za=null;function Fs(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ua=null,gr=null,vr=null;function Zu(e){if(e=Il(e)){if(typeof Ua!="function")throw Error(O(280));var t=e.stateNode;t&&(t=Si(t),Ua(e.stateNode,e.type,t))}}function df(e){gr?vr?vr.push(e):vr=[e]:gr=e}function ff(){if(gr){var e=gr,t=vr;if(vr=gr=null,Zu(e),t)for(e=0;e<t.length;e++)Zu(t[e])}}function hf(e,t){return e(t)}function pf(){}var Yi=!1;function mf(e,t,n){if(Yi)return e(t,n);Yi=!0;try{return hf(e,t,n)}finally{Yi=!1,(gr!==null||vr!==null)&&(pf(),ff())}}function yl(e,t){var n=e.stateNode;if(n===null)return null;var r=Si(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(O(231,t,typeof n));return n}var ba=!1;if(Wt)try{var Br={};Object.defineProperty(Br,"passive",{get:function(){ba=!0}}),window.addEventListener("test",Br,Br),window.removeEventListener("test",Br,Br)}catch{ba=!1}function Im(e,t,n,r,l,o,i,a,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var ol=!1,Uo=null,bo=!1,Ia=null,$m={onError:function(e){ol=!0,Uo=e}};function Bm(e,t,n,r,l,o,i,a,s){ol=!1,Uo=null,Im.apply($m,arguments)}function Hm(e,t,n,r,l,o,i,a,s){if(Bm.apply(this,arguments),ol){if(ol){var u=Uo;ol=!1,Uo=null}else throw Error(O(198));bo||(bo=!0,Ia=u)}}function Yn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function yf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ec(e){if(Yn(e)!==e)throw Error(O(188))}function Vm(e){var t=e.alternate;if(!t){if(t=Yn(e),t===null)throw Error(O(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return ec(l),e;if(o===r)return ec(l),t;o=o.sibling}throw Error(O(188))}if(n.return!==r.return)n=l,r=o;else{for(var i=!1,a=l.child;a;){if(a===n){i=!0,n=l,r=o;break}if(a===r){i=!0,r=l,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,r=l;break}if(a===r){i=!0,r=o,n=l;break}a=a.sibling}if(!i)throw Error(O(189))}}if(n.alternate!==r)throw Error(O(190))}if(n.tag!==3)throw Error(O(188));return n.stateNode.current===n?e:t}function gf(e){return e=Vm(e),e!==null?vf(e):null}function vf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=vf(e);if(t!==null)return t;e=e.sibling}return null}var xf=at.unstable_scheduleCallback,tc=at.unstable_cancelCallback,Wm=at.unstable_shouldYield,Km=at.unstable_requestPaint,we=at.unstable_now,Qm=at.unstable_getCurrentPriorityLevel,zs=at.unstable_ImmediatePriority,wf=at.unstable_UserBlockingPriority,Io=at.unstable_NormalPriority,qm=at.unstable_LowPriority,Sf=at.unstable_IdlePriority,gi=null,Mt=null;function Ym(e){if(Mt&&typeof Mt.onCommitFiberRoot=="function")try{Mt.onCommitFiberRoot(gi,e,void 0,(e.current.flags&128)===128)}catch{}}var Ct=Math.clz32?Math.clz32:Gm,Jm=Math.log,Xm=Math.LN2;function Gm(e){return e>>>=0,e===0?32:31-(Jm(e)/Xm|0)|0}var io=64,ao=4194304;function tl(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function $o(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~l;a!==0?r=tl(a):(o&=i,o!==0&&(r=tl(o)))}else i=n&~l,i!==0?r=tl(i):o!==0&&(r=tl(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ct(t),l=1<<n,r|=e[n],t&=~l;return r}function Zm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ey(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-Ct(o),a=1<<i,s=l[i];s===-1?(!(a&n)||a&r)&&(l[i]=Zm(a,t)):s<=t&&(e.expiredLanes|=a),o&=~a}}function $a(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ef(){var e=io;return io<<=1,!(io&4194240)&&(io=64),e}function Ji(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ul(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ct(t),e[t]=n}function ty(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Ct(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function Us(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ct(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var ne=0;function kf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Nf,bs,Cf,jf,Rf,Ba=!1,so=[],cn=null,dn=null,fn=null,gl=new Map,vl=new Map,ln=[],ny="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function nc(e,t){switch(e){case"focusin":case"focusout":cn=null;break;case"dragenter":case"dragleave":dn=null;break;case"mouseover":case"mouseout":fn=null;break;case"pointerover":case"pointerout":gl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":vl.delete(t.pointerId)}}function Hr(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=Il(t),t!==null&&bs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function ry(e,t,n,r,l){switch(t){case"focusin":return cn=Hr(cn,e,t,n,r,l),!0;case"dragenter":return dn=Hr(dn,e,t,n,r,l),!0;case"mouseover":return fn=Hr(fn,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return gl.set(o,Hr(gl.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,vl.set(o,Hr(vl.get(o)||null,e,t,n,r,l)),!0}return!1}function _f(e){var t=On(e.target);if(t!==null){var n=Yn(t);if(n!==null){if(t=n.tag,t===13){if(t=yf(n),t!==null){e.blockedOn=t,Rf(e.priority,function(){Cf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ko(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ha(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);za=r,n.target.dispatchEvent(r),za=null}else return t=Il(n),t!==null&&bs(t),e.blockedOn=n,!1;t.shift()}return!0}function rc(e,t,n){ko(e)&&n.delete(t)}function ly(){Ba=!1,cn!==null&&ko(cn)&&(cn=null),dn!==null&&ko(dn)&&(dn=null),fn!==null&&ko(fn)&&(fn=null),gl.forEach(rc),vl.forEach(rc)}function Vr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ba||(Ba=!0,at.unstable_scheduleCallback(at.unstable_NormalPriority,ly)))}function xl(e){function t(l){return Vr(l,e)}if(0<so.length){Vr(so[0],e);for(var n=1;n<so.length;n++){var r=so[n];r.blockedOn===e&&(r.blockedOn=null)}}for(cn!==null&&Vr(cn,e),dn!==null&&Vr(dn,e),fn!==null&&Vr(fn,e),gl.forEach(t),vl.forEach(t),n=0;n<ln.length;n++)r=ln[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ln.length&&(n=ln[0],n.blockedOn===null);)_f(n),n.blockedOn===null&&ln.shift()}var xr=Yt.ReactCurrentBatchConfig,Bo=!0;function oy(e,t,n,r){var l=ne,o=xr.transition;xr.transition=null;try{ne=1,Is(e,t,n,r)}finally{ne=l,xr.transition=o}}function iy(e,t,n,r){var l=ne,o=xr.transition;xr.transition=null;try{ne=4,Is(e,t,n,r)}finally{ne=l,xr.transition=o}}function Is(e,t,n,r){if(Bo){var l=Ha(e,t,n,r);if(l===null)ia(e,t,r,Ho,n),nc(e,r);else if(ry(l,e,t,n,r))r.stopPropagation();else if(nc(e,r),t&4&&-1<ny.indexOf(e)){for(;l!==null;){var o=Il(l);if(o!==null&&Nf(o),o=Ha(e,t,n,r),o===null&&ia(e,t,r,Ho,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else ia(e,t,r,null,n)}}var Ho=null;function Ha(e,t,n,r){if(Ho=null,e=Fs(r),e=On(e),e!==null)if(t=Yn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=yf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ho=e,null}function Pf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qm()){case zs:return 1;case wf:return 4;case Io:case qm:return 16;case Sf:return 536870912;default:return 16}default:return 16}}var an=null,$s=null,No=null;function Tf(){if(No)return No;var e,t=$s,n=t.length,r,l="value"in an?an.value:an.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===l[o-r];r++);return No=l.slice(e,1<r?1-r:void 0)}function Co(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function uo(){return!0}function lc(){return!1}function ut(e){function t(n,r,l,o,i){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?uo:lc,this.isPropagationStopped=lc,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=uo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=uo)},persist:function(){},isPersistent:uo}),t}var Or={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bs=ut(Or),bl=me({},Or,{view:0,detail:0}),ay=ut(bl),Xi,Gi,Wr,vi=me({},bl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Wr&&(Wr&&e.type==="mousemove"?(Xi=e.screenX-Wr.screenX,Gi=e.screenY-Wr.screenY):Gi=Xi=0,Wr=e),Xi)},movementY:function(e){return"movementY"in e?e.movementY:Gi}}),oc=ut(vi),sy=me({},vi,{dataTransfer:0}),uy=ut(sy),cy=me({},bl,{relatedTarget:0}),Zi=ut(cy),dy=me({},Or,{animationName:0,elapsedTime:0,pseudoElement:0}),fy=ut(dy),hy=me({},Or,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),py=ut(hy),my=me({},Or,{data:0}),ic=ut(my),yy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},gy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=vy[e])?!!t[e]:!1}function Hs(){return xy}var wy=me({},bl,{key:function(e){if(e.key){var t=yy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Co(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?gy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hs,charCode:function(e){return e.type==="keypress"?Co(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Co(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Sy=ut(wy),Ey=me({},vi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ac=ut(Ey),ky=me({},bl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hs}),Ny=ut(ky),Cy=me({},Or,{propertyName:0,elapsedTime:0,pseudoElement:0}),jy=ut(Cy),Ry=me({},vi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),_y=ut(Ry),Py=[9,13,27,32],Vs=Wt&&"CompositionEvent"in window,il=null;Wt&&"documentMode"in document&&(il=document.documentMode);var Ty=Wt&&"TextEvent"in window&&!il,Lf=Wt&&(!Vs||il&&8<il&&11>=il),sc=" ",uc=!1;function Of(e,t){switch(e){case"keyup":return Py.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Df(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ir=!1;function Ly(e,t){switch(e){case"compositionend":return Df(t);case"keypress":return t.which!==32?null:(uc=!0,sc);case"textInput":return e=t.data,e===sc&&uc?null:e;default:return null}}function Oy(e,t){if(ir)return e==="compositionend"||!Vs&&Of(e,t)?(e=Tf(),No=$s=an=null,ir=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Lf&&t.locale!=="ko"?null:t.data;default:return null}}var Dy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Dy[e.type]:t==="textarea"}function Mf(e,t,n,r){df(r),t=Vo(t,"onChange"),0<t.length&&(n=new Bs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var al=null,wl=null;function My(e){Wf(e,0)}function xi(e){var t=ur(e);if(rf(t))return e}function Ay(e,t){if(e==="change")return t}var Af=!1;if(Wt){var ea;if(Wt){var ta="oninput"in document;if(!ta){var dc=document.createElement("div");dc.setAttribute("oninput","return;"),ta=typeof dc.oninput=="function"}ea=ta}else ea=!1;Af=ea&&(!document.documentMode||9<document.documentMode)}function fc(){al&&(al.detachEvent("onpropertychange",Ff),wl=al=null)}function Ff(e){if(e.propertyName==="value"&&xi(wl)){var t=[];Mf(t,wl,e,Fs(e)),mf(My,t)}}function Fy(e,t,n){e==="focusin"?(fc(),al=t,wl=n,al.attachEvent("onpropertychange",Ff)):e==="focusout"&&fc()}function zy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return xi(wl)}function Uy(e,t){if(e==="click")return xi(t)}function by(e,t){if(e==="input"||e==="change")return xi(t)}function Iy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Rt=typeof Object.is=="function"?Object.is:Iy;function Sl(e,t){if(Rt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!Ca.call(t,l)||!Rt(e[l],t[l]))return!1}return!0}function hc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function pc(e,t){var n=hc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=hc(n)}}function zf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?zf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Uf(){for(var e=window,t=zo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zo(e.document)}return t}function Ws(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function $y(e){var t=Uf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&zf(n.ownerDocument.documentElement,n)){if(r!==null&&Ws(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=pc(n,o);var i=pc(n,r);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var By=Wt&&"documentMode"in document&&11>=document.documentMode,ar=null,Va=null,sl=null,Wa=!1;function mc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Wa||ar==null||ar!==zo(r)||(r=ar,"selectionStart"in r&&Ws(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),sl&&Sl(sl,r)||(sl=r,r=Vo(Va,"onSelect"),0<r.length&&(t=new Bs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ar)))}function co(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var sr={animationend:co("Animation","AnimationEnd"),animationiteration:co("Animation","AnimationIteration"),animationstart:co("Animation","AnimationStart"),transitionend:co("Transition","TransitionEnd")},na={},bf={};Wt&&(bf=document.createElement("div").style,"AnimationEvent"in window||(delete sr.animationend.animation,delete sr.animationiteration.animation,delete sr.animationstart.animation),"TransitionEvent"in window||delete sr.transitionend.transition);function wi(e){if(na[e])return na[e];if(!sr[e])return e;var t=sr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in bf)return na[e]=t[n];return e}var If=wi("animationend"),$f=wi("animationiteration"),Bf=wi("animationstart"),Hf=wi("transitionend"),Vf=new Map,yc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function wn(e,t){Vf.set(e,t),qn(t,[e])}for(var ra=0;ra<yc.length;ra++){var la=yc[ra],Hy=la.toLowerCase(),Vy=la[0].toUpperCase()+la.slice(1);wn(Hy,"on"+Vy)}wn(If,"onAnimationEnd");wn($f,"onAnimationIteration");wn(Bf,"onAnimationStart");wn("dblclick","onDoubleClick");wn("focusin","onFocus");wn("focusout","onBlur");wn(Hf,"onTransitionEnd");Er("onMouseEnter",["mouseout","mouseover"]);Er("onMouseLeave",["mouseout","mouseover"]);Er("onPointerEnter",["pointerout","pointerover"]);Er("onPointerLeave",["pointerout","pointerover"]);qn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));qn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));qn("onBeforeInput",["compositionend","keypress","textInput","paste"]);qn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var nl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wy=new Set("cancel close invalid load scroll toggle".split(" ").concat(nl));function gc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Hm(r,t,void 0,e),e.currentTarget=null}function Wf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],s=a.instance,u=a.currentTarget;if(a=a.listener,s!==o&&l.isPropagationStopped())break e;gc(l,a,u),o=s}else for(i=0;i<r.length;i++){if(a=r[i],s=a.instance,u=a.currentTarget,a=a.listener,s!==o&&l.isPropagationStopped())break e;gc(l,a,u),o=s}}}if(bo)throw e=Ia,bo=!1,Ia=null,e}function ue(e,t){var n=t[Ja];n===void 0&&(n=t[Ja]=new Set);var r=e+"__bubble";n.has(r)||(Kf(t,e,2,!1),n.add(r))}function oa(e,t,n){var r=0;t&&(r|=4),Kf(n,e,r,t)}var fo="_reactListening"+Math.random().toString(36).slice(2);function El(e){if(!e[fo]){e[fo]=!0,Gd.forEach(function(n){n!=="selectionchange"&&(Wy.has(n)||oa(n,!1,e),oa(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[fo]||(t[fo]=!0,oa("selectionchange",!1,t))}}function Kf(e,t,n,r){switch(Pf(t)){case 1:var l=oy;break;case 4:l=iy;break;default:l=Is}n=l.bind(null,t,n,e),l=void 0,!ba||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function ia(e,t,n,r,l){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(i===4)for(i=r.return;i!==null;){var s=i.tag;if((s===3||s===4)&&(s=i.stateNode.containerInfo,s===l||s.nodeType===8&&s.parentNode===l))return;i=i.return}for(;a!==null;){if(i=On(a),i===null)return;if(s=i.tag,s===5||s===6){r=o=i;continue e}a=a.parentNode}}r=r.return}mf(function(){var u=o,d=Fs(n),f=[];e:{var p=Vf.get(e);if(p!==void 0){var S=Bs,x=e;switch(e){case"keypress":if(Co(n)===0)break e;case"keydown":case"keyup":S=Sy;break;case"focusin":x="focus",S=Zi;break;case"focusout":x="blur",S=Zi;break;case"beforeblur":case"afterblur":S=Zi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=oc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=uy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=Ny;break;case If:case $f:case Bf:S=fy;break;case Hf:S=jy;break;case"scroll":S=ay;break;case"wheel":S=_y;break;case"copy":case"cut":case"paste":S=py;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=ac}var v=(t&4)!==0,C=!v&&e==="scroll",m=v?p!==null?p+"Capture":null:p;v=[];for(var h=u,y;h!==null;){y=h;var j=y.stateNode;if(y.tag===5&&j!==null&&(y=j,m!==null&&(j=yl(h,m),j!=null&&v.push(kl(h,j,y)))),C)break;h=h.return}0<v.length&&(p=new S(p,x,null,n,d),f.push({event:p,listeners:v}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",p&&n!==za&&(x=n.relatedTarget||n.fromElement)&&(On(x)||x[Kt]))break e;if((S||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,S?(x=n.relatedTarget||n.toElement,S=u,x=x?On(x):null,x!==null&&(C=Yn(x),x!==C||x.tag!==5&&x.tag!==6)&&(x=null)):(S=null,x=u),S!==x)){if(v=oc,j="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(v=ac,j="onPointerLeave",m="onPointerEnter",h="pointer"),C=S==null?p:ur(S),y=x==null?p:ur(x),p=new v(j,h+"leave",S,n,d),p.target=C,p.relatedTarget=y,j=null,On(d)===u&&(v=new v(m,h+"enter",x,n,d),v.target=y,v.relatedTarget=C,j=v),C=j,S&&x)t:{for(v=S,m=x,h=0,y=v;y;y=nr(y))h++;for(y=0,j=m;j;j=nr(j))y++;for(;0<h-y;)v=nr(v),h--;for(;0<y-h;)m=nr(m),y--;for(;h--;){if(v===m||m!==null&&v===m.alternate)break t;v=nr(v),m=nr(m)}v=null}else v=null;S!==null&&vc(f,p,S,v,!1),x!==null&&C!==null&&vc(f,C,x,v,!0)}}e:{if(p=u?ur(u):window,S=p.nodeName&&p.nodeName.toLowerCase(),S==="select"||S==="input"&&p.type==="file")var P=Ay;else if(cc(p))if(Af)P=by;else{P=zy;var T=Fy}else(S=p.nodeName)&&S.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(P=Uy);if(P&&(P=P(e,u))){Mf(f,P,n,d);break e}T&&T(e,p,u),e==="focusout"&&(T=p._wrapperState)&&T.controlled&&p.type==="number"&&Oa(p,"number",p.value)}switch(T=u?ur(u):window,e){case"focusin":(cc(T)||T.contentEditable==="true")&&(ar=T,Va=u,sl=null);break;case"focusout":sl=Va=ar=null;break;case"mousedown":Wa=!0;break;case"contextmenu":case"mouseup":case"dragend":Wa=!1,mc(f,n,d);break;case"selectionchange":if(By)break;case"keydown":case"keyup":mc(f,n,d)}var w;if(Vs)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else ir?Of(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Lf&&n.locale!=="ko"&&(ir||_!=="onCompositionStart"?_==="onCompositionEnd"&&ir&&(w=Tf()):(an=d,$s="value"in an?an.value:an.textContent,ir=!0)),T=Vo(u,_),0<T.length&&(_=new ic(_,e,null,n,d),f.push({event:_,listeners:T}),w?_.data=w:(w=Df(n),w!==null&&(_.data=w)))),(w=Ty?Ly(e,n):Oy(e,n))&&(u=Vo(u,"onBeforeInput"),0<u.length&&(d=new ic("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=w))}Wf(f,t)})}function kl(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vo(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=yl(e,n),o!=null&&r.unshift(kl(e,o,l)),o=yl(e,t),o!=null&&r.push(kl(e,o,l))),e=e.return}return r}function nr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function vc(e,t,n,r,l){for(var o=t._reactName,i=[];n!==null&&n!==r;){var a=n,s=a.alternate,u=a.stateNode;if(s!==null&&s===r)break;a.tag===5&&u!==null&&(a=u,l?(s=yl(n,o),s!=null&&i.unshift(kl(n,s,a))):l||(s=yl(n,o),s!=null&&i.push(kl(n,s,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Ky=/\r\n?/g,Qy=/\u0000|\uFFFD/g;function xc(e){return(typeof e=="string"?e:""+e).replace(Ky,`
`).replace(Qy,"")}function ho(e,t,n){if(t=xc(t),xc(e)!==t&&n)throw Error(O(425))}function Wo(){}var Ka=null,Qa=null;function qa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ya=typeof setTimeout=="function"?setTimeout:void 0,qy=typeof clearTimeout=="function"?clearTimeout:void 0,wc=typeof Promise=="function"?Promise:void 0,Yy=typeof queueMicrotask=="function"?queueMicrotask:typeof wc<"u"?function(e){return wc.resolve(null).then(e).catch(Jy)}:Ya;function Jy(e){setTimeout(function(){throw e})}function aa(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),xl(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);xl(t)}function hn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Sc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Dr=Math.random().toString(36).slice(2),Dt="__reactFiber$"+Dr,Nl="__reactProps$"+Dr,Kt="__reactContainer$"+Dr,Ja="__reactEvents$"+Dr,Xy="__reactListeners$"+Dr,Gy="__reactHandles$"+Dr;function On(e){var t=e[Dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Kt]||n[Dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Sc(e);e!==null;){if(n=e[Dt])return n;e=Sc(e)}return t}e=n,n=e.parentNode}return null}function Il(e){return e=e[Dt]||e[Kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function ur(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(O(33))}function Si(e){return e[Nl]||null}var Xa=[],cr=-1;function Sn(e){return{current:e}}function ce(e){0>cr||(e.current=Xa[cr],Xa[cr]=null,cr--)}function se(e,t){cr++,Xa[cr]=e.current,e.current=t}var xn={},be=Sn(xn),Xe=Sn(!1),$n=xn;function kr(e,t){var n=e.type.contextTypes;if(!n)return xn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ge(e){return e=e.childContextTypes,e!=null}function Ko(){ce(Xe),ce(be)}function Ec(e,t,n){if(be.current!==xn)throw Error(O(168));se(be,t),se(Xe,n)}function Qf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(O(108,Fm(e)||"Unknown",l));return me({},n,r)}function Qo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||xn,$n=be.current,se(be,e),se(Xe,Xe.current),!0}function kc(e,t,n){var r=e.stateNode;if(!r)throw Error(O(169));n?(e=Qf(e,t,$n),r.__reactInternalMemoizedMergedChildContext=e,ce(Xe),ce(be),se(be,e)):ce(Xe),se(Xe,n)}var It=null,Ei=!1,sa=!1;function qf(e){It===null?It=[e]:It.push(e)}function Zy(e){Ei=!0,qf(e)}function En(){if(!sa&&It!==null){sa=!0;var e=0,t=ne;try{var n=It;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}It=null,Ei=!1}catch(l){throw It!==null&&(It=It.slice(e+1)),xf(zs,En),l}finally{ne=t,sa=!1}}return null}var dr=[],fr=0,qo=null,Yo=0,dt=[],ft=0,Bn=null,$t=1,Bt="";function _n(e,t){dr[fr++]=Yo,dr[fr++]=qo,qo=e,Yo=t}function Yf(e,t,n){dt[ft++]=$t,dt[ft++]=Bt,dt[ft++]=Bn,Bn=e;var r=$t;e=Bt;var l=32-Ct(r)-1;r&=~(1<<l),n+=1;var o=32-Ct(t)+l;if(30<o){var i=l-l%5;o=(r&(1<<i)-1).toString(32),r>>=i,l-=i,$t=1<<32-Ct(t)+l|n<<l|r,Bt=o+e}else $t=1<<o|n<<l|r,Bt=e}function Ks(e){e.return!==null&&(_n(e,1),Yf(e,1,0))}function Qs(e){for(;e===qo;)qo=dr[--fr],dr[fr]=null,Yo=dr[--fr],dr[fr]=null;for(;e===Bn;)Bn=dt[--ft],dt[ft]=null,Bt=dt[--ft],dt[ft]=null,$t=dt[--ft],dt[ft]=null}var it=null,ot=null,fe=!1,Nt=null;function Jf(e,t){var n=pt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Nc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,it=e,ot=hn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,it=e,ot=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Bn!==null?{id:$t,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=pt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,it=e,ot=null,!0):!1;default:return!1}}function Ga(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Za(e){if(fe){var t=ot;if(t){var n=t;if(!Nc(e,t)){if(Ga(e))throw Error(O(418));t=hn(n.nextSibling);var r=it;t&&Nc(e,t)?Jf(r,n):(e.flags=e.flags&-4097|2,fe=!1,it=e)}}else{if(Ga(e))throw Error(O(418));e.flags=e.flags&-4097|2,fe=!1,it=e}}}function Cc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;it=e}function po(e){if(e!==it)return!1;if(!fe)return Cc(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!qa(e.type,e.memoizedProps)),t&&(t=ot)){if(Ga(e))throw Xf(),Error(O(418));for(;t;)Jf(e,t),t=hn(t.nextSibling)}if(Cc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(O(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ot=hn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ot=null}}else ot=it?hn(e.stateNode.nextSibling):null;return!0}function Xf(){for(var e=ot;e;)e=hn(e.nextSibling)}function Nr(){ot=it=null,fe=!1}function qs(e){Nt===null?Nt=[e]:Nt.push(e)}var eg=Yt.ReactCurrentBatchConfig;function Kr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(O(309));var r=n.stateNode}if(!r)throw Error(O(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=l.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(O(284));if(!n._owner)throw Error(O(290,e))}return e}function mo(e,t){throw e=Object.prototype.toString.call(t),Error(O(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function jc(e){var t=e._init;return t(e._payload)}function Gf(e){function t(m,h){if(e){var y=m.deletions;y===null?(m.deletions=[h],m.flags|=16):y.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function l(m,h){return m=gn(m,h),m.index=0,m.sibling=null,m}function o(m,h,y){return m.index=y,e?(y=m.alternate,y!==null?(y=y.index,y<h?(m.flags|=2,h):y):(m.flags|=2,h)):(m.flags|=1048576,h)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,h,y,j){return h===null||h.tag!==6?(h=ma(y,m.mode,j),h.return=m,h):(h=l(h,y),h.return=m,h)}function s(m,h,y,j){var P=y.type;return P===or?d(m,h,y.props.children,j,y.key):h!==null&&(h.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===nn&&jc(P)===h.type)?(j=l(h,y.props),j.ref=Kr(m,h,y),j.return=m,j):(j=Oo(y.type,y.key,y.props,null,m.mode,j),j.ref=Kr(m,h,y),j.return=m,j)}function u(m,h,y,j){return h===null||h.tag!==4||h.stateNode.containerInfo!==y.containerInfo||h.stateNode.implementation!==y.implementation?(h=ya(y,m.mode,j),h.return=m,h):(h=l(h,y.children||[]),h.return=m,h)}function d(m,h,y,j,P){return h===null||h.tag!==7?(h=Un(y,m.mode,j,P),h.return=m,h):(h=l(h,y),h.return=m,h)}function f(m,h,y){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ma(""+h,m.mode,y),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case ro:return y=Oo(h.type,h.key,h.props,null,m.mode,y),y.ref=Kr(m,null,h),y.return=m,y;case lr:return h=ya(h,m.mode,y),h.return=m,h;case nn:var j=h._init;return f(m,j(h._payload),y)}if(el(h)||$r(h))return h=Un(h,m.mode,y,null),h.return=m,h;mo(m,h)}return null}function p(m,h,y,j){var P=h!==null?h.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return P!==null?null:a(m,h,""+y,j);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ro:return y.key===P?s(m,h,y,j):null;case lr:return y.key===P?u(m,h,y,j):null;case nn:return P=y._init,p(m,h,P(y._payload),j)}if(el(y)||$r(y))return P!==null?null:d(m,h,y,j,null);mo(m,y)}return null}function S(m,h,y,j,P){if(typeof j=="string"&&j!==""||typeof j=="number")return m=m.get(y)||null,a(h,m,""+j,P);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case ro:return m=m.get(j.key===null?y:j.key)||null,s(h,m,j,P);case lr:return m=m.get(j.key===null?y:j.key)||null,u(h,m,j,P);case nn:var T=j._init;return S(m,h,y,T(j._payload),P)}if(el(j)||$r(j))return m=m.get(y)||null,d(h,m,j,P,null);mo(h,j)}return null}function x(m,h,y,j){for(var P=null,T=null,w=h,_=h=0,$=null;w!==null&&_<y.length;_++){w.index>_?($=w,w=null):$=w.sibling;var A=p(m,w,y[_],j);if(A===null){w===null&&(w=$);break}e&&w&&A.alternate===null&&t(m,w),h=o(A,h,_),T===null?P=A:T.sibling=A,T=A,w=$}if(_===y.length)return n(m,w),fe&&_n(m,_),P;if(w===null){for(;_<y.length;_++)w=f(m,y[_],j),w!==null&&(h=o(w,h,_),T===null?P=w:T.sibling=w,T=w);return fe&&_n(m,_),P}for(w=r(m,w);_<y.length;_++)$=S(w,m,_,y[_],j),$!==null&&(e&&$.alternate!==null&&w.delete($.key===null?_:$.key),h=o($,h,_),T===null?P=$:T.sibling=$,T=$);return e&&w.forEach(function(re){return t(m,re)}),fe&&_n(m,_),P}function v(m,h,y,j){var P=$r(y);if(typeof P!="function")throw Error(O(150));if(y=P.call(y),y==null)throw Error(O(151));for(var T=P=null,w=h,_=h=0,$=null,A=y.next();w!==null&&!A.done;_++,A=y.next()){w.index>_?($=w,w=null):$=w.sibling;var re=p(m,w,A.value,j);if(re===null){w===null&&(w=$);break}e&&w&&re.alternate===null&&t(m,w),h=o(re,h,_),T===null?P=re:T.sibling=re,T=re,w=$}if(A.done)return n(m,w),fe&&_n(m,_),P;if(w===null){for(;!A.done;_++,A=y.next())A=f(m,A.value,j),A!==null&&(h=o(A,h,_),T===null?P=A:T.sibling=A,T=A);return fe&&_n(m,_),P}for(w=r(m,w);!A.done;_++,A=y.next())A=S(w,m,_,A.value,j),A!==null&&(e&&A.alternate!==null&&w.delete(A.key===null?_:A.key),h=o(A,h,_),T===null?P=A:T.sibling=A,T=A);return e&&w.forEach(function(ie){return t(m,ie)}),fe&&_n(m,_),P}function C(m,h,y,j){if(typeof y=="object"&&y!==null&&y.type===or&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ro:e:{for(var P=y.key,T=h;T!==null;){if(T.key===P){if(P=y.type,P===or){if(T.tag===7){n(m,T.sibling),h=l(T,y.props.children),h.return=m,m=h;break e}}else if(T.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===nn&&jc(P)===T.type){n(m,T.sibling),h=l(T,y.props),h.ref=Kr(m,T,y),h.return=m,m=h;break e}n(m,T);break}else t(m,T);T=T.sibling}y.type===or?(h=Un(y.props.children,m.mode,j,y.key),h.return=m,m=h):(j=Oo(y.type,y.key,y.props,null,m.mode,j),j.ref=Kr(m,h,y),j.return=m,m=j)}return i(m);case lr:e:{for(T=y.key;h!==null;){if(h.key===T)if(h.tag===4&&h.stateNode.containerInfo===y.containerInfo&&h.stateNode.implementation===y.implementation){n(m,h.sibling),h=l(h,y.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=ya(y,m.mode,j),h.return=m,m=h}return i(m);case nn:return T=y._init,C(m,h,T(y._payload),j)}if(el(y))return x(m,h,y,j);if($r(y))return v(m,h,y,j);mo(m,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,h!==null&&h.tag===6?(n(m,h.sibling),h=l(h,y),h.return=m,m=h):(n(m,h),h=ma(y,m.mode,j),h.return=m,m=h),i(m)):n(m,h)}return C}var Cr=Gf(!0),Zf=Gf(!1),Jo=Sn(null),Xo=null,hr=null,Ys=null;function Js(){Ys=hr=Xo=null}function Xs(e){var t=Jo.current;ce(Jo),e._currentValue=t}function es(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function wr(e,t){Xo=e,Ys=hr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Je=!0),e.firstContext=null)}function yt(e){var t=e._currentValue;if(Ys!==e)if(e={context:e,memoizedValue:t,next:null},hr===null){if(Xo===null)throw Error(O(308));hr=e,Xo.dependencies={lanes:0,firstContext:e}}else hr=hr.next=e;return t}var Dn=null;function Gs(e){Dn===null?Dn=[e]:Dn.push(e)}function eh(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Gs(t)):(n.next=l.next,l.next=n),t.interleaved=n,Qt(e,r)}function Qt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var rn=!1;function Zs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function th(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ht(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function pn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,G&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,Qt(e,n)}return l=r.interleaved,l===null?(t.next=t,Gs(r)):(t.next=l.next,l.next=t),r.interleaved=t,Qt(e,n)}function jo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Us(e,n)}}function Rc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Go(e,t,n,r){var l=e.updateQueue;rn=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var s=a,u=s.next;s.next=null,i===null?o=u:i.next=u,i=s;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==i&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=s))}if(o!==null){var f=l.baseState;i=0,d=u=s=null,a=o;do{var p=a.lane,S=a.eventTime;if((r&p)===p){d!==null&&(d=d.next={eventTime:S,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,v=a;switch(p=t,S=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){f=x.call(S,f,p);break e}f=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,p=typeof x=="function"?x.call(S,f,p):x,p==null)break e;f=me({},f,p);break e;case 2:rn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[a]:p.push(a))}else S={eventTime:S,lane:p,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=S,s=f):d=d.next=S,i|=p;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;p=a,a=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(!0);if(d===null&&(s=f),l.baseState=s,l.firstBaseUpdate=u,l.lastBaseUpdate=d,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Vn|=i,e.lanes=i,e.memoizedState=f}}function _c(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(O(191,l));l.call(r)}}}var $l={},At=Sn($l),Cl=Sn($l),jl=Sn($l);function Mn(e){if(e===$l)throw Error(O(174));return e}function eu(e,t){switch(se(jl,t),se(Cl,e),se(At,$l),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ma(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ma(t,e)}ce(At),se(At,t)}function jr(){ce(At),ce(Cl),ce(jl)}function nh(e){Mn(jl.current);var t=Mn(At.current),n=Ma(t,e.type);t!==n&&(se(Cl,e),se(At,n))}function tu(e){Cl.current===e&&(ce(At),ce(Cl))}var he=Sn(0);function Zo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ua=[];function nu(){for(var e=0;e<ua.length;e++)ua[e]._workInProgressVersionPrimary=null;ua.length=0}var Ro=Yt.ReactCurrentDispatcher,ca=Yt.ReactCurrentBatchConfig,Hn=0,pe=null,je=null,_e=null,ei=!1,ul=!1,Rl=0,tg=0;function Ae(){throw Error(O(321))}function ru(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Rt(e[n],t[n]))return!1;return!0}function lu(e,t,n,r,l,o){if(Hn=o,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ro.current=e===null||e.memoizedState===null?og:ig,e=n(r,l),ul){o=0;do{if(ul=!1,Rl=0,25<=o)throw Error(O(301));o+=1,_e=je=null,t.updateQueue=null,Ro.current=ag,e=n(r,l)}while(ul)}if(Ro.current=ti,t=je!==null&&je.next!==null,Hn=0,_e=je=pe=null,ei=!1,t)throw Error(O(300));return e}function ou(){var e=Rl!==0;return Rl=0,e}function Ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return _e===null?pe.memoizedState=_e=e:_e=_e.next=e,_e}function gt(){if(je===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=je.next;var t=_e===null?pe.memoizedState:_e.next;if(t!==null)_e=t,je=e;else{if(e===null)throw Error(O(310));je=e,e={memoizedState:je.memoizedState,baseState:je.baseState,baseQueue:je.baseQueue,queue:je.queue,next:null},_e===null?pe.memoizedState=_e=e:_e=_e.next=e}return _e}function _l(e,t){return typeof t=="function"?t(e):t}function da(e){var t=gt(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=je,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var a=i=null,s=null,u=o;do{var d=u.lane;if((Hn&d)===d)s!==null&&(s=s.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};s===null?(a=s=f,i=r):s=s.next=f,pe.lanes|=d,Vn|=d}u=u.next}while(u!==null&&u!==o);s===null?i=r:s.next=a,Rt(r,t.memoizedState)||(Je=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,pe.lanes|=o,Vn|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function fa(e){var t=gt(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);Rt(o,t.memoizedState)||(Je=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function rh(){}function lh(e,t){var n=pe,r=gt(),l=t(),o=!Rt(r.memoizedState,l);if(o&&(r.memoizedState=l,Je=!0),r=r.queue,iu(ah.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||_e!==null&&_e.memoizedState.tag&1){if(n.flags|=2048,Pl(9,ih.bind(null,n,r,l,t),void 0,null),Pe===null)throw Error(O(349));Hn&30||oh(n,t,l)}return l}function oh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ih(e,t,n,r){t.value=n,t.getSnapshot=r,sh(t)&&uh(e)}function ah(e,t,n){return n(function(){sh(t)&&uh(e)})}function sh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Rt(e,n)}catch{return!0}}function uh(e){var t=Qt(e,1);t!==null&&jt(t,e,1,-1)}function Pc(e){var t=Ot();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:_l,lastRenderedState:e},t.queue=e,e=e.dispatch=lg.bind(null,pe,e),[t.memoizedState,e]}function Pl(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ch(){return gt().memoizedState}function _o(e,t,n,r){var l=Ot();pe.flags|=e,l.memoizedState=Pl(1|t,n,void 0,r===void 0?null:r)}function ki(e,t,n,r){var l=gt();r=r===void 0?null:r;var o=void 0;if(je!==null){var i=je.memoizedState;if(o=i.destroy,r!==null&&ru(r,i.deps)){l.memoizedState=Pl(t,n,o,r);return}}pe.flags|=e,l.memoizedState=Pl(1|t,n,o,r)}function Tc(e,t){return _o(8390656,8,e,t)}function iu(e,t){return ki(2048,8,e,t)}function dh(e,t){return ki(4,2,e,t)}function fh(e,t){return ki(4,4,e,t)}function hh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ph(e,t,n){return n=n!=null?n.concat([e]):null,ki(4,4,hh.bind(null,t,e),n)}function au(){}function mh(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ru(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function yh(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ru(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function gh(e,t,n){return Hn&21?(Rt(n,t)||(n=Ef(),pe.lanes|=n,Vn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Je=!0),e.memoizedState=n)}function ng(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=ca.transition;ca.transition={};try{e(!1),t()}finally{ne=n,ca.transition=r}}function vh(){return gt().memoizedState}function rg(e,t,n){var r=yn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},xh(e))wh(t,n);else if(n=eh(e,t,n,r),n!==null){var l=We();jt(n,e,r,l),Sh(n,t,r)}}function lg(e,t,n){var r=yn(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(xh(e))wh(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(l.hasEagerState=!0,l.eagerState=a,Rt(a,i)){var s=t.interleaved;s===null?(l.next=l,Gs(t)):(l.next=s.next,s.next=l),t.interleaved=l;return}}catch{}finally{}n=eh(e,t,l,r),n!==null&&(l=We(),jt(n,e,r,l),Sh(n,t,r))}}function xh(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function wh(e,t){ul=ei=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Sh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Us(e,n)}}var ti={readContext:yt,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useInsertionEffect:Ae,useLayoutEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useMutableSource:Ae,useSyncExternalStore:Ae,useId:Ae,unstable_isNewReconciler:!1},og={readContext:yt,useCallback:function(e,t){return Ot().memoizedState=[e,t===void 0?null:t],e},useContext:yt,useEffect:Tc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,_o(4194308,4,hh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _o(4194308,4,e,t)},useInsertionEffect:function(e,t){return _o(4,2,e,t)},useMemo:function(e,t){var n=Ot();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ot();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=rg.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Ot();return e={current:e},t.memoizedState=e},useState:Pc,useDebugValue:au,useDeferredValue:function(e){return Ot().memoizedState=e},useTransition:function(){var e=Pc(!1),t=e[0];return e=ng.bind(null,e[1]),Ot().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,l=Ot();if(fe){if(n===void 0)throw Error(O(407));n=n()}else{if(n=t(),Pe===null)throw Error(O(349));Hn&30||oh(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Tc(ah.bind(null,r,o,e),[e]),r.flags|=2048,Pl(9,ih.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ot(),t=Pe.identifierPrefix;if(fe){var n=Bt,r=$t;n=(r&~(1<<32-Ct(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Rl++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=tg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ig={readContext:yt,useCallback:mh,useContext:yt,useEffect:iu,useImperativeHandle:ph,useInsertionEffect:dh,useLayoutEffect:fh,useMemo:yh,useReducer:da,useRef:ch,useState:function(){return da(_l)},useDebugValue:au,useDeferredValue:function(e){var t=gt();return gh(t,je.memoizedState,e)},useTransition:function(){var e=da(_l)[0],t=gt().memoizedState;return[e,t]},useMutableSource:rh,useSyncExternalStore:lh,useId:vh,unstable_isNewReconciler:!1},ag={readContext:yt,useCallback:mh,useContext:yt,useEffect:iu,useImperativeHandle:ph,useInsertionEffect:dh,useLayoutEffect:fh,useMemo:yh,useReducer:fa,useRef:ch,useState:function(){return fa(_l)},useDebugValue:au,useDeferredValue:function(e){var t=gt();return je===null?t.memoizedState=e:gh(t,je.memoizedState,e)},useTransition:function(){var e=fa(_l)[0],t=gt().memoizedState;return[e,t]},useMutableSource:rh,useSyncExternalStore:lh,useId:vh,unstable_isNewReconciler:!1};function St(e,t){if(e&&e.defaultProps){t=me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ts(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ni={isMounted:function(e){return(e=e._reactInternals)?Yn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=We(),l=yn(e),o=Ht(r,l);o.payload=t,n!=null&&(o.callback=n),t=pn(e,o,l),t!==null&&(jt(t,e,l,r),jo(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=We(),l=yn(e),o=Ht(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=pn(e,o,l),t!==null&&(jt(t,e,l,r),jo(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=We(),r=yn(e),l=Ht(n,r);l.tag=2,t!=null&&(l.callback=t),t=pn(e,l,r),t!==null&&(jt(t,e,r,n),jo(t,e,r))}};function Lc(e,t,n,r,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!Sl(n,r)||!Sl(l,o):!0}function Eh(e,t,n){var r=!1,l=xn,o=t.contextType;return typeof o=="object"&&o!==null?o=yt(o):(l=Ge(t)?$n:be.current,r=t.contextTypes,o=(r=r!=null)?kr(e,l):xn),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ni,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Oc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ni.enqueueReplaceState(t,t.state,null)}function ns(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Zs(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=yt(o):(o=Ge(t)?$n:be.current,l.context=kr(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(ts(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Ni.enqueueReplaceState(l,l.state,null),Go(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Rr(e,t){try{var n="",r=t;do n+=Am(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function ha(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function rs(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var sg=typeof WeakMap=="function"?WeakMap:Map;function kh(e,t,n){n=Ht(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ri||(ri=!0,hs=r),rs(e,t)},n}function Nh(e,t,n){n=Ht(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){rs(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){rs(e,t),typeof r!="function"&&(mn===null?mn=new Set([this]):mn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Dc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new sg;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Eg.bind(null,e,t,n),t.then(e,e))}function Mc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ac(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ht(-1,1),t.tag=2,pn(n,t,1))),n.lanes|=1),e)}var ug=Yt.ReactCurrentOwner,Je=!1;function He(e,t,n,r){t.child=e===null?Zf(t,null,n,r):Cr(t,e.child,n,r)}function Fc(e,t,n,r,l){n=n.render;var o=t.ref;return wr(t,l),r=lu(e,t,n,r,o,l),n=ou(),e!==null&&!Je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,qt(e,t,l)):(fe&&n&&Ks(t),t.flags|=1,He(e,t,r,l),t.child)}function zc(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!mu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Ch(e,t,o,r,l)):(e=Oo(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:Sl,n(i,r)&&e.ref===t.ref)return qt(e,t,l)}return t.flags|=1,e=gn(o,r),e.ref=t.ref,e.return=t,t.child=e}function Ch(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(Sl(o,r)&&e.ref===t.ref)if(Je=!1,t.pendingProps=r=o,(e.lanes&l)!==0)e.flags&131072&&(Je=!0);else return t.lanes=e.lanes,qt(e,t,l)}return ls(e,t,n,r,l)}function jh(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},se(mr,rt),rt|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,se(mr,rt),rt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,se(mr,rt),rt|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,se(mr,rt),rt|=r;return He(e,t,l,n),t.child}function Rh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ls(e,t,n,r,l){var o=Ge(n)?$n:be.current;return o=kr(t,o),wr(t,l),n=lu(e,t,n,r,o,l),r=ou(),e!==null&&!Je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,qt(e,t,l)):(fe&&r&&Ks(t),t.flags|=1,He(e,t,n,l),t.child)}function Uc(e,t,n,r,l){if(Ge(n)){var o=!0;Qo(t)}else o=!1;if(wr(t,l),t.stateNode===null)Po(e,t),Eh(t,n,r),ns(t,n,r,l),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var s=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=yt(u):(u=Ge(n)?$n:be.current,u=kr(t,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||s!==u)&&Oc(t,i,r,u),rn=!1;var p=t.memoizedState;i.state=p,Go(t,r,i,l),s=t.memoizedState,a!==r||p!==s||Xe.current||rn?(typeof d=="function"&&(ts(t,n,d,r),s=t.memoizedState),(a=rn||Lc(t,n,a,r,p,s,u))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=u,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,th(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:St(t.type,a),i.props=u,f=t.pendingProps,p=i.context,s=n.contextType,typeof s=="object"&&s!==null?s=yt(s):(s=Ge(n)?$n:be.current,s=kr(t,s));var S=n.getDerivedStateFromProps;(d=typeof S=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||p!==s)&&Oc(t,i,r,s),rn=!1,p=t.memoizedState,i.state=p,Go(t,r,i,l);var x=t.memoizedState;a!==f||p!==x||Xe.current||rn?(typeof S=="function"&&(ts(t,n,S,r),x=t.memoizedState),(u=rn||Lc(t,n,u,r,p,x,s)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,x,s),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,x,s)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),i.props=r,i.state=x,i.context=s,r=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return os(e,t,n,r,o,l)}function os(e,t,n,r,l,o){Rh(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return l&&kc(t,n,!1),qt(e,t,o);r=t.stateNode,ug.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Cr(t,e.child,null,o),t.child=Cr(t,null,a,o)):He(e,t,a,o),t.memoizedState=r.state,l&&kc(t,n,!0),t.child}function _h(e){var t=e.stateNode;t.pendingContext?Ec(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ec(e,t.context,!1),eu(e,t.containerInfo)}function bc(e,t,n,r,l){return Nr(),qs(l),t.flags|=256,He(e,t,n,r),t.child}var is={dehydrated:null,treeContext:null,retryLane:0};function as(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ph(e,t,n){var r=t.pendingProps,l=he.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),se(he,l&1),e===null)return Za(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Ri(i,r,0,null),e=Un(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=as(n),t.memoizedState=is,e):su(t,i));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return cg(e,t,i,r,a,l,n);if(o){o=r.fallback,i=t.mode,l=e.child,a=l.sibling;var s={mode:"hidden",children:r.children};return!(i&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=s,t.deletions=null):(r=gn(l,s),r.subtreeFlags=l.subtreeFlags&14680064),a!==null?o=gn(a,o):(o=Un(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?as(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=is,r}return o=e.child,e=o.sibling,r=gn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function su(e,t){return t=Ri({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function yo(e,t,n,r){return r!==null&&qs(r),Cr(t,e.child,null,n),e=su(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function cg(e,t,n,r,l,o,i){if(n)return t.flags&256?(t.flags&=-257,r=ha(Error(O(422))),yo(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Ri({mode:"visible",children:r.children},l,0,null),o=Un(o,l,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Cr(t,e.child,null,i),t.child.memoizedState=as(i),t.memoizedState=is,o);if(!(t.mode&1))return yo(e,t,i,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(O(419)),r=ha(o,r,void 0),yo(e,t,i,r)}if(a=(i&e.childLanes)!==0,Je||a){if(r=Pe,r!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,Qt(e,l),jt(r,e,l,-1))}return pu(),r=ha(Error(O(421))),yo(e,t,i,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=kg.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,ot=hn(l.nextSibling),it=t,fe=!0,Nt=null,e!==null&&(dt[ft++]=$t,dt[ft++]=Bt,dt[ft++]=Bn,$t=e.id,Bt=e.overflow,Bn=t),t=su(t,r.children),t.flags|=4096,t)}function Ic(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),es(e.return,t,n)}function pa(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function Th(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(He(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ic(e,n,t);else if(e.tag===19)Ic(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(se(he,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Zo(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),pa(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Zo(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}pa(t,!0,n,null,o);break;case"together":pa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Po(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Vn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(O(153));if(t.child!==null){for(e=t.child,n=gn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=gn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function dg(e,t,n){switch(t.tag){case 3:_h(t),Nr();break;case 5:nh(t);break;case 1:Ge(t.type)&&Qo(t);break;case 4:eu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;se(Jo,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(se(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?Ph(e,t,n):(se(he,he.current&1),e=qt(e,t,n),e!==null?e.sibling:null);se(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Th(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),se(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,jh(e,t,n)}return qt(e,t,n)}var Lh,ss,Oh,Dh;Lh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ss=function(){};Oh=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Mn(At.current);var o=null;switch(n){case"input":l=Ta(e,l),r=Ta(e,r),o=[];break;case"select":l=me({},l,{value:void 0}),r=me({},r,{value:void 0}),o=[];break;case"textarea":l=Da(e,l),r=Da(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Wo)}Aa(n,r);var i;n=null;for(u in l)if(!r.hasOwnProperty(u)&&l.hasOwnProperty(u)&&l[u]!=null)if(u==="style"){var a=l[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(pl.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var s=r[u];if(a=l!=null?l[u]:void 0,r.hasOwnProperty(u)&&s!==a&&(s!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||s&&s.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in s)s.hasOwnProperty(i)&&a[i]!==s[i]&&(n||(n={}),n[i]=s[i])}else n||(o||(o=[]),o.push(u,n)),n=s;else u==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,a=a?a.__html:void 0,s!=null&&a!==s&&(o=o||[]).push(u,s)):u==="children"?typeof s!="string"&&typeof s!="number"||(o=o||[]).push(u,""+s):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(pl.hasOwnProperty(u)?(s!=null&&u==="onScroll"&&ue("scroll",e),o||a===s||(o=[])):(o=o||[]).push(u,s))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Dh=function(e,t,n,r){n!==r&&(t.flags|=4)};function Qr(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function fg(e,t,n){var r=t.pendingProps;switch(Qs(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Fe(t),null;case 1:return Ge(t.type)&&Ko(),Fe(t),null;case 3:return r=t.stateNode,jr(),ce(Xe),ce(be),nu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(po(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Nt!==null&&(ys(Nt),Nt=null))),ss(e,t),Fe(t),null;case 5:tu(t);var l=Mn(jl.current);if(n=t.type,e!==null&&t.stateNode!=null)Oh(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(O(166));return Fe(t),null}if(e=Mn(At.current),po(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Dt]=t,r[Nl]=o,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(l=0;l<nl.length;l++)ue(nl[l],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":Yu(r,o),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},ue("invalid",r);break;case"textarea":Xu(r,o),ue("invalid",r)}Aa(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&ho(r.textContent,a,e),l=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&ho(r.textContent,a,e),l=["children",""+a]):pl.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&ue("scroll",r)}switch(n){case"input":lo(r),Ju(r,o,!0);break;case"textarea":lo(r),Gu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Wo)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=af(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Dt]=t,e[Nl]=r,Lh(e,t,!1,!1),t.stateNode=e;e:{switch(i=Fa(n,r),n){case"dialog":ue("cancel",e),ue("close",e),l=r;break;case"iframe":case"object":case"embed":ue("load",e),l=r;break;case"video":case"audio":for(l=0;l<nl.length;l++)ue(nl[l],e);l=r;break;case"source":ue("error",e),l=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),l=r;break;case"details":ue("toggle",e),l=r;break;case"input":Yu(e,r),l=Ta(e,r),ue("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=me({},r,{value:void 0}),ue("invalid",e);break;case"textarea":Xu(e,r),l=Da(e,r),ue("invalid",e);break;default:l=r}Aa(n,l),a=l;for(o in a)if(a.hasOwnProperty(o)){var s=a[o];o==="style"?cf(e,s):o==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&sf(e,s)):o==="children"?typeof s=="string"?(n!=="textarea"||s!=="")&&ml(e,s):typeof s=="number"&&ml(e,""+s):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(pl.hasOwnProperty(o)?s!=null&&o==="onScroll"&&ue("scroll",e):s!=null&&Os(e,o,s,i))}switch(n){case"input":lo(e),Ju(e,r,!1);break;case"textarea":lo(e),Gu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+vn(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?yr(e,!!r.multiple,o,!1):r.defaultValue!=null&&yr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Wo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Fe(t),null;case 6:if(e&&t.stateNode!=null)Dh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(O(166));if(n=Mn(jl.current),Mn(At.current),po(t)){if(r=t.stateNode,n=t.memoizedProps,r[Dt]=t,(o=r.nodeValue!==n)&&(e=it,e!==null))switch(e.tag){case 3:ho(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ho(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Dt]=t,t.stateNode=r}return Fe(t),null;case 13:if(ce(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&ot!==null&&t.mode&1&&!(t.flags&128))Xf(),Nr(),t.flags|=98560,o=!1;else if(o=po(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(O(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(O(317));o[Dt]=t}else Nr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Fe(t),o=!1}else Nt!==null&&(ys(Nt),Nt=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?Re===0&&(Re=3):pu())),t.updateQueue!==null&&(t.flags|=4),Fe(t),null);case 4:return jr(),ss(e,t),e===null&&El(t.stateNode.containerInfo),Fe(t),null;case 10:return Xs(t.type._context),Fe(t),null;case 17:return Ge(t.type)&&Ko(),Fe(t),null;case 19:if(ce(he),o=t.memoizedState,o===null)return Fe(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)Qr(o,!1);else{if(Re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Zo(e),i!==null){for(t.flags|=128,Qr(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return se(he,he.current&1|2),t.child}e=e.sibling}o.tail!==null&&we()>_r&&(t.flags|=128,r=!0,Qr(o,!1),t.lanes=4194304)}else{if(!r)if(e=Zo(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Qr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!fe)return Fe(t),null}else 2*we()-o.renderingStartTime>_r&&n!==1073741824&&(t.flags|=128,r=!0,Qr(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=we(),t.sibling=null,n=he.current,se(he,r?n&1|2:n&1),t):(Fe(t),null);case 22:case 23:return hu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?rt&1073741824&&(Fe(t),t.subtreeFlags&6&&(t.flags|=8192)):Fe(t),null;case 24:return null;case 25:return null}throw Error(O(156,t.tag))}function hg(e,t){switch(Qs(t),t.tag){case 1:return Ge(t.type)&&Ko(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return jr(),ce(Xe),ce(be),nu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return tu(t),null;case 13:if(ce(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(O(340));Nr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(he),null;case 4:return jr(),null;case 10:return Xs(t.type._context),null;case 22:case 23:return hu(),null;case 24:return null;default:return null}}var go=!1,ze=!1,pg=typeof WeakSet=="function"?WeakSet:Set,F=null;function pr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ve(e,t,r)}else n.current=null}function us(e,t,n){try{n()}catch(r){ve(e,t,r)}}var $c=!1;function mg(e,t){if(Ka=Bo,e=Uf(),Ws(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,s=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var S;f!==n||l!==0&&f.nodeType!==3||(a=i+l),f!==o||r!==0&&f.nodeType!==3||(s=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(S=f.firstChild)!==null;)p=f,f=S;for(;;){if(f===e)break t;if(p===n&&++u===l&&(a=i),p===o&&++d===r&&(s=i),(S=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=S}n=a===-1||s===-1?null:{start:a,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(Qa={focusedElem:e,selectionRange:n},Bo=!1,F=t;F!==null;)if(t=F,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,F=e;else for(;F!==null;){t=F;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,C=x.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?v:St(t.type,v),C);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(O(163))}}catch(j){ve(t,t.return,j)}if(e=t.sibling,e!==null){e.return=t.return,F=e;break}F=t.return}return x=$c,$c=!1,x}function cl(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&us(t,n,o)}l=l.next}while(l!==r)}}function Ci(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function cs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Mh(e){var t=e.alternate;t!==null&&(e.alternate=null,Mh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Dt],delete t[Nl],delete t[Ja],delete t[Xy],delete t[Gy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ah(e){return e.tag===5||e.tag===3||e.tag===4}function Bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ah(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ds(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Wo));else if(r!==4&&(e=e.child,e!==null))for(ds(e,t,n),e=e.sibling;e!==null;)ds(e,t,n),e=e.sibling}function fs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(fs(e,t,n),e=e.sibling;e!==null;)fs(e,t,n),e=e.sibling}var Oe=null,Et=!1;function en(e,t,n){for(n=n.child;n!==null;)Fh(e,t,n),n=n.sibling}function Fh(e,t,n){if(Mt&&typeof Mt.onCommitFiberUnmount=="function")try{Mt.onCommitFiberUnmount(gi,n)}catch{}switch(n.tag){case 5:ze||pr(n,t);case 6:var r=Oe,l=Et;Oe=null,en(e,t,n),Oe=r,Et=l,Oe!==null&&(Et?(e=Oe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Oe.removeChild(n.stateNode));break;case 18:Oe!==null&&(Et?(e=Oe,n=n.stateNode,e.nodeType===8?aa(e.parentNode,n):e.nodeType===1&&aa(e,n),xl(e)):aa(Oe,n.stateNode));break;case 4:r=Oe,l=Et,Oe=n.stateNode.containerInfo,Et=!0,en(e,t,n),Oe=r,Et=l;break;case 0:case 11:case 14:case 15:if(!ze&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&us(n,t,i),l=l.next}while(l!==r)}en(e,t,n);break;case 1:if(!ze&&(pr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ve(n,t,a)}en(e,t,n);break;case 21:en(e,t,n);break;case 22:n.mode&1?(ze=(r=ze)||n.memoizedState!==null,en(e,t,n),ze=r):en(e,t,n);break;default:en(e,t,n)}}function Hc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new pg),t.forEach(function(r){var l=Ng.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function wt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Oe=a.stateNode,Et=!1;break e;case 3:Oe=a.stateNode.containerInfo,Et=!0;break e;case 4:Oe=a.stateNode.containerInfo,Et=!0;break e}a=a.return}if(Oe===null)throw Error(O(160));Fh(o,i,l),Oe=null,Et=!1;var s=l.alternate;s!==null&&(s.return=null),l.return=null}catch(u){ve(l,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)zh(t,e),t=t.sibling}function zh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(wt(t,e),Tt(e),r&4){try{cl(3,e,e.return),Ci(3,e)}catch(v){ve(e,e.return,v)}try{cl(5,e,e.return)}catch(v){ve(e,e.return,v)}}break;case 1:wt(t,e),Tt(e),r&512&&n!==null&&pr(n,n.return);break;case 5:if(wt(t,e),Tt(e),r&512&&n!==null&&pr(n,n.return),e.flags&32){var l=e.stateNode;try{ml(l,"")}catch(v){ve(e,e.return,v)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&lf(l,o),Fa(a,i);var u=Fa(a,o);for(i=0;i<s.length;i+=2){var d=s[i],f=s[i+1];d==="style"?cf(l,f):d==="dangerouslySetInnerHTML"?sf(l,f):d==="children"?ml(l,f):Os(l,d,f,u)}switch(a){case"input":La(l,o);break;case"textarea":of(l,o);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var S=o.value;S!=null?yr(l,!!o.multiple,S,!1):p!==!!o.multiple&&(o.defaultValue!=null?yr(l,!!o.multiple,o.defaultValue,!0):yr(l,!!o.multiple,o.multiple?[]:"",!1))}l[Nl]=o}catch(v){ve(e,e.return,v)}}break;case 6:if(wt(t,e),Tt(e),r&4){if(e.stateNode===null)throw Error(O(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(v){ve(e,e.return,v)}}break;case 3:if(wt(t,e),Tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{xl(t.containerInfo)}catch(v){ve(e,e.return,v)}break;case 4:wt(t,e),Tt(e);break;case 13:wt(t,e),Tt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(du=we())),r&4&&Hc(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ze=(u=ze)||d,wt(t,e),ze=u):wt(t,e),Tt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(F=e,d=e.child;d!==null;){for(f=F=d;F!==null;){switch(p=F,S=p.child,p.tag){case 0:case 11:case 14:case 15:cl(4,p,p.return);break;case 1:pr(p,p.return);var x=p.stateNode;if(typeof x.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(v){ve(r,n,v)}}break;case 5:pr(p,p.return);break;case 22:if(p.memoizedState!==null){Wc(f);continue}}S!==null?(S.return=p,F=S):Wc(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{l=f.stateNode,u?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=f.stateNode,s=f.memoizedProps.style,i=s!=null&&s.hasOwnProperty("display")?s.display:null,a.style.display=uf("display",i))}catch(v){ve(e,e.return,v)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){ve(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:wt(t,e),Tt(e),r&4&&Hc(e);break;case 21:break;default:wt(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ah(n)){var r=n;break e}n=n.return}throw Error(O(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(ml(l,""),r.flags&=-33);var o=Bc(e);fs(e,o,l);break;case 3:case 4:var i=r.stateNode.containerInfo,a=Bc(e);ds(e,a,i);break;default:throw Error(O(161))}}catch(s){ve(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function yg(e,t,n){F=e,Uh(e)}function Uh(e,t,n){for(var r=(e.mode&1)!==0;F!==null;){var l=F,o=l.child;if(l.tag===22&&r){var i=l.memoizedState!==null||go;if(!i){var a=l.alternate,s=a!==null&&a.memoizedState!==null||ze;a=go;var u=ze;if(go=i,(ze=s)&&!u)for(F=l;F!==null;)i=F,s=i.child,i.tag===22&&i.memoizedState!==null?Kc(l):s!==null?(s.return=i,F=s):Kc(l);for(;o!==null;)F=o,Uh(o),o=o.sibling;F=l,go=a,ze=u}Vc(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,F=o):Vc(e)}}function Vc(e){for(;F!==null;){var t=F;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ze||Ci(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ze)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:St(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&_c(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}_c(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&xl(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(O(163))}ze||t.flags&512&&cs(t)}catch(p){ve(t,t.return,p)}}if(t===e){F=null;break}if(n=t.sibling,n!==null){n.return=t.return,F=n;break}F=t.return}}function Wc(e){for(;F!==null;){var t=F;if(t===e){F=null;break}var n=t.sibling;if(n!==null){n.return=t.return,F=n;break}F=t.return}}function Kc(e){for(;F!==null;){var t=F;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ci(4,t)}catch(s){ve(t,n,s)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(s){ve(t,l,s)}}var o=t.return;try{cs(t)}catch(s){ve(t,o,s)}break;case 5:var i=t.return;try{cs(t)}catch(s){ve(t,i,s)}}}catch(s){ve(t,t.return,s)}if(t===e){F=null;break}var a=t.sibling;if(a!==null){a.return=t.return,F=a;break}F=t.return}}var gg=Math.ceil,ni=Yt.ReactCurrentDispatcher,uu=Yt.ReactCurrentOwner,mt=Yt.ReactCurrentBatchConfig,G=0,Pe=null,Ne=null,De=0,rt=0,mr=Sn(0),Re=0,Tl=null,Vn=0,ji=0,cu=0,dl=null,Ye=null,du=0,_r=1/0,bt=null,ri=!1,hs=null,mn=null,vo=!1,sn=null,li=0,fl=0,ps=null,To=-1,Lo=0;function We(){return G&6?we():To!==-1?To:To=we()}function yn(e){return e.mode&1?G&2&&De!==0?De&-De:eg.transition!==null?(Lo===0&&(Lo=Ef()),Lo):(e=ne,e!==0||(e=window.event,e=e===void 0?16:Pf(e.type)),e):1}function jt(e,t,n,r){if(50<fl)throw fl=0,ps=null,Error(O(185));Ul(e,n,r),(!(G&2)||e!==Pe)&&(e===Pe&&(!(G&2)&&(ji|=n),Re===4&&on(e,De)),Ze(e,r),n===1&&G===0&&!(t.mode&1)&&(_r=we()+500,Ei&&En()))}function Ze(e,t){var n=e.callbackNode;ey(e,t);var r=$o(e,e===Pe?De:0);if(r===0)n!==null&&tc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&tc(n),t===1)e.tag===0?Zy(Qc.bind(null,e)):qf(Qc.bind(null,e)),Yy(function(){!(G&6)&&En()}),n=null;else{switch(kf(r)){case 1:n=zs;break;case 4:n=wf;break;case 16:n=Io;break;case 536870912:n=Sf;break;default:n=Io}n=Kh(n,bh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function bh(e,t){if(To=-1,Lo=0,G&6)throw Error(O(327));var n=e.callbackNode;if(Sr()&&e.callbackNode!==n)return null;var r=$o(e,e===Pe?De:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=oi(e,r);else{t=r;var l=G;G|=2;var o=$h();(Pe!==e||De!==t)&&(bt=null,_r=we()+500,zn(e,t));do try{wg();break}catch(a){Ih(e,a)}while(!0);Js(),ni.current=o,G=l,Ne!==null?t=0:(Pe=null,De=0,t=Re)}if(t!==0){if(t===2&&(l=$a(e),l!==0&&(r=l,t=ms(e,l))),t===1)throw n=Tl,zn(e,0),on(e,r),Ze(e,we()),n;if(t===6)on(e,r);else{if(l=e.current.alternate,!(r&30)&&!vg(l)&&(t=oi(e,r),t===2&&(o=$a(e),o!==0&&(r=o,t=ms(e,o))),t===1))throw n=Tl,zn(e,0),on(e,r),Ze(e,we()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(O(345));case 2:Pn(e,Ye,bt);break;case 3:if(on(e,r),(r&130023424)===r&&(t=du+500-we(),10<t)){if($o(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){We(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Ya(Pn.bind(null,e,Ye,bt),t);break}Pn(e,Ye,bt);break;case 4:if(on(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var i=31-Ct(r);o=1<<i,i=t[i],i>l&&(l=i),r&=~o}if(r=l,r=we()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*gg(r/1960))-r,10<r){e.timeoutHandle=Ya(Pn.bind(null,e,Ye,bt),r);break}Pn(e,Ye,bt);break;case 5:Pn(e,Ye,bt);break;default:throw Error(O(329))}}}return Ze(e,we()),e.callbackNode===n?bh.bind(null,e):null}function ms(e,t){var n=dl;return e.current.memoizedState.isDehydrated&&(zn(e,t).flags|=256),e=oi(e,t),e!==2&&(t=Ye,Ye=n,t!==null&&ys(t)),e}function ys(e){Ye===null?Ye=e:Ye.push.apply(Ye,e)}function vg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!Rt(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function on(e,t){for(t&=~cu,t&=~ji,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ct(t),r=1<<n;e[n]=-1,t&=~r}}function Qc(e){if(G&6)throw Error(O(327));Sr();var t=$o(e,0);if(!(t&1))return Ze(e,we()),null;var n=oi(e,t);if(e.tag!==0&&n===2){var r=$a(e);r!==0&&(t=r,n=ms(e,r))}if(n===1)throw n=Tl,zn(e,0),on(e,t),Ze(e,we()),n;if(n===6)throw Error(O(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Pn(e,Ye,bt),Ze(e,we()),null}function fu(e,t){var n=G;G|=1;try{return e(t)}finally{G=n,G===0&&(_r=we()+500,Ei&&En())}}function Wn(e){sn!==null&&sn.tag===0&&!(G&6)&&Sr();var t=G;G|=1;var n=mt.transition,r=ne;try{if(mt.transition=null,ne=1,e)return e()}finally{ne=r,mt.transition=n,G=t,!(G&6)&&En()}}function hu(){rt=mr.current,ce(mr)}function zn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,qy(n)),Ne!==null)for(n=Ne.return;n!==null;){var r=n;switch(Qs(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ko();break;case 3:jr(),ce(Xe),ce(be),nu();break;case 5:tu(r);break;case 4:jr();break;case 13:ce(he);break;case 19:ce(he);break;case 10:Xs(r.type._context);break;case 22:case 23:hu()}n=n.return}if(Pe=e,Ne=e=gn(e.current,null),De=rt=t,Re=0,Tl=null,cu=ji=Vn=0,Ye=dl=null,Dn!==null){for(t=0;t<Dn.length;t++)if(n=Dn[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,r.next=i}n.pending=r}Dn=null}return e}function Ih(e,t){do{var n=Ne;try{if(Js(),Ro.current=ti,ei){for(var r=pe.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}ei=!1}if(Hn=0,_e=je=pe=null,ul=!1,Rl=0,uu.current=null,n===null||n.return===null){Re=1,Tl=t,Ne=null;break}e:{var o=e,i=n.return,a=n,s=t;if(t=De,a.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var u=s,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var S=Mc(i);if(S!==null){S.flags&=-257,Ac(S,i,a,o,t),S.mode&1&&Dc(o,u,t),t=S,s=u;var x=t.updateQueue;if(x===null){var v=new Set;v.add(s),t.updateQueue=v}else x.add(s);break e}else{if(!(t&1)){Dc(o,u,t),pu();break e}s=Error(O(426))}}else if(fe&&a.mode&1){var C=Mc(i);if(C!==null){!(C.flags&65536)&&(C.flags|=256),Ac(C,i,a,o,t),qs(Rr(s,a));break e}}o=s=Rr(s,a),Re!==4&&(Re=2),dl===null?dl=[o]:dl.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=kh(o,s,t);Rc(o,m);break e;case 1:a=s;var h=o.type,y=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(mn===null||!mn.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t;var j=Nh(o,a,t);Rc(o,j);break e}}o=o.return}while(o!==null)}Hh(n)}catch(P){t=P,Ne===n&&n!==null&&(Ne=n=n.return);continue}break}while(!0)}function $h(){var e=ni.current;return ni.current=ti,e===null?ti:e}function pu(){(Re===0||Re===3||Re===2)&&(Re=4),Pe===null||!(Vn&268435455)&&!(ji&268435455)||on(Pe,De)}function oi(e,t){var n=G;G|=2;var r=$h();(Pe!==e||De!==t)&&(bt=null,zn(e,t));do try{xg();break}catch(l){Ih(e,l)}while(!0);if(Js(),G=n,ni.current=r,Ne!==null)throw Error(O(261));return Pe=null,De=0,Re}function xg(){for(;Ne!==null;)Bh(Ne)}function wg(){for(;Ne!==null&&!Wm();)Bh(Ne)}function Bh(e){var t=Wh(e.alternate,e,rt);e.memoizedProps=e.pendingProps,t===null?Hh(e):Ne=t,uu.current=null}function Hh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=hg(n,t),n!==null){n.flags&=32767,Ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Re=6,Ne=null;return}}else if(n=fg(n,t,rt),n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Re===0&&(Re=5)}function Pn(e,t,n){var r=ne,l=mt.transition;try{mt.transition=null,ne=1,Sg(e,t,n,r)}finally{mt.transition=l,ne=r}return null}function Sg(e,t,n,r){do Sr();while(sn!==null);if(G&6)throw Error(O(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(O(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(ty(e,o),e===Pe&&(Ne=Pe=null,De=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||vo||(vo=!0,Kh(Io,function(){return Sr(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=mt.transition,mt.transition=null;var i=ne;ne=1;var a=G;G|=4,uu.current=null,mg(e,n),zh(n,e),$y(Qa),Bo=!!Ka,Qa=Ka=null,e.current=n,yg(n),Km(),G=a,ne=i,mt.transition=o}else e.current=n;if(vo&&(vo=!1,sn=e,li=l),o=e.pendingLanes,o===0&&(mn=null),Ym(n.stateNode),Ze(e,we()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(ri)throw ri=!1,e=hs,hs=null,e;return li&1&&e.tag!==0&&Sr(),o=e.pendingLanes,o&1?e===ps?fl++:(fl=0,ps=e):fl=0,En(),null}function Sr(){if(sn!==null){var e=kf(li),t=mt.transition,n=ne;try{if(mt.transition=null,ne=16>e?16:e,sn===null)var r=!1;else{if(e=sn,sn=null,li=0,G&6)throw Error(O(331));var l=G;for(G|=4,F=e.current;F!==null;){var o=F,i=o.child;if(F.flags&16){var a=o.deletions;if(a!==null){for(var s=0;s<a.length;s++){var u=a[s];for(F=u;F!==null;){var d=F;switch(d.tag){case 0:case 11:case 15:cl(8,d,o)}var f=d.child;if(f!==null)f.return=d,F=f;else for(;F!==null;){d=F;var p=d.sibling,S=d.return;if(Mh(d),d===u){F=null;break}if(p!==null){p.return=S,F=p;break}F=S}}}var x=o.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var C=v.sibling;v.sibling=null,v=C}while(v!==null)}}F=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,F=i;else e:for(;F!==null;){if(o=F,o.flags&2048)switch(o.tag){case 0:case 11:case 15:cl(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,F=m;break e}F=o.return}}var h=e.current;for(F=h;F!==null;){i=F;var y=i.child;if(i.subtreeFlags&2064&&y!==null)y.return=i,F=y;else e:for(i=h;F!==null;){if(a=F,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ci(9,a)}}catch(P){ve(a,a.return,P)}if(a===i){F=null;break e}var j=a.sibling;if(j!==null){j.return=a.return,F=j;break e}F=a.return}}if(G=l,En(),Mt&&typeof Mt.onPostCommitFiberRoot=="function")try{Mt.onPostCommitFiberRoot(gi,e)}catch{}r=!0}return r}finally{ne=n,mt.transition=t}}return!1}function qc(e,t,n){t=Rr(n,t),t=kh(e,t,1),e=pn(e,t,1),t=We(),e!==null&&(Ul(e,1,t),Ze(e,t))}function ve(e,t,n){if(e.tag===3)qc(e,e,n);else for(;t!==null;){if(t.tag===3){qc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(mn===null||!mn.has(r))){e=Rr(n,e),e=Nh(t,e,1),t=pn(t,e,1),e=We(),t!==null&&(Ul(t,1,e),Ze(t,e));break}}t=t.return}}function Eg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=We(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(De&n)===n&&(Re===4||Re===3&&(De&130023424)===De&&500>we()-du?zn(e,0):cu|=n),Ze(e,t)}function Vh(e,t){t===0&&(e.mode&1?(t=ao,ao<<=1,!(ao&130023424)&&(ao=4194304)):t=1);var n=We();e=Qt(e,t),e!==null&&(Ul(e,t,n),Ze(e,n))}function kg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Vh(e,n)}function Ng(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(O(314))}r!==null&&r.delete(t),Vh(e,n)}var Wh;Wh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Xe.current)Je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Je=!1,dg(e,t,n);Je=!!(e.flags&131072)}else Je=!1,fe&&t.flags&1048576&&Yf(t,Yo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Po(e,t),e=t.pendingProps;var l=kr(t,be.current);wr(t,n),l=lu(null,t,r,e,l,n);var o=ou();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ge(r)?(o=!0,Qo(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Zs(t),l.updater=Ni,t.stateNode=l,l._reactInternals=t,ns(t,r,e,n),t=os(null,t,r,!0,o,n)):(t.tag=0,fe&&o&&Ks(t),He(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Po(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=jg(r),e=St(r,e),l){case 0:t=ls(null,t,r,e,n);break e;case 1:t=Uc(null,t,r,e,n);break e;case 11:t=Fc(null,t,r,e,n);break e;case 14:t=zc(null,t,r,St(r.type,e),n);break e}throw Error(O(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:St(r,l),ls(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:St(r,l),Uc(e,t,r,l,n);case 3:e:{if(_h(t),e===null)throw Error(O(387));r=t.pendingProps,o=t.memoizedState,l=o.element,th(e,t),Go(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Rr(Error(O(423)),t),t=bc(e,t,r,n,l);break e}else if(r!==l){l=Rr(Error(O(424)),t),t=bc(e,t,r,n,l);break e}else for(ot=hn(t.stateNode.containerInfo.firstChild),it=t,fe=!0,Nt=null,n=Zf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Nr(),r===l){t=qt(e,t,n);break e}He(e,t,r,n)}t=t.child}return t;case 5:return nh(t),e===null&&Za(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,qa(r,l)?i=null:o!==null&&qa(r,o)&&(t.flags|=32),Rh(e,t),He(e,t,i,n),t.child;case 6:return e===null&&Za(t),null;case 13:return Ph(e,t,n);case 4:return eu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Cr(t,null,r,n):He(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:St(r,l),Fc(e,t,r,l,n);case 7:return He(e,t,t.pendingProps,n),t.child;case 8:return He(e,t,t.pendingProps.children,n),t.child;case 12:return He(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,se(Jo,r._currentValue),r._currentValue=i,o!==null)if(Rt(o.value,i)){if(o.children===l.children&&!Xe.current){t=qt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var s=a.firstContext;s!==null;){if(s.context===r){if(o.tag===1){s=Ht(-1,n&-n),s.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?s.next=s:(s.next=d.next,d.next=s),u.pending=s}}o.lanes|=n,s=o.alternate,s!==null&&(s.lanes|=n),es(o.return,n,t),a.lanes|=n;break}s=s.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(O(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),es(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}He(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,wr(t,n),l=yt(l),r=r(l),t.flags|=1,He(e,t,r,n),t.child;case 14:return r=t.type,l=St(r,t.pendingProps),l=St(r.type,l),zc(e,t,r,l,n);case 15:return Ch(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:St(r,l),Po(e,t),t.tag=1,Ge(r)?(e=!0,Qo(t)):e=!1,wr(t,n),Eh(t,r,l),ns(t,r,l,n),os(null,t,r,!0,e,n);case 19:return Th(e,t,n);case 22:return jh(e,t,n)}throw Error(O(156,t.tag))};function Kh(e,t){return xf(e,t)}function Cg(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pt(e,t,n,r){return new Cg(e,t,n,r)}function mu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function jg(e){if(typeof e=="function")return mu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ms)return 11;if(e===As)return 14}return 2}function gn(e,t){var n=e.alternate;return n===null?(n=pt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Oo(e,t,n,r,l,o){var i=2;if(r=e,typeof e=="function")mu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case or:return Un(n.children,l,o,t);case Ds:i=8,l|=8;break;case ja:return e=pt(12,n,t,l|2),e.elementType=ja,e.lanes=o,e;case Ra:return e=pt(13,n,t,l),e.elementType=Ra,e.lanes=o,e;case _a:return e=pt(19,n,t,l),e.elementType=_a,e.lanes=o,e;case tf:return Ri(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Zd:i=10;break e;case ef:i=9;break e;case Ms:i=11;break e;case As:i=14;break e;case nn:i=16,r=null;break e}throw Error(O(130,e==null?e:typeof e,""))}return t=pt(i,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function Un(e,t,n,r){return e=pt(7,e,r,t),e.lanes=n,e}function Ri(e,t,n,r){return e=pt(22,e,r,t),e.elementType=tf,e.lanes=n,e.stateNode={isHidden:!1},e}function ma(e,t,n){return e=pt(6,e,null,t),e.lanes=n,e}function ya(e,t,n){return t=pt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Rg(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ji(0),this.expirationTimes=Ji(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ji(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function yu(e,t,n,r,l,o,i,a,s){return e=new Rg(e,t,n,a,s),t===1?(t=1,o===!0&&(t|=8)):t=0,o=pt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zs(o),e}function _g(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:lr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Qh(e){if(!e)return xn;e=e._reactInternals;e:{if(Yn(e)!==e||e.tag!==1)throw Error(O(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ge(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(O(171))}if(e.tag===1){var n=e.type;if(Ge(n))return Qf(e,n,t)}return t}function qh(e,t,n,r,l,o,i,a,s){return e=yu(n,r,!0,e,l,o,i,a,s),e.context=Qh(null),n=e.current,r=We(),l=yn(n),o=Ht(r,l),o.callback=t??null,pn(n,o,l),e.current.lanes=l,Ul(e,l,r),Ze(e,r),e}function _i(e,t,n,r){var l=t.current,o=We(),i=yn(l);return n=Qh(n),t.context===null?t.context=n:t.pendingContext=n,t=Ht(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=pn(l,t,i),e!==null&&(jt(e,l,i,o),jo(e,l,i)),i}function ii(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Yc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function gu(e,t){Yc(e,t),(e=e.alternate)&&Yc(e,t)}function Pg(){return null}var Yh=typeof reportError=="function"?reportError:function(e){console.error(e)};function vu(e){this._internalRoot=e}Pi.prototype.render=vu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(O(409));_i(e,t,null,null)};Pi.prototype.unmount=vu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Wn(function(){_i(null,e,null,null)}),t[Kt]=null}};function Pi(e){this._internalRoot=e}Pi.prototype.unstable_scheduleHydration=function(e){if(e){var t=jf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ln.length&&t!==0&&t<ln[n].priority;n++);ln.splice(n,0,e),n===0&&_f(e)}};function xu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ti(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Jc(){}function Tg(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var u=ii(i);o.call(u)}}var i=qh(t,r,e,0,null,!1,!1,"",Jc);return e._reactRootContainer=i,e[Kt]=i.current,El(e.nodeType===8?e.parentNode:e),Wn(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var a=r;r=function(){var u=ii(s);a.call(u)}}var s=yu(e,0,!1,null,null,!1,!1,"",Jc);return e._reactRootContainer=s,e[Kt]=s.current,El(e.nodeType===8?e.parentNode:e),Wn(function(){_i(t,s,n,r)}),s}function Li(e,t,n,r,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var a=l;l=function(){var s=ii(i);a.call(s)}}_i(t,i,e,l)}else i=Tg(n,t,e,l,r);return ii(i)}Nf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=tl(t.pendingLanes);n!==0&&(Us(t,n|1),Ze(t,we()),!(G&6)&&(_r=we()+500,En()))}break;case 13:Wn(function(){var r=Qt(e,1);if(r!==null){var l=We();jt(r,e,1,l)}}),gu(e,1)}};bs=function(e){if(e.tag===13){var t=Qt(e,134217728);if(t!==null){var n=We();jt(t,e,134217728,n)}gu(e,134217728)}};Cf=function(e){if(e.tag===13){var t=yn(e),n=Qt(e,t);if(n!==null){var r=We();jt(n,e,t,r)}gu(e,t)}};jf=function(){return ne};Rf=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};Ua=function(e,t,n){switch(t){case"input":if(La(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Si(r);if(!l)throw Error(O(90));rf(r),La(r,l)}}}break;case"textarea":of(e,n);break;case"select":t=n.value,t!=null&&yr(e,!!n.multiple,t,!1)}};hf=fu;pf=Wn;var Lg={usingClientEntryPoint:!1,Events:[Il,ur,Si,df,ff,fu]},qr={findFiberByHostInstance:On,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Og={bundleType:qr.bundleType,version:qr.version,rendererPackageName:qr.rendererPackageName,rendererConfig:qr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Yt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=gf(e),e===null?null:e.stateNode},findFiberByHostInstance:qr.findFiberByHostInstance||Pg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var xo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xo.isDisabled&&xo.supportsFiber)try{gi=xo.inject(Og),Mt=xo}catch{}}st.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Lg;st.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xu(t))throw Error(O(200));return _g(e,t,null,n)};st.createRoot=function(e,t){if(!xu(e))throw Error(O(299));var n=!1,r="",l=Yh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=yu(e,1,!1,null,null,n,!1,r,l),e[Kt]=t.current,El(e.nodeType===8?e.parentNode:e),new vu(t)};st.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(O(188)):(e=Object.keys(e).join(","),Error(O(268,e)));return e=gf(t),e=e===null?null:e.stateNode,e};st.flushSync=function(e){return Wn(e)};st.hydrate=function(e,t,n){if(!Ti(t))throw Error(O(200));return Li(null,e,t,!0,n)};st.hydrateRoot=function(e,t,n){if(!xu(e))throw Error(O(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",i=Yh;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=qh(t,null,e,1,n??null,l,!1,o,i),e[Kt]=t.current,El(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Pi(t)};st.render=function(e,t,n){if(!Ti(t))throw Error(O(200));return Li(null,e,t,!1,n)};st.unmountComponentAtNode=function(e){if(!Ti(e))throw Error(O(40));return e._reactRootContainer?(Wn(function(){Li(null,null,e,!1,function(){e._reactRootContainer=null,e[Kt]=null})}),!0):!1};st.unstable_batchedUpdates=fu;st.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ti(n))throw Error(O(200));if(e==null||e._reactInternals===void 0)throw Error(O(38));return Li(e,t,n,!1,r)};st.version="18.3.1-next-f1338f8080-20240426";function Jh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Jh)}catch(e){console.error(e)}}Jh(),Yd.exports=st;var wu=Yd.exports;const Dg=Fd(wu),Mg=Ad({__proto__:null,default:Dg},[wu]);var Xc=wu;Na.createRoot=Xc.createRoot,Na.hydrateRoot=Xc.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(this,arguments)}var ke;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(ke||(ke={}));const Gc="popstate";function Ag(e){e===void 0&&(e={});function t(r,l){let{pathname:o,search:i,hash:a}=r.location;return Ll("",{pathname:o,search:i,hash:a},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function n(r,l){return typeof l=="string"?l:Kn(l)}return zg(t,n,null,e)}function J(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Pr(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Fg(){return Math.random().toString(36).substr(2,8)}function Zc(e,t){return{usr:e.state,key:e.key,idx:t}}function Ll(e,t,n,r){return n===void 0&&(n=null),de({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?kn(t):t,{state:n,key:t&&t.key||r||Fg()})}function Kn(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function kn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function zg(e,t,n,r){r===void 0&&(r={});let{window:l=document.defaultView,v5Compat:o=!1}=r,i=l.history,a=ke.Pop,s=null,u=d();u==null&&(u=0,i.replaceState(de({},i.state,{idx:u}),""));function d(){return(i.state||{idx:null}).idx}function f(){a=ke.Pop;let C=d(),m=C==null?null:C-u;u=C,s&&s({action:a,location:v.location,delta:m})}function p(C,m){a=ke.Push;let h=Ll(v.location,C,m);u=d()+1;let y=Zc(h,u),j=v.createHref(h);try{i.pushState(y,"",j)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;l.location.assign(j)}o&&s&&s({action:a,location:v.location,delta:1})}function S(C,m){a=ke.Replace;let h=Ll(v.location,C,m);u=d();let y=Zc(h,u),j=v.createHref(h);i.replaceState(y,"",j),o&&s&&s({action:a,location:v.location,delta:0})}function x(C){let m=l.location.origin!=="null"?l.location.origin:l.location.href,h=typeof C=="string"?C:Kn(C);return h=h.replace(/ $/,"%20"),J(m,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,m)}let v={get action(){return a},get location(){return e(l,i)},listen(C){if(s)throw new Error("A history only accepts one active listener");return l.addEventListener(Gc,f),s=C,()=>{l.removeEventListener(Gc,f),s=null}},createHref(C){return t(l,C)},createURL:x,encodeLocation(C){let m=x(C);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:p,replace:S,go(C){return i.go(C)}};return v}var te;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(te||(te={}));const Ug=new Set(["lazy","caseSensitive","path","id","index","children"]);function bg(e){return e.index===!0}function ai(e,t,n,r){return n===void 0&&(n=[]),r===void 0&&(r={}),e.map((l,o)=>{let i=[...n,String(o)],a=typeof l.id=="string"?l.id:i.join("-");if(J(l.index!==!0||!l.children,"Cannot specify children on an index route"),J(!r[a],'Found a route id collision on id "'+a+`".  Route id's must be globally unique within Data Router usages`),bg(l)){let s=de({},l,t(l),{id:a});return r[a]=s,s}else{let s=de({},l,t(l),{id:a,children:void 0});return r[a]=s,l.children&&(s.children=ai(l.children,t,i,r)),s}})}function Tn(e,t,n){return n===void 0&&(n="/"),Do(e,t,n,!1)}function Do(e,t,n,r){let l=typeof t=="string"?kn(t):t,o=Mr(l.pathname||"/",n);if(o==null)return null;let i=Xh(e);$g(i);let a=null;for(let s=0;a==null&&s<i.length;++s){let u=Gg(o);a=Jg(i[s],u,r)}return a}function Ig(e,t){let{route:n,pathname:r,params:l}=e;return{id:n.id,pathname:r,params:l,data:t[n.id],handle:n.handle}}function Xh(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let l=(o,i,a)=>{let s={relativePath:a===void 0?o.path||"":a,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};s.relativePath.startsWith("/")&&(J(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(r.length));let u=Vt([r,s.relativePath]),d=n.concat(s);o.children&&o.children.length>0&&(J(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Xh(o.children,t,d,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:qg(u,o.index),routesMeta:d})};return e.forEach((o,i)=>{var a;if(o.path===""||!((a=o.path)!=null&&a.includes("?")))l(o,i);else for(let s of Gh(o.path))l(o,i,s)}),t}function Gh(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,l=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return l?[o,""]:[o];let i=Gh(r.join("/")),a=[];return a.push(...i.map(s=>s===""?o:[o,s].join("/"))),l&&a.push(...i),a.map(s=>e.startsWith("/")&&s===""?"/":s)}function $g(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Yg(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Bg=/^:[\w-]+$/,Hg=3,Vg=2,Wg=1,Kg=10,Qg=-2,ed=e=>e==="*";function qg(e,t){let n=e.split("/"),r=n.length;return n.some(ed)&&(r+=Qg),t&&(r+=Vg),n.filter(l=>!ed(l)).reduce((l,o)=>l+(Bg.test(o)?Hg:o===""?Wg:Kg),r)}function Yg(e,t){return e.length===t.length&&e.slice(0,-1).every((r,l)=>r===t[l])?e[e.length-1]-t[t.length-1]:0}function Jg(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,l={},o="/",i=[];for(let a=0;a<r.length;++a){let s=r[a],u=a===r.length-1,d=o==="/"?t:t.slice(o.length)||"/",f=td({path:s.relativePath,caseSensitive:s.caseSensitive,end:u},d),p=s.route;if(!f&&u&&n&&!r[r.length-1].route.index&&(f=td({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},d)),!f)return null;Object.assign(l,f.params),i.push({params:l,pathname:Vt([o,f.pathname]),pathnameBase:t0(Vt([o,f.pathnameBase])),route:p}),f.pathnameBase!=="/"&&(o=Vt([o,f.pathnameBase]))}return i}function td(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Xg(e.path,e.caseSensitive,e.end),l=t.match(n);if(!l)return null;let o=l[0],i=o.replace(/(.)\/+$/,"$1"),a=l.slice(1);return{params:r.reduce((u,d,f)=>{let{paramName:p,isOptional:S}=d;if(p==="*"){let v=a[f]||"";i=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const x=a[f];return S&&!x?u[p]=void 0:u[p]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:i,pattern:e}}function Xg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Pr(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,s)=>(r.push({paramName:a,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),r]}function Gg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Pr(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Mr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Zg(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:l=""}=typeof e=="string"?kn(e):e;return{pathname:n?n.startsWith("/")?n:e0(n,t):t,search:n0(r),hash:r0(l)}}function e0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?n.length>1&&n.pop():l!=="."&&n.push(l)}),n.length>1?n.join("/"):"/"}function ga(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Zh(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Su(e,t){let n=Zh(e);return t?n.map((r,l)=>l===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Eu(e,t,n,r){r===void 0&&(r=!1);let l;typeof e=="string"?l=kn(e):(l=de({},e),J(!l.pathname||!l.pathname.includes("?"),ga("?","pathname","search",l)),J(!l.pathname||!l.pathname.includes("#"),ga("#","pathname","hash",l)),J(!l.search||!l.search.includes("#"),ga("#","search","hash",l)));let o=e===""||l.pathname==="",i=o?"/":l.pathname,a;if(i==null)a=n;else{let f=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),f-=1;l.pathname=p.join("/")}a=f>=0?t[f]:"/"}let s=Zg(l,a),u=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&n.endsWith("/");return!s.pathname.endsWith("/")&&(u||d)&&(s.pathname+="/"),s}const Vt=e=>e.join("/").replace(/\/\/+/g,"/"),t0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),n0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,r0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class si{constructor(t,n,r,l){l===void 0&&(l=!1),this.status=t,this.statusText=n||"",this.internal=l,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function Ol(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ep=["post","put","patch","delete"],l0=new Set(ep),o0=["get",...ep],i0=new Set(o0),a0=new Set([301,302,303,307,308]),s0=new Set([307,308]),va={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},u0={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Yr={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ku=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,c0=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),tp="remix-router-transitions";function d0(e){const t=e.window?e.window:typeof window<"u"?window:void 0,n=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",r=!n;J(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let l;if(e.mapRouteProperties)l=e.mapRouteProperties;else if(e.detectErrorBoundary){let g=e.detectErrorBoundary;l=E=>({hasErrorBoundary:g(E)})}else l=c0;let o={},i=ai(e.routes,l,void 0,o),a,s=e.basename||"/",u=e.dataStrategy||m0,d=e.patchRoutesOnNavigation,f=de({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),p=null,S=new Set,x=null,v=null,C=null,m=e.hydrationData!=null,h=Tn(i,e.history.location,s),y=!1,j=null;if(h==null&&!d){let g=qe(404,{pathname:e.history.location.pathname}),{matches:E,route:k}=fd(i);h=E,j={[k.id]:g}}h&&!e.hydrationData&&Gl(h,i,e.history.location.pathname).active&&(h=null);let P;if(h)if(h.some(g=>g.route.lazy))P=!1;else if(!h.some(g=>g.route.loader))P=!0;else if(f.v7_partialHydration){let g=e.hydrationData?e.hydrationData.loaderData:null,E=e.hydrationData?e.hydrationData.errors:null;if(E){let k=h.findIndex(L=>E[L.route.id]!==void 0);P=h.slice(0,k+1).every(L=>!vs(L.route,g,E))}else P=h.every(k=>!vs(k.route,g,E))}else P=e.hydrationData!=null;else if(P=!1,h=[],f.v7_partialHydration){let g=Gl(null,i,e.history.location.pathname);g.active&&g.matches&&(y=!0,h=g.matches)}let T,w={historyAction:e.history.action,location:e.history.location,matches:h,initialized:P,navigation:va,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||j,fetchers:new Map,blockers:new Map},_=ke.Pop,$=!1,A,re=!1,ie=new Map,Ce=null,Te=!1,vt=!1,Jt=[],Xt=new Set,D=new Map,H=0,W=-1,le=new Map,oe=new Set,xt=new Map,nt=new Map,Ie=new Set,$e=new Map,ct=new Map,Yl;function Vp(){if(p=e.history.listen(g=>{let{action:E,location:k,delta:L}=g;if(Yl){Yl(),Yl=void 0;return}Pr(ct.size===0||L!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let M=bu({currentLocation:w.location,nextLocation:k,historyAction:E});if(M&&L!=null){let B=new Promise(V=>{Yl=V});e.history.go(L*-1),Xl(M,{state:"blocked",location:k,proceed(){Xl(M,{state:"proceeding",proceed:void 0,reset:void 0,location:k}),B.then(()=>e.history.go(L))},reset(){let V=new Map(w.blockers);V.set(M,Yr),Be({blockers:V})}});return}return Cn(E,k)}),n){P0(t,ie);let g=()=>T0(t,ie);t.addEventListener("pagehide",g),Ce=()=>t.removeEventListener("pagehide",g)}return w.initialized||Cn(ke.Pop,w.location,{initialHydration:!0}),T}function Wp(){p&&p(),Ce&&Ce(),S.clear(),A&&A.abort(),w.fetchers.forEach((g,E)=>Jl(E)),w.blockers.forEach((g,E)=>Uu(E))}function Kp(g){return S.add(g),()=>S.delete(g)}function Be(g,E){E===void 0&&(E={}),w=de({},w,g);let k=[],L=[];f.v7_fetcherPersist&&w.fetchers.forEach((M,B)=>{M.state==="idle"&&(Ie.has(B)?L.push(B):k.push(B))}),Ie.forEach(M=>{!w.fetchers.has(M)&&!D.has(M)&&L.push(M)}),[...S].forEach(M=>M(w,{deletedFetchers:L,viewTransitionOpts:E.viewTransitionOpts,flushSync:E.flushSync===!0})),f.v7_fetcherPersist?(k.forEach(M=>w.fetchers.delete(M)),L.forEach(M=>Jl(M))):L.forEach(M=>Ie.delete(M))}function Gn(g,E,k){var L,M;let{flushSync:B}=k===void 0?{}:k,V=w.actionData!=null&&w.navigation.formMethod!=null&&kt(w.navigation.formMethod)&&w.navigation.state==="loading"&&((L=g.state)==null?void 0:L._isRedirect)!==!0,U;E.actionData?Object.keys(E.actionData).length>0?U=E.actionData:U=null:V?U=w.actionData:U=null;let b=E.loaderData?cd(w.loaderData,E.loaderData,E.matches||[],E.errors):w.loaderData,z=w.blockers;z.size>0&&(z=new Map(z),z.forEach((X,Le)=>z.set(Le,Yr)));let I=$===!0||w.navigation.formMethod!=null&&kt(w.navigation.formMethod)&&((M=g.state)==null?void 0:M._isRedirect)!==!0;a&&(i=a,a=void 0),Te||_===ke.Pop||(_===ke.Push?e.history.push(g,g.state):_===ke.Replace&&e.history.replace(g,g.state));let Q;if(_===ke.Pop){let X=ie.get(w.location.pathname);X&&X.has(g.pathname)?Q={currentLocation:w.location,nextLocation:g}:ie.has(g.pathname)&&(Q={currentLocation:g,nextLocation:w.location})}else if(re){let X=ie.get(w.location.pathname);X?X.add(g.pathname):(X=new Set([g.pathname]),ie.set(w.location.pathname,X)),Q={currentLocation:w.location,nextLocation:g}}Be(de({},E,{actionData:U,loaderData:b,historyAction:_,location:g,initialized:!0,navigation:va,revalidation:"idle",restoreScrollPosition:$u(g,E.matches||w.matches),preventScrollReset:I,blockers:z}),{viewTransitionOpts:Q,flushSync:B===!0}),_=ke.Pop,$=!1,re=!1,Te=!1,vt=!1,Jt=[]}async function Lu(g,E){if(typeof g=="number"){e.history.go(g);return}let k=gs(w.location,w.matches,s,f.v7_prependBasename,g,f.v7_relativeSplatPath,E==null?void 0:E.fromRouteId,E==null?void 0:E.relative),{path:L,submission:M,error:B}=nd(f.v7_normalizeFormMethod,!1,k,E),V=w.location,U=Ll(w.location,L,E&&E.state);U=de({},U,e.history.encodeLocation(U));let b=E&&E.replace!=null?E.replace:void 0,z=ke.Push;b===!0?z=ke.Replace:b===!1||M!=null&&kt(M.formMethod)&&M.formAction===w.location.pathname+w.location.search&&(z=ke.Replace);let I=E&&"preventScrollReset"in E?E.preventScrollReset===!0:void 0,Q=(E&&E.flushSync)===!0,X=bu({currentLocation:V,nextLocation:U,historyAction:z});if(X){Xl(X,{state:"blocked",location:U,proceed(){Xl(X,{state:"proceeding",proceed:void 0,reset:void 0,location:U}),Lu(g,E)},reset(){let Le=new Map(w.blockers);Le.set(X,Yr),Be({blockers:Le})}});return}return await Cn(z,U,{submission:M,pendingError:B,preventScrollReset:I,replace:E&&E.replace,enableViewTransition:E&&E.viewTransition,flushSync:Q})}function Qp(){if($i(),Be({revalidation:"loading"}),w.navigation.state!=="submitting"){if(w.navigation.state==="idle"){Cn(w.historyAction,w.location,{startUninterruptedRevalidation:!0});return}Cn(_||w.historyAction,w.navigation.location,{overrideNavigation:w.navigation,enableViewTransition:re===!0})}}async function Cn(g,E,k){A&&A.abort(),A=null,_=g,Te=(k&&k.startUninterruptedRevalidation)===!0,rm(w.location,w.matches),$=(k&&k.preventScrollReset)===!0,re=(k&&k.enableViewTransition)===!0;let L=a||i,M=k&&k.overrideNavigation,B=k!=null&&k.initialHydration&&w.matches&&w.matches.length>0&&!y?w.matches:Tn(L,E,s),V=(k&&k.flushSync)===!0;if(B&&w.initialized&&!vt&&S0(w.location,E)&&!(k&&k.submission&&kt(k.submission.formMethod))){Gn(E,{matches:B},{flushSync:V});return}let U=Gl(B,L,E.pathname);if(U.active&&U.matches&&(B=U.matches),!B){let{error:ae,notFoundMatches:ee,route:ye}=Bi(E.pathname);Gn(E,{matches:ee,loaderData:{},errors:{[ye.id]:ae}},{flushSync:V});return}A=new AbortController;let b=rr(e.history,E,A.signal,k&&k.submission),z;if(k&&k.pendingError)z=[Ln(B).route.id,{type:te.error,error:k.pendingError}];else if(k&&k.submission&&kt(k.submission.formMethod)){let ae=await qp(b,E,k.submission,B,U.active,{replace:k.replace,flushSync:V});if(ae.shortCircuited)return;if(ae.pendingActionResult){let[ee,ye]=ae.pendingActionResult;if(lt(ye)&&Ol(ye.error)&&ye.error.status===404){A=null,Gn(E,{matches:ae.matches,loaderData:{},errors:{[ee]:ye.error}});return}}B=ae.matches||B,z=ae.pendingActionResult,M=xa(E,k.submission),V=!1,U.active=!1,b=rr(e.history,b.url,b.signal)}let{shortCircuited:I,matches:Q,loaderData:X,errors:Le}=await Yp(b,E,B,U.active,M,k&&k.submission,k&&k.fetcherSubmission,k&&k.replace,k&&k.initialHydration===!0,V,z);I||(A=null,Gn(E,de({matches:Q||B},dd(z),{loaderData:X,errors:Le})))}async function qp(g,E,k,L,M,B){B===void 0&&(B={}),$i();let V=R0(E,k);if(Be({navigation:V},{flushSync:B.flushSync===!0}),M){let z=await Zl(L,E.pathname,g.signal);if(z.type==="aborted")return{shortCircuited:!0};if(z.type==="error"){let I=Ln(z.partialMatches).route.id;return{matches:z.partialMatches,pendingActionResult:[I,{type:te.error,error:z.error}]}}else if(z.matches)L=z.matches;else{let{notFoundMatches:I,error:Q,route:X}=Bi(E.pathname);return{matches:I,pendingActionResult:[X.id,{type:te.error,error:Q}]}}}let U,b=rl(L,E);if(!b.route.action&&!b.route.lazy)U={type:te.error,error:qe(405,{method:g.method,pathname:E.pathname,routeId:b.route.id})};else if(U=(await zr("action",w,g,[b],L,null))[b.route.id],g.signal.aborted)return{shortCircuited:!0};if(An(U)){let z;return B&&B.replace!=null?z=B.replace:z=ad(U.response.headers.get("Location"),new URL(g.url),s)===w.location.pathname+w.location.search,await jn(g,U,!0,{submission:k,replace:z}),{shortCircuited:!0}}if(un(U))throw qe(400,{type:"defer-action"});if(lt(U)){let z=Ln(L,b.route.id);return(B&&B.replace)!==!0&&(_=ke.Push),{matches:L,pendingActionResult:[z.route.id,U]}}return{matches:L,pendingActionResult:[b.route.id,U]}}async function Yp(g,E,k,L,M,B,V,U,b,z,I){let Q=M||xa(E,B),X=B||V||pd(Q),Le=!Te&&(!f.v7_partialHydration||!b);if(L){if(Le){let ge=Ou(I);Be(de({navigation:Q},ge!==void 0?{actionData:ge}:{}),{flushSync:z})}let Z=await Zl(k,E.pathname,g.signal);if(Z.type==="aborted")return{shortCircuited:!0};if(Z.type==="error"){let ge=Ln(Z.partialMatches).route.id;return{matches:Z.partialMatches,loaderData:{},errors:{[ge]:Z.error}}}else if(Z.matches)k=Z.matches;else{let{error:ge,notFoundMatches:er,route:Ir}=Bi(E.pathname);return{matches:er,loaderData:{},errors:{[Ir.id]:ge}}}}let ae=a||i,[ee,ye]=ld(e.history,w,k,X,E,f.v7_partialHydration&&b===!0,f.v7_skipActionErrorRevalidation,vt,Jt,Xt,Ie,xt,oe,ae,s,I);if(Hi(Z=>!(k&&k.some(ge=>ge.route.id===Z))||ee&&ee.some(ge=>ge.route.id===Z)),W=++H,ee.length===0&&ye.length===0){let Z=Fu();return Gn(E,de({matches:k,loaderData:{},errors:I&&lt(I[1])?{[I[0]]:I[1].error}:null},dd(I),Z?{fetchers:new Map(w.fetchers)}:{}),{flushSync:z}),{shortCircuited:!0}}if(Le){let Z={};if(!L){Z.navigation=Q;let ge=Ou(I);ge!==void 0&&(Z.actionData=ge)}ye.length>0&&(Z.fetchers=Jp(ye)),Be(Z,{flushSync:z})}ye.forEach(Z=>{Zt(Z.key),Z.controller&&D.set(Z.key,Z.controller)});let Zn=()=>ye.forEach(Z=>Zt(Z.key));A&&A.signal.addEventListener("abort",Zn);let{loaderResults:Ur,fetcherResults:zt}=await Du(w,k,ee,ye,g);if(g.signal.aborted)return{shortCircuited:!0};A&&A.signal.removeEventListener("abort",Zn),ye.forEach(Z=>D.delete(Z.key));let Pt=wo(Ur);if(Pt)return await jn(g,Pt.result,!0,{replace:U}),{shortCircuited:!0};if(Pt=wo(zt),Pt)return oe.add(Pt.key),await jn(g,Pt.result,!0,{replace:U}),{shortCircuited:!0};let{loaderData:Vi,errors:br}=ud(w,k,Ur,I,ye,zt,$e);$e.forEach((Z,ge)=>{Z.subscribe(er=>{(er||Z.done)&&$e.delete(ge)})}),f.v7_partialHydration&&b&&w.errors&&(br=de({},w.errors,br));let Rn=Fu(),eo=zu(W),to=Rn||eo||ye.length>0;return de({matches:k,loaderData:Vi,errors:br},to?{fetchers:new Map(w.fetchers)}:{})}function Ou(g){if(g&&!lt(g[1]))return{[g[0]]:g[1].data};if(w.actionData)return Object.keys(w.actionData).length===0?null:w.actionData}function Jp(g){return g.forEach(E=>{let k=w.fetchers.get(E.key),L=Jr(void 0,k?k.data:void 0);w.fetchers.set(E.key,L)}),new Map(w.fetchers)}function Xp(g,E,k,L){if(r)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");Zt(g);let M=(L&&L.flushSync)===!0,B=a||i,V=gs(w.location,w.matches,s,f.v7_prependBasename,k,f.v7_relativeSplatPath,E,L==null?void 0:L.relative),U=Tn(B,V,s),b=Gl(U,B,V);if(b.active&&b.matches&&(U=b.matches),!U){Ft(g,E,qe(404,{pathname:V}),{flushSync:M});return}let{path:z,submission:I,error:Q}=nd(f.v7_normalizeFormMethod,!0,V,L);if(Q){Ft(g,E,Q,{flushSync:M});return}let X=rl(U,z),Le=(L&&L.preventScrollReset)===!0;if(I&&kt(I.formMethod)){Gp(g,E,z,X,U,b.active,M,Le,I);return}xt.set(g,{routeId:E,path:z}),Zp(g,E,z,X,U,b.active,M,Le,I)}async function Gp(g,E,k,L,M,B,V,U,b){$i(),xt.delete(g);function z(Ee){if(!Ee.route.action&&!Ee.route.lazy){let tr=qe(405,{method:b.formMethod,pathname:k,routeId:E});return Ft(g,E,tr,{flushSync:V}),!0}return!1}if(!B&&z(L))return;let I=w.fetchers.get(g);Gt(g,_0(b,I),{flushSync:V});let Q=new AbortController,X=rr(e.history,k,Q.signal,b);if(B){let Ee=await Zl(M,new URL(X.url).pathname,X.signal,g);if(Ee.type==="aborted")return;if(Ee.type==="error"){Ft(g,E,Ee.error,{flushSync:V});return}else if(Ee.matches){if(M=Ee.matches,L=rl(M,k),z(L))return}else{Ft(g,E,qe(404,{pathname:k}),{flushSync:V});return}}D.set(g,Q);let Le=H,ee=(await zr("action",w,X,[L],M,g))[L.route.id];if(X.signal.aborted){D.get(g)===Q&&D.delete(g);return}if(f.v7_fetcherPersist&&Ie.has(g)){if(An(ee)||lt(ee)){Gt(g,tn(void 0));return}}else{if(An(ee))if(D.delete(g),W>Le){Gt(g,tn(void 0));return}else return oe.add(g),Gt(g,Jr(b)),jn(X,ee,!1,{fetcherSubmission:b,preventScrollReset:U});if(lt(ee)){Ft(g,E,ee.error);return}}if(un(ee))throw qe(400,{type:"defer-action"});let ye=w.navigation.location||w.location,Zn=rr(e.history,ye,Q.signal),Ur=a||i,zt=w.navigation.state!=="idle"?Tn(Ur,w.navigation.location,s):w.matches;J(zt,"Didn't find any matches after fetcher action");let Pt=++H;le.set(g,Pt);let Vi=Jr(b,ee.data);w.fetchers.set(g,Vi);let[br,Rn]=ld(e.history,w,zt,b,ye,!1,f.v7_skipActionErrorRevalidation,vt,Jt,Xt,Ie,xt,oe,Ur,s,[L.route.id,ee]);Rn.filter(Ee=>Ee.key!==g).forEach(Ee=>{let tr=Ee.key,Bu=w.fetchers.get(tr),im=Jr(void 0,Bu?Bu.data:void 0);w.fetchers.set(tr,im),Zt(tr),Ee.controller&&D.set(tr,Ee.controller)}),Be({fetchers:new Map(w.fetchers)});let eo=()=>Rn.forEach(Ee=>Zt(Ee.key));Q.signal.addEventListener("abort",eo);let{loaderResults:to,fetcherResults:Z}=await Du(w,zt,br,Rn,Zn);if(Q.signal.aborted)return;Q.signal.removeEventListener("abort",eo),le.delete(g),D.delete(g),Rn.forEach(Ee=>D.delete(Ee.key));let ge=wo(to);if(ge)return jn(Zn,ge.result,!1,{preventScrollReset:U});if(ge=wo(Z),ge)return oe.add(ge.key),jn(Zn,ge.result,!1,{preventScrollReset:U});let{loaderData:er,errors:Ir}=ud(w,zt,to,void 0,Rn,Z,$e);if(w.fetchers.has(g)){let Ee=tn(ee.data);w.fetchers.set(g,Ee)}zu(Pt),w.navigation.state==="loading"&&Pt>W?(J(_,"Expected pending action"),A&&A.abort(),Gn(w.navigation.location,{matches:zt,loaderData:er,errors:Ir,fetchers:new Map(w.fetchers)})):(Be({errors:Ir,loaderData:cd(w.loaderData,er,zt,Ir),fetchers:new Map(w.fetchers)}),vt=!1)}async function Zp(g,E,k,L,M,B,V,U,b){let z=w.fetchers.get(g);Gt(g,Jr(b,z?z.data:void 0),{flushSync:V});let I=new AbortController,Q=rr(e.history,k,I.signal);if(B){let ee=await Zl(M,new URL(Q.url).pathname,Q.signal,g);if(ee.type==="aborted")return;if(ee.type==="error"){Ft(g,E,ee.error,{flushSync:V});return}else if(ee.matches)M=ee.matches,L=rl(M,k);else{Ft(g,E,qe(404,{pathname:k}),{flushSync:V});return}}D.set(g,I);let X=H,ae=(await zr("loader",w,Q,[L],M,g))[L.route.id];if(un(ae)&&(ae=await Nu(ae,Q.signal,!0)||ae),D.get(g)===I&&D.delete(g),!Q.signal.aborted){if(Ie.has(g)){Gt(g,tn(void 0));return}if(An(ae))if(W>X){Gt(g,tn(void 0));return}else{oe.add(g),await jn(Q,ae,!1,{preventScrollReset:U});return}if(lt(ae)){Ft(g,E,ae.error);return}J(!un(ae),"Unhandled fetcher deferred data"),Gt(g,tn(ae.data))}}async function jn(g,E,k,L){let{submission:M,fetcherSubmission:B,preventScrollReset:V,replace:U}=L===void 0?{}:L;E.response.headers.has("X-Remix-Revalidate")&&(vt=!0);let b=E.response.headers.get("Location");J(b,"Expected a Location header on the redirect Response"),b=ad(b,new URL(g.url),s);let z=Ll(w.location,b,{_isRedirect:!0});if(n){let ee=!1;if(E.response.headers.has("X-Remix-Reload-Document"))ee=!0;else if(ku.test(b)){const ye=e.history.createURL(b);ee=ye.origin!==t.location.origin||Mr(ye.pathname,s)==null}if(ee){U?t.location.replace(b):t.location.assign(b);return}}A=null;let I=U===!0||E.response.headers.has("X-Remix-Replace")?ke.Replace:ke.Push,{formMethod:Q,formAction:X,formEncType:Le}=w.navigation;!M&&!B&&Q&&X&&Le&&(M=pd(w.navigation));let ae=M||B;if(s0.has(E.response.status)&&ae&&kt(ae.formMethod))await Cn(I,z,{submission:de({},ae,{formAction:b}),preventScrollReset:V||$,enableViewTransition:k?re:void 0});else{let ee=xa(z,M);await Cn(I,z,{overrideNavigation:ee,fetcherSubmission:B,preventScrollReset:V||$,enableViewTransition:k?re:void 0})}}async function zr(g,E,k,L,M,B){let V,U={};try{V=await y0(u,g,E,k,L,M,B,o,l)}catch(b){return L.forEach(z=>{U[z.route.id]={type:te.error,error:b}}),U}for(let[b,z]of Object.entries(V))if(E0(z)){let I=z.result;U[b]={type:te.redirect,response:x0(I,k,b,M,s,f.v7_relativeSplatPath)}}else U[b]=await v0(z);return U}async function Du(g,E,k,L,M){let B=g.matches,V=zr("loader",g,M,k,E,null),U=Promise.all(L.map(async I=>{if(I.matches&&I.match&&I.controller){let X=(await zr("loader",g,rr(e.history,I.path,I.controller.signal),[I.match],I.matches,I.key))[I.match.route.id];return{[I.key]:X}}else return Promise.resolve({[I.key]:{type:te.error,error:qe(404,{pathname:I.path})}})})),b=await V,z=(await U).reduce((I,Q)=>Object.assign(I,Q),{});return await Promise.all([C0(E,b,M.signal,B,g.loaderData),j0(E,z,L)]),{loaderResults:b,fetcherResults:z}}function $i(){vt=!0,Jt.push(...Hi()),xt.forEach((g,E)=>{D.has(E)&&Xt.add(E),Zt(E)})}function Gt(g,E,k){k===void 0&&(k={}),w.fetchers.set(g,E),Be({fetchers:new Map(w.fetchers)},{flushSync:(k&&k.flushSync)===!0})}function Ft(g,E,k,L){L===void 0&&(L={});let M=Ln(w.matches,E);Jl(g),Be({errors:{[M.route.id]:k},fetchers:new Map(w.fetchers)},{flushSync:(L&&L.flushSync)===!0})}function Mu(g){return nt.set(g,(nt.get(g)||0)+1),Ie.has(g)&&Ie.delete(g),w.fetchers.get(g)||u0}function Jl(g){let E=w.fetchers.get(g);D.has(g)&&!(E&&E.state==="loading"&&le.has(g))&&Zt(g),xt.delete(g),le.delete(g),oe.delete(g),f.v7_fetcherPersist&&Ie.delete(g),Xt.delete(g),w.fetchers.delete(g)}function em(g){let E=(nt.get(g)||0)-1;E<=0?(nt.delete(g),Ie.add(g),f.v7_fetcherPersist||Jl(g)):nt.set(g,E),Be({fetchers:new Map(w.fetchers)})}function Zt(g){let E=D.get(g);E&&(E.abort(),D.delete(g))}function Au(g){for(let E of g){let k=Mu(E),L=tn(k.data);w.fetchers.set(E,L)}}function Fu(){let g=[],E=!1;for(let k of oe){let L=w.fetchers.get(k);J(L,"Expected fetcher: "+k),L.state==="loading"&&(oe.delete(k),g.push(k),E=!0)}return Au(g),E}function zu(g){let E=[];for(let[k,L]of le)if(L<g){let M=w.fetchers.get(k);J(M,"Expected fetcher: "+k),M.state==="loading"&&(Zt(k),le.delete(k),E.push(k))}return Au(E),E.length>0}function tm(g,E){let k=w.blockers.get(g)||Yr;return ct.get(g)!==E&&ct.set(g,E),k}function Uu(g){w.blockers.delete(g),ct.delete(g)}function Xl(g,E){let k=w.blockers.get(g)||Yr;J(k.state==="unblocked"&&E.state==="blocked"||k.state==="blocked"&&E.state==="blocked"||k.state==="blocked"&&E.state==="proceeding"||k.state==="blocked"&&E.state==="unblocked"||k.state==="proceeding"&&E.state==="unblocked","Invalid blocker state transition: "+k.state+" -> "+E.state);let L=new Map(w.blockers);L.set(g,E),Be({blockers:L})}function bu(g){let{currentLocation:E,nextLocation:k,historyAction:L}=g;if(ct.size===0)return;ct.size>1&&Pr(!1,"A router only supports one blocker at a time");let M=Array.from(ct.entries()),[B,V]=M[M.length-1],U=w.blockers.get(B);if(!(U&&U.state==="proceeding")&&V({currentLocation:E,nextLocation:k,historyAction:L}))return B}function Bi(g){let E=qe(404,{pathname:g}),k=a||i,{matches:L,route:M}=fd(k);return Hi(),{notFoundMatches:L,route:M,error:E}}function Hi(g){let E=[];return $e.forEach((k,L)=>{(!g||g(L))&&(k.cancel(),E.push(L),$e.delete(L))}),E}function nm(g,E,k){if(x=g,C=E,v=k||null,!m&&w.navigation===va){m=!0;let L=$u(w.location,w.matches);L!=null&&Be({restoreScrollPosition:L})}return()=>{x=null,C=null,v=null}}function Iu(g,E){return v&&v(g,E.map(L=>Ig(L,w.loaderData)))||g.key}function rm(g,E){if(x&&C){let k=Iu(g,E);x[k]=C()}}function $u(g,E){if(x){let k=Iu(g,E),L=x[k];if(typeof L=="number")return L}return null}function Gl(g,E,k){if(d)if(g){if(Object.keys(g[0].params).length>0)return{active:!0,matches:Do(E,k,s,!0)}}else return{active:!0,matches:Do(E,k,s,!0)||[]};return{active:!1,matches:null}}async function Zl(g,E,k,L){if(!d)return{type:"success",matches:g};let M=g;for(;;){let B=a==null,V=a||i,U=o;try{await d({signal:k,path:E,matches:M,fetcherKey:L,patch:(I,Q)=>{k.aborted||id(I,Q,V,U,l)}})}catch(I){return{type:"error",error:I,partialMatches:M}}finally{B&&!k.aborted&&(i=[...i])}if(k.aborted)return{type:"aborted"};let b=Tn(V,E,s);if(b)return{type:"success",matches:b};let z=Do(V,E,s,!0);if(!z||M.length===z.length&&M.every((I,Q)=>I.route.id===z[Q].route.id))return{type:"success",matches:null};M=z}}function lm(g){o={},a=ai(g,l,void 0,o)}function om(g,E){let k=a==null;id(g,E,a||i,o,l),k&&(i=[...i],Be({}))}return T={get basename(){return s},get future(){return f},get state(){return w},get routes(){return i},get window(){return t},initialize:Vp,subscribe:Kp,enableScrollRestoration:nm,navigate:Lu,fetch:Xp,revalidate:Qp,createHref:g=>e.history.createHref(g),encodeLocation:g=>e.history.encodeLocation(g),getFetcher:Mu,deleteFetcher:em,dispose:Wp,getBlocker:tm,deleteBlocker:Uu,patchRoutes:om,_internalFetchControllers:D,_internalActiveDeferreds:$e,_internalSetRoutes:lm},T}function f0(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function gs(e,t,n,r,l,o,i,a){let s,u;if(i){s=[];for(let f of t)if(s.push(f),f.route.id===i){u=f;break}}else s=t,u=t[t.length-1];let d=Eu(l||".",Su(s,o),Mr(e.pathname,n)||e.pathname,a==="path");if(l==null&&(d.search=e.search,d.hash=e.hash),(l==null||l===""||l===".")&&u){let f=Cu(d.search);if(u.route.index&&!f)d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&f){let p=new URLSearchParams(d.search),S=p.getAll("index");p.delete("index"),S.filter(v=>v).forEach(v=>p.append("index",v));let x=p.toString();d.search=x?"?"+x:""}}return r&&n!=="/"&&(d.pathname=d.pathname==="/"?n:Vt([n,d.pathname])),Kn(d)}function nd(e,t,n,r){if(!r||!f0(r))return{path:n};if(r.formMethod&&!N0(r.formMethod))return{path:n,error:qe(405,{method:r.formMethod})};let l=()=>({path:n,error:qe(400,{type:"invalid-body"})}),o=r.formMethod||"get",i=e?o.toUpperCase():o.toLowerCase(),a=lp(n);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!kt(i))return l();let p=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((S,x)=>{let[v,C]=x;return""+S+v+"="+C+`
`},""):String(r.body);return{path:n,submission:{formMethod:i,formAction:a,formEncType:r.formEncType,formData:void 0,json:void 0,text:p}}}else if(r.formEncType==="application/json"){if(!kt(i))return l();try{let p=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:n,submission:{formMethod:i,formAction:a,formEncType:r.formEncType,formData:void 0,json:p,text:void 0}}}catch{return l()}}}J(typeof FormData=="function","FormData is not available in this environment");let s,u;if(r.formData)s=xs(r.formData),u=r.formData;else if(r.body instanceof FormData)s=xs(r.body),u=r.body;else if(r.body instanceof URLSearchParams)s=r.body,u=sd(s);else if(r.body==null)s=new URLSearchParams,u=new FormData;else try{s=new URLSearchParams(r.body),u=sd(s)}catch{return l()}let d={formMethod:i,formAction:a,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:u,json:void 0,text:void 0};if(kt(d.formMethod))return{path:n,submission:d};let f=kn(n);return t&&f.search&&Cu(f.search)&&s.append("index",""),f.search="?"+s,{path:Kn(f),submission:d}}function rd(e,t,n){n===void 0&&(n=!1);let r=e.findIndex(l=>l.route.id===t);return r>=0?e.slice(0,n?r+1:r):e}function ld(e,t,n,r,l,o,i,a,s,u,d,f,p,S,x,v){let C=v?lt(v[1])?v[1].error:v[1].data:void 0,m=e.createURL(t.location),h=e.createURL(l),y=n;o&&t.errors?y=rd(n,Object.keys(t.errors)[0],!0):v&&lt(v[1])&&(y=rd(n,v[0]));let j=v?v[1].statusCode:void 0,P=i&&j&&j>=400,T=y.filter((_,$)=>{let{route:A}=_;if(A.lazy)return!0;if(A.loader==null)return!1;if(o)return vs(A,t.loaderData,t.errors);if(h0(t.loaderData,t.matches[$],_)||s.some(Ce=>Ce===_.route.id))return!0;let re=t.matches[$],ie=_;return od(_,de({currentUrl:m,currentParams:re.params,nextUrl:h,nextParams:ie.params},r,{actionResult:C,actionStatus:j,defaultShouldRevalidate:P?!1:a||m.pathname+m.search===h.pathname+h.search||m.search!==h.search||np(re,ie)}))}),w=[];return f.forEach((_,$)=>{if(o||!n.some(Te=>Te.route.id===_.routeId)||d.has($))return;let A=Tn(S,_.path,x);if(!A){w.push({key:$,routeId:_.routeId,path:_.path,matches:null,match:null,controller:null});return}let re=t.fetchers.get($),ie=rl(A,_.path),Ce=!1;p.has($)?Ce=!1:u.has($)?(u.delete($),Ce=!0):re&&re.state!=="idle"&&re.data===void 0?Ce=a:Ce=od(ie,de({currentUrl:m,currentParams:t.matches[t.matches.length-1].params,nextUrl:h,nextParams:n[n.length-1].params},r,{actionResult:C,actionStatus:j,defaultShouldRevalidate:P?!1:a})),Ce&&w.push({key:$,routeId:_.routeId,path:_.path,matches:A,match:ie,controller:new AbortController})}),[T,w]}function vs(e,t,n){if(e.lazy)return!0;if(!e.loader)return!1;let r=t!=null&&t[e.id]!==void 0,l=n!=null&&n[e.id]!==void 0;return!r&&l?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!r&&!l}function h0(e,t,n){let r=!t||n.route.id!==t.route.id,l=e[n.route.id]===void 0;return r||l}function np(e,t){let n=e.route.path;return e.pathname!==t.pathname||n!=null&&n.endsWith("*")&&e.params["*"]!==t.params["*"]}function od(e,t){if(e.route.shouldRevalidate){let n=e.route.shouldRevalidate(t);if(typeof n=="boolean")return n}return t.defaultShouldRevalidate}function id(e,t,n,r,l){var o;let i;if(e){let u=r[e];J(u,"No route found to patch children into: routeId = "+e),u.children||(u.children=[]),i=u.children}else i=n;let a=t.filter(u=>!i.some(d=>rp(u,d))),s=ai(a,l,[e||"_","patch",String(((o=i)==null?void 0:o.length)||"0")],r);i.push(...s)}function rp(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((n,r)=>{var l;return(l=t.children)==null?void 0:l.some(o=>rp(n,o))}):!1}async function p0(e,t,n){if(!e.lazy)return;let r=await e.lazy();if(!e.lazy)return;let l=n[e.id];J(l,"No route found in manifest");let o={};for(let i in r){let s=l[i]!==void 0&&i!=="hasErrorBoundary";Pr(!s,'Route "'+l.id+'" has a static property "'+i+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+i+'" will be ignored.')),!s&&!Ug.has(i)&&(o[i]=r[i])}Object.assign(l,o),Object.assign(l,de({},t(l),{lazy:void 0}))}async function m0(e){let{matches:t}=e,n=t.filter(l=>l.shouldLoad);return(await Promise.all(n.map(l=>l.resolve()))).reduce((l,o,i)=>Object.assign(l,{[n[i].route.id]:o}),{})}async function y0(e,t,n,r,l,o,i,a,s,u){let d=o.map(S=>S.route.lazy?p0(S.route,s,a):void 0),f=o.map((S,x)=>{let v=d[x],C=l.some(h=>h.route.id===S.route.id);return de({},S,{shouldLoad:C,resolve:async h=>(h&&r.method==="GET"&&(S.route.lazy||S.route.loader)&&(C=!0),C?g0(t,r,S,v,h,u):Promise.resolve({type:te.data,result:void 0}))})}),p=await e({matches:f,request:r,params:o[0].params,fetcherKey:i,context:u});try{await Promise.all(d)}catch{}return p}async function g0(e,t,n,r,l,o){let i,a,s=u=>{let d,f=new Promise((x,v)=>d=v);a=()=>d(),t.signal.addEventListener("abort",a);let p=x=>typeof u!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+n.route.id+"]"))):u({request:t,params:n.params,context:o},...x!==void 0?[x]:[]),S=(async()=>{try{return{type:"data",result:await(l?l(v=>p(v)):p())}}catch(x){return{type:"error",result:x}}})();return Promise.race([S,f])};try{let u=n.route[e];if(r)if(u){let d,[f]=await Promise.all([s(u).catch(p=>{d=p}),r]);if(d!==void 0)throw d;i=f}else if(await r,u=n.route[e],u)i=await s(u);else if(e==="action"){let d=new URL(t.url),f=d.pathname+d.search;throw qe(405,{method:t.method,pathname:f,routeId:n.route.id})}else return{type:te.data,result:void 0};else if(u)i=await s(u);else{let d=new URL(t.url),f=d.pathname+d.search;throw qe(404,{pathname:f})}J(i.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+n.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(u){return{type:te.error,result:u}}finally{a&&t.signal.removeEventListener("abort",a)}return i}async function v0(e){let{result:t,type:n}=e;if(op(t)){let f;try{let p=t.headers.get("Content-Type");p&&/\bapplication\/json\b/.test(p)?t.body==null?f=null:f=await t.json():f=await t.text()}catch(p){return{type:te.error,error:p}}return n===te.error?{type:te.error,error:new si(t.status,t.statusText,f),statusCode:t.status,headers:t.headers}:{type:te.data,data:f,statusCode:t.status,headers:t.headers}}if(n===te.error){if(hd(t)){var r,l;if(t.data instanceof Error){var o,i;return{type:te.error,error:t.data,statusCode:(o=t.init)==null?void 0:o.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}}return{type:te.error,error:new si(((r=t.init)==null?void 0:r.status)||500,void 0,t.data),statusCode:Ol(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}}return{type:te.error,error:t,statusCode:Ol(t)?t.status:void 0}}if(k0(t)){var a,s;return{type:te.deferred,deferredData:t,statusCode:(a=t.init)==null?void 0:a.status,headers:((s=t.init)==null?void 0:s.headers)&&new Headers(t.init.headers)}}if(hd(t)){var u,d;return{type:te.data,data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(d=t.init)!=null&&d.headers?new Headers(t.init.headers):void 0}}return{type:te.data,data:t}}function x0(e,t,n,r,l,o){let i=e.headers.get("Location");if(J(i,"Redirects returned/thrown from loaders/actions must have a Location header"),!ku.test(i)){let a=r.slice(0,r.findIndex(s=>s.route.id===n)+1);i=gs(new URL(t.url),a,l,!0,i,o),e.headers.set("Location",i)}return e}function ad(e,t,n){if(ku.test(e)){let r=e,l=r.startsWith("//")?new URL(t.protocol+r):new URL(r),o=Mr(l.pathname,n)!=null;if(l.origin===t.origin&&o)return l.pathname+l.search+l.hash}return e}function rr(e,t,n,r){let l=e.createURL(lp(t)).toString(),o={signal:n};if(r&&kt(r.formMethod)){let{formMethod:i,formEncType:a}=r;o.method=i.toUpperCase(),a==="application/json"?(o.headers=new Headers({"Content-Type":a}),o.body=JSON.stringify(r.json)):a==="text/plain"?o.body=r.text:a==="application/x-www-form-urlencoded"&&r.formData?o.body=xs(r.formData):o.body=r.formData}return new Request(l,o)}function xs(e){let t=new URLSearchParams;for(let[n,r]of e.entries())t.append(n,typeof r=="string"?r:r.name);return t}function sd(e){let t=new FormData;for(let[n,r]of e.entries())t.append(n,r);return t}function w0(e,t,n,r,l){let o={},i=null,a,s=!1,u={},d=n&&lt(n[1])?n[1].error:void 0;return e.forEach(f=>{if(!(f.route.id in t))return;let p=f.route.id,S=t[p];if(J(!An(S),"Cannot handle redirect results in processLoaderData"),lt(S)){let x=S.error;d!==void 0&&(x=d,d=void 0),i=i||{};{let v=Ln(e,p);i[v.route.id]==null&&(i[v.route.id]=x)}o[p]=void 0,s||(s=!0,a=Ol(S.error)?S.error.status:500),S.headers&&(u[p]=S.headers)}else un(S)?(r.set(p,S.deferredData),o[p]=S.deferredData.data,S.statusCode!=null&&S.statusCode!==200&&!s&&(a=S.statusCode),S.headers&&(u[p]=S.headers)):(o[p]=S.data,S.statusCode&&S.statusCode!==200&&!s&&(a=S.statusCode),S.headers&&(u[p]=S.headers))}),d!==void 0&&n&&(i={[n[0]]:d},o[n[0]]=void 0),{loaderData:o,errors:i,statusCode:a||200,loaderHeaders:u}}function ud(e,t,n,r,l,o,i){let{loaderData:a,errors:s}=w0(t,n,r,i);return l.forEach(u=>{let{key:d,match:f,controller:p}=u,S=o[d];if(J(S,"Did not find corresponding fetcher result"),!(p&&p.signal.aborted))if(lt(S)){let x=Ln(e.matches,f==null?void 0:f.route.id);s&&s[x.route.id]||(s=de({},s,{[x.route.id]:S.error})),e.fetchers.delete(d)}else if(An(S))J(!1,"Unhandled fetcher revalidation redirect");else if(un(S))J(!1,"Unhandled fetcher deferred data");else{let x=tn(S.data);e.fetchers.set(d,x)}}),{loaderData:a,errors:s}}function cd(e,t,n,r){let l=de({},t);for(let o of n){let i=o.route.id;if(t.hasOwnProperty(i)?t[i]!==void 0&&(l[i]=t[i]):e[i]!==void 0&&o.route.loader&&(l[i]=e[i]),r&&r.hasOwnProperty(i))break}return l}function dd(e){return e?lt(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Ln(e,t){return(t?e.slice(0,e.findIndex(r=>r.route.id===t)+1):[...e]).reverse().find(r=>r.route.hasErrorBoundary===!0)||e[0]}function fd(e){let t=e.length===1?e[0]:e.find(n=>n.index||!n.path||n.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function qe(e,t){let{pathname:n,routeId:r,method:l,type:o,message:i}=t===void 0?{}:t,a="Unknown Server Error",s="Unknown @remix-run/router error";return e===400?(a="Bad Request",l&&n&&r?s="You made a "+l+' request to "'+n+'" but '+('did not provide a `loader` for route "'+r+'", ')+"so there is no way to handle the request.":o==="defer-action"?s="defer() is not supported in actions":o==="invalid-body"&&(s="Unable to encode submission body")):e===403?(a="Forbidden",s='Route "'+r+'" does not match URL "'+n+'"'):e===404?(a="Not Found",s='No route matches URL "'+n+'"'):e===405&&(a="Method Not Allowed",l&&n&&r?s="You made a "+l.toUpperCase()+' request to "'+n+'" but '+('did not provide an `action` for route "'+r+'", ')+"so there is no way to handle the request.":l&&(s='Invalid request method "'+l.toUpperCase()+'"')),new si(e||500,a,new Error(s),!0)}function wo(e){let t=Object.entries(e);for(let n=t.length-1;n>=0;n--){let[r,l]=t[n];if(An(l))return{key:r,result:l}}}function lp(e){let t=typeof e=="string"?kn(e):e;return Kn(de({},t,{hash:""}))}function S0(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function E0(e){return op(e.result)&&a0.has(e.result.status)}function un(e){return e.type===te.deferred}function lt(e){return e.type===te.error}function An(e){return(e&&e.type)===te.redirect}function hd(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function k0(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function op(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function N0(e){return i0.has(e.toLowerCase())}function kt(e){return l0.has(e.toLowerCase())}async function C0(e,t,n,r,l){let o=Object.entries(t);for(let i=0;i<o.length;i++){let[a,s]=o[i],u=e.find(p=>(p==null?void 0:p.route.id)===a);if(!u)continue;let d=r.find(p=>p.route.id===u.route.id),f=d!=null&&!np(d,u)&&(l&&l[u.route.id])!==void 0;un(s)&&f&&await Nu(s,n,!1).then(p=>{p&&(t[a]=p)})}}async function j0(e,t,n){for(let r=0;r<n.length;r++){let{key:l,routeId:o,controller:i}=n[r],a=t[l];e.find(u=>(u==null?void 0:u.route.id)===o)&&un(a)&&(J(i,"Expected an AbortController for revalidating fetcher deferred result"),await Nu(a,i.signal,!0).then(u=>{u&&(t[l]=u)}))}}async function Nu(e,t,n){if(n===void 0&&(n=!1),!await e.deferredData.resolveData(t)){if(n)try{return{type:te.data,data:e.deferredData.unwrappedData}}catch(l){return{type:te.error,error:l}}return{type:te.data,data:e.deferredData.data}}}function Cu(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function rl(e,t){let n=typeof t=="string"?kn(t).search:t.search;if(e[e.length-1].route.index&&Cu(n||""))return e[e.length-1];let r=Zh(e);return r[r.length-1]}function pd(e){let{formMethod:t,formAction:n,formEncType:r,text:l,formData:o,json:i}=e;if(!(!t||!n||!r)){if(l!=null)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:void 0,text:l};if(o!=null)return{formMethod:t,formAction:n,formEncType:r,formData:o,json:void 0,text:void 0};if(i!==void 0)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:i,text:void 0}}}function xa(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function R0(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Jr(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function _0(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function tn(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function P0(e,t){try{let n=e.sessionStorage.getItem(tp);if(n){let r=JSON.parse(n);for(let[l,o]of Object.entries(r||{}))o&&Array.isArray(o)&&t.set(l,new Set(o||[]))}}catch{}}function T0(e,t){if(t.size>0){let n={};for(let[r,l]of t)n[r]=[...l];try{e.sessionStorage.setItem(tp,JSON.stringify(n))}catch(r){Pr(!1,"Failed to save applied view transitions in sessionStorage ("+r+").")}}}/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}const Oi=R.createContext(null),ip=R.createContext(null),Jn=R.createContext(null),ju=R.createContext(null),Nn=R.createContext({outlet:null,matches:[],isDataRoute:!1}),ap=R.createContext(null);function L0(e,t){let{relative:n}=t===void 0?{}:t;Bl()||J(!1);let{basename:r,navigator:l}=R.useContext(Jn),{hash:o,pathname:i,search:a}=up(e,{relative:n}),s=i;return r!=="/"&&(s=i==="/"?r:Vt([r,i])),l.createHref({pathname:s,search:a,hash:o})}function Bl(){return R.useContext(ju)!=null}function Hl(){return Bl()||J(!1),R.useContext(ju).location}function sp(e){R.useContext(Jn).static||R.useLayoutEffect(e)}function Xn(){let{isDataRoute:e}=R.useContext(Nn);return e?W0():O0()}function O0(){Bl()||J(!1);let e=R.useContext(Oi),{basename:t,future:n,navigator:r}=R.useContext(Jn),{matches:l}=R.useContext(Nn),{pathname:o}=Hl(),i=JSON.stringify(Su(l,n.v7_relativeSplatPath)),a=R.useRef(!1);return sp(()=>{a.current=!0}),R.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let f=Eu(u,JSON.parse(i),o,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Vt([t,f.pathname])),(d.replace?r.replace:r.push)(f,d.state,d)},[t,r,i,o,e])}const D0=R.createContext(null);function M0(e){let t=R.useContext(Nn).outlet;return t&&R.createElement(D0.Provider,{value:e},t)}function up(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=R.useContext(Jn),{matches:l}=R.useContext(Nn),{pathname:o}=Hl(),i=JSON.stringify(Su(l,r.v7_relativeSplatPath));return R.useMemo(()=>Eu(e,JSON.parse(i),o,n==="path"),[e,i,o,n])}function A0(e,t,n,r){Bl()||J(!1);let{navigator:l}=R.useContext(Jn),{matches:o}=R.useContext(Nn),i=o[o.length-1],a=i?i.params:{};i&&i.pathname;let s=i?i.pathnameBase:"/";i&&i.route;let u=Hl(),d;d=u;let f=d.pathname||"/",p=f;if(s!=="/"){let v=s.replace(/^\//,"").split("/");p="/"+f.replace(/^\//,"").split("/").slice(v.length).join("/")}let S=Tn(e,{pathname:p});return I0(S&&S.map(v=>Object.assign({},v,{params:Object.assign({},a,v.params),pathname:Vt([s,l.encodeLocation?l.encodeLocation(v.pathname).pathname:v.pathname]),pathnameBase:v.pathnameBase==="/"?s:Vt([s,l.encodeLocation?l.encodeLocation(v.pathnameBase).pathname:v.pathnameBase])})),o,n,r)}function F0(){let e=V0(),t=Ol(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,l={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},t),n?R.createElement("pre",{style:l},n):null,null)}const z0=R.createElement(F0,null);class U0 extends R.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?R.createElement(Nn.Provider,{value:this.props.routeContext},R.createElement(ap.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function b0(e){let{routeContext:t,match:n,children:r}=e,l=R.useContext(Oi);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),R.createElement(Nn.Provider,{value:t},r)}function I0(e,t,n,r){var l;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,a=(l=n)==null?void 0:l.errors;if(a!=null){let d=i.findIndex(f=>f.route.id&&(a==null?void 0:a[f.route.id])!==void 0);d>=0||J(!1),i=i.slice(0,Math.min(i.length,d+1))}let s=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<i.length;d++){let f=i[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:p,errors:S}=n,x=f.route.loader&&p[f.route.id]===void 0&&(!S||S[f.route.id]===void 0);if(f.route.lazy||x){s=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((d,f,p)=>{let S,x=!1,v=null,C=null;n&&(S=a&&f.route.id?a[f.route.id]:void 0,v=f.route.errorElement||z0,s&&(u<0&&p===0?(K0("route-fallback"),x=!0,C=null):u===p&&(x=!0,C=f.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,p+1)),h=()=>{let y;return S?y=v:x?y=C:f.route.Component?y=R.createElement(f.route.Component,null):f.route.element?y=f.route.element:y=d,R.createElement(b0,{match:f,routeContext:{outlet:d,matches:m,isDataRoute:n!=null},children:y})};return n&&(f.route.ErrorBoundary||f.route.errorElement||p===0)?R.createElement(U0,{location:n.location,revalidation:n.revalidation,component:v,error:S,children:h(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):h()},null)}var cp=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(cp||{}),dp=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(dp||{});function $0(e){let t=R.useContext(Oi);return t||J(!1),t}function B0(e){let t=R.useContext(ip);return t||J(!1),t}function H0(e){let t=R.useContext(Nn);return t||J(!1),t}function fp(e){let t=H0(),n=t.matches[t.matches.length-1];return n.route.id||J(!1),n.route.id}function V0(){var e;let t=R.useContext(ap),n=B0(dp.UseRouteError),r=fp();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function W0(){let{router:e}=$0(cp.UseNavigateStable),t=fp(),n=R.useRef(!1);return sp(()=>{n.current=!0}),R.useCallback(function(l,o){o===void 0&&(o={}),n.current&&(typeof l=="number"?e.navigate(l):e.navigate(l,ui({fromRouteId:t},o)))},[e,t])}const md={};function K0(e,t,n){md[e]||(md[e]=!0)}function Q0(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function q0(e){return M0(e.context)}function Y0(e){let{basename:t="/",children:n=null,location:r,navigationType:l=ke.Pop,navigator:o,static:i=!1,future:a}=e;Bl()&&J(!1);let s=t.replace(/^\/*/,"/"),u=R.useMemo(()=>({basename:s,navigator:o,static:i,future:ui({v7_relativeSplatPath:!1},a)}),[s,a,o,i]);typeof r=="string"&&(r=kn(r));let{pathname:d="/",search:f="",hash:p="",state:S=null,key:x="default"}=r,v=R.useMemo(()=>{let C=Mr(d,s);return C==null?null:{location:{pathname:C,search:f,hash:p,state:S,key:x},navigationType:l}},[s,d,f,p,S,x,l]);return v==null?null:R.createElement(Jn.Provider,{value:u},R.createElement(ju.Provider,{children:n,value:v}))}new Promise(()=>{});function J0(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:R.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:R.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:R.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Dl(){return Dl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dl.apply(this,arguments)}function X0(e,t){if(e==null)return{};var n={},r=Object.keys(e),l,o;for(o=0;o<r.length;o++)l=r[o],!(t.indexOf(l)>=0)&&(n[l]=e[l]);return n}function G0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Z0(e,t){return e.button===0&&(!t||t==="_self")&&!G0(e)}const ev=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],tv="6";try{window.__reactRouterVersion=tv}catch{}function nv(e,t){return d0({basename:void 0,future:Dl({},void 0,{v7_prependBasename:!0}),history:Ag({window:void 0}),hydrationData:rv(),routes:e,mapRouteProperties:J0,dataStrategy:void 0,patchRoutesOnNavigation:void 0,window:void 0}).initialize()}function rv(){var e;let t=(e=window)==null?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=Dl({},t,{errors:lv(t.errors)})),t}function lv(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,l]of t)if(l&&l.__type==="RouteErrorResponse")n[r]=new si(l.status,l.statusText,l.data,l.internal===!0);else if(l&&l.__type==="Error"){if(l.__subType){let o=window[l.__subType];if(typeof o=="function")try{let i=new o(l.message);i.stack="",n[r]=i}catch{}}if(n[r]==null){let o=new Error(l.message);o.stack="",n[r]=o}}else n[r]=l;return n}const ov=R.createContext({isTransitioning:!1}),iv=R.createContext(new Map),av="startTransition",yd=Em[av],sv="flushSync",gd=Mg[sv];function uv(e){yd?yd(e):e()}function Xr(e){gd?gd(e):e()}class cv{constructor(){this.status="pending",this.promise=new Promise((t,n)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",t(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",n(r))}})}}function dv(e){let{fallbackElement:t,router:n,future:r}=e,[l,o]=R.useState(n.state),[i,a]=R.useState(),[s,u]=R.useState({isTransitioning:!1}),[d,f]=R.useState(),[p,S]=R.useState(),[x,v]=R.useState(),C=R.useRef(new Map),{v7_startTransition:m}=r||{},h=R.useCallback(_=>{m?uv(_):_()},[m]),y=R.useCallback((_,$)=>{let{deletedFetchers:A,flushSync:re,viewTransitionOpts:ie}=$;_.fetchers.forEach((Te,vt)=>{Te.data!==void 0&&C.current.set(vt,Te.data)}),A.forEach(Te=>C.current.delete(Te));let Ce=n.window==null||n.window.document==null||typeof n.window.document.startViewTransition!="function";if(!ie||Ce){re?Xr(()=>o(_)):h(()=>o(_));return}if(re){Xr(()=>{p&&(d&&d.resolve(),p.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:ie.currentLocation,nextLocation:ie.nextLocation})});let Te=n.window.document.startViewTransition(()=>{Xr(()=>o(_))});Te.finished.finally(()=>{Xr(()=>{f(void 0),S(void 0),a(void 0),u({isTransitioning:!1})})}),Xr(()=>S(Te));return}p?(d&&d.resolve(),p.skipTransition(),v({state:_,currentLocation:ie.currentLocation,nextLocation:ie.nextLocation})):(a(_),u({isTransitioning:!0,flushSync:!1,currentLocation:ie.currentLocation,nextLocation:ie.nextLocation}))},[n.window,p,d,C,h]);R.useLayoutEffect(()=>n.subscribe(y),[n,y]),R.useEffect(()=>{s.isTransitioning&&!s.flushSync&&f(new cv)},[s]),R.useEffect(()=>{if(d&&i&&n.window){let _=i,$=d.promise,A=n.window.document.startViewTransition(async()=>{h(()=>o(_)),await $});A.finished.finally(()=>{f(void 0),S(void 0),a(void 0),u({isTransitioning:!1})}),S(A)}},[h,i,d,n.window]),R.useEffect(()=>{d&&i&&l.location.key===i.location.key&&d.resolve()},[d,p,l.location,i]),R.useEffect(()=>{!s.isTransitioning&&x&&(a(x.state),u({isTransitioning:!0,flushSync:!1,currentLocation:x.currentLocation,nextLocation:x.nextLocation}),v(void 0))},[s.isTransitioning,x]),R.useEffect(()=>{},[]);let j=R.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:_=>n.navigate(_),push:(_,$,A)=>n.navigate(_,{state:$,preventScrollReset:A==null?void 0:A.preventScrollReset}),replace:(_,$,A)=>n.navigate(_,{replace:!0,state:$,preventScrollReset:A==null?void 0:A.preventScrollReset})}),[n]),P=n.basename||"/",T=R.useMemo(()=>({router:n,navigator:j,static:!1,basename:P}),[n,j,P]),w=R.useMemo(()=>({v7_relativeSplatPath:n.future.v7_relativeSplatPath}),[n.future.v7_relativeSplatPath]);return R.useEffect(()=>Q0(r,n.future),[r,n.future]),R.createElement(R.Fragment,null,R.createElement(Oi.Provider,{value:T},R.createElement(ip.Provider,{value:l},R.createElement(iv.Provider,{value:C.current},R.createElement(ov.Provider,{value:s},R.createElement(Y0,{basename:P,location:l.location,navigationType:l.historyAction,navigator:j,future:w},l.initialized||n.future.v7_partialHydration?R.createElement(fv,{routes:n.routes,future:n.future,state:l}):t))))),null)}const fv=R.memo(hv);function hv(e){let{routes:t,future:n,state:r}=e;return A0(t,void 0,r,n)}const pv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",mv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,hp=R.forwardRef(function(t,n){let{onClick:r,relative:l,reloadDocument:o,replace:i,state:a,target:s,to:u,preventScrollReset:d,viewTransition:f}=t,p=X0(t,ev),{basename:S}=R.useContext(Jn),x,v=!1;if(typeof u=="string"&&mv.test(u)&&(x=u,pv))try{let y=new URL(window.location.href),j=u.startsWith("//")?new URL(y.protocol+u):new URL(u),P=Mr(j.pathname,S);j.origin===y.origin&&P!=null?u=P+j.search+j.hash:v=!0}catch{}let C=L0(u,{relative:l}),m=yv(u,{replace:i,state:a,target:s,preventScrollReset:d,relative:l,viewTransition:f});function h(y){r&&r(y),y.defaultPrevented||m(y)}return R.createElement("a",Dl({},p,{href:x||C,onClick:v||o?r:h,ref:n,target:s}))});var vd;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(vd||(vd={}));var xd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(xd||(xd={}));function yv(e,t){let{target:n,replace:r,state:l,preventScrollReset:o,relative:i,viewTransition:a}=t===void 0?{}:t,s=Xn(),u=Hl(),d=up(e,{relative:i});return R.useCallback(f=>{if(Z0(f,n)){f.preventDefault();let p=r!==void 0?r:Kn(u)===Kn(d);s(e,{replace:p,state:l,preventScrollReset:o,relative:i,viewTransition:a})}},[u,s,d,r,l,n,e,o,i,a])}/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gv=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),pp=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var vv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xv=R.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:l="",children:o,iconNode:i,...a},s)=>R.createElement("svg",{ref:s,...vv,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:pp("lucide",l),...a},[...i.map(([u,d])=>R.createElement(u,d)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=(e,t)=>{const n=R.forwardRef(({className:r,...l},o)=>R.createElement(xv,{ref:o,iconNode:t,className:pp(`lucide-${gv(e)}`,r),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=xe("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=xe("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wv=xe("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mp=xe("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=xe("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sv=xe("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yp=xe("Files",[["path",{d:"M20 7h-3a2 2 0 0 1-2-2V2",key:"x099mo"}],["path",{d:"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z",key:"18t6ie"}],["path",{d:"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8",key:"1nja0z"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ev=xe("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kv=xe("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nv=xe("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cv=xe("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jv=xe("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=xe("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rv=xe("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=xe("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Di=xe("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _v=xe("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pv=xe("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tv=xe("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gp=xe("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=xe("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=xe("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function vp(e,t){return function(){return e.apply(t,arguments)}}const{toString:Lv}=Object.prototype,{getPrototypeOf:Ru}=Object,{iterator:Mi,toStringTag:xp}=Symbol,Ai=(e=>t=>{const n=Lv.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),_t=e=>(e=e.toLowerCase(),t=>Ai(t)===e),Fi=e=>t=>typeof t===e,{isArray:Ar}=Array,Fl=Fi("undefined");function Wl(e){return e!==null&&!Fl(e)&&e.constructor!==null&&!Fl(e.constructor)&&et(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const wp=_t("ArrayBuffer");function Ov(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&wp(e.buffer),t}const Dv=Fi("string"),et=Fi("function"),Sp=Fi("number"),Kl=e=>e!==null&&typeof e=="object",Mv=e=>e===!0||e===!1,Mo=e=>{if(Ai(e)!=="object")return!1;const t=Ru(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(xp in e)&&!(Mi in e)},Av=e=>{if(!Kl(e)||Wl(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Fv=_t("Date"),zv=_t("File"),Uv=_t("Blob"),bv=_t("FileList"),Iv=e=>Kl(e)&&et(e.pipe),$v=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||et(e.append)&&((t=Ai(e))==="formdata"||t==="object"&&et(e.toString)&&e.toString()==="[object FormData]"))},Bv=_t("URLSearchParams"),[Hv,Vv,Wv,Kv]=["ReadableStream","Request","Response","Headers"].map(_t),Qv=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ql(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,l;if(typeof e!="object"&&(e=[e]),Ar(e))for(r=0,l=e.length;r<l;r++)t.call(null,e[r],r,e);else{if(Wl(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function Ep(e,t){if(Wl(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,l;for(;r-- >0;)if(l=n[r],t===l.toLowerCase())return l;return null}const Fn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,kp=e=>!Fl(e)&&e!==Fn;function ws(){const{caseless:e}=kp(this)&&this||{},t={},n=(r,l)=>{const o=e&&Ep(t,l)||l;Mo(t[o])&&Mo(r)?t[o]=ws(t[o],r):Mo(r)?t[o]=ws({},r):Ar(r)?t[o]=r.slice():t[o]=r};for(let r=0,l=arguments.length;r<l;r++)arguments[r]&&Ql(arguments[r],n);return t}const qv=(e,t,n,{allOwnKeys:r}={})=>(Ql(t,(l,o)=>{n&&et(l)?e[o]=vp(l,n):e[o]=l},{allOwnKeys:r}),e),Yv=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Jv=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Xv=(e,t,n,r)=>{let l,o,i;const a={};if(t=t||{},e==null)return t;do{for(l=Object.getOwnPropertyNames(e),o=l.length;o-- >0;)i=l[o],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&Ru(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Gv=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Zv=e=>{if(!e)return null;if(Ar(e))return e;let t=e.length;if(!Sp(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},ex=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ru(Uint8Array)),tx=(e,t)=>{const r=(e&&e[Mi]).call(e);let l;for(;(l=r.next())&&!l.done;){const o=l.value;t.call(e,o[0],o[1])}},nx=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},rx=_t("HTMLFormElement"),lx=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,l){return r.toUpperCase()+l}),wd=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ox=_t("RegExp"),Np=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Ql(n,(l,o)=>{let i;(i=t(l,o,e))!==!1&&(r[o]=i||l)}),Object.defineProperties(e,r)},ix=e=>{Np(e,(t,n)=>{if(et(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(et(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ax=(e,t)=>{const n={},r=l=>{l.forEach(o=>{n[o]=!0})};return Ar(e)?r(e):r(String(e).split(t)),n},sx=()=>{},ux=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function cx(e){return!!(e&&et(e.append)&&e[xp]==="FormData"&&e[Mi])}const dx=e=>{const t=new Array(10),n=(r,l)=>{if(Kl(r)){if(t.indexOf(r)>=0)return;if(Wl(r))return r;if(!("toJSON"in r)){t[l]=r;const o=Ar(r)?[]:{};return Ql(r,(i,a)=>{const s=n(i,l+1);!Fl(s)&&(o[a]=s)}),t[l]=void 0,o}}return r};return n(e,0)},fx=_t("AsyncFunction"),hx=e=>e&&(Kl(e)||et(e))&&et(e.then)&&et(e.catch),Cp=((e,t)=>e?setImmediate:t?((n,r)=>(Fn.addEventListener("message",({source:l,data:o})=>{l===Fn&&o===n&&r.length&&r.shift()()},!1),l=>{r.push(l),Fn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",et(Fn.postMessage)),px=typeof queueMicrotask<"u"?queueMicrotask.bind(Fn):typeof process<"u"&&process.nextTick||Cp,mx=e=>e!=null&&et(e[Mi]),N={isArray:Ar,isArrayBuffer:wp,isBuffer:Wl,isFormData:$v,isArrayBufferView:Ov,isString:Dv,isNumber:Sp,isBoolean:Mv,isObject:Kl,isPlainObject:Mo,isEmptyObject:Av,isReadableStream:Hv,isRequest:Vv,isResponse:Wv,isHeaders:Kv,isUndefined:Fl,isDate:Fv,isFile:zv,isBlob:Uv,isRegExp:ox,isFunction:et,isStream:Iv,isURLSearchParams:Bv,isTypedArray:ex,isFileList:bv,forEach:Ql,merge:ws,extend:qv,trim:Qv,stripBOM:Yv,inherits:Jv,toFlatObject:Xv,kindOf:Ai,kindOfTest:_t,endsWith:Gv,toArray:Zv,forEachEntry:tx,matchAll:nx,isHTMLForm:rx,hasOwnProperty:wd,hasOwnProp:wd,reduceDescriptors:Np,freezeMethods:ix,toObjectSet:ax,toCamelCase:lx,noop:sx,toFiniteNumber:ux,findKey:Ep,global:Fn,isContextDefined:kp,isSpecCompliantForm:cx,toJSONObject:dx,isAsyncFn:fx,isThenable:hx,setImmediate:Cp,asap:px,isIterable:mx};function K(e,t,n,r,l){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),l&&(this.response=l,this.status=l.status?l.status:null)}N.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:N.toJSONObject(this.config),code:this.code,status:this.status}}});const jp=K.prototype,Rp={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Rp[e]={value:e}});Object.defineProperties(K,Rp);Object.defineProperty(jp,"isAxiosError",{value:!0});K.from=(e,t,n,r,l,o)=>{const i=Object.create(jp);return N.toFlatObject(e,i,function(s){return s!==Error.prototype},a=>a!=="isAxiosError"),K.call(i,e.message,t,n,r,l),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const yx=null;function Ss(e){return N.isPlainObject(e)||N.isArray(e)}function _p(e){return N.endsWith(e,"[]")?e.slice(0,-2):e}function Sd(e,t,n){return e?e.concat(t).map(function(l,o){return l=_p(l),!n&&o?"["+l+"]":l}).join(n?".":""):t}function gx(e){return N.isArray(e)&&!e.some(Ss)}const vx=N.toFlatObject(N,{},null,function(t){return/^is[A-Z]/.test(t)});function zi(e,t,n){if(!N.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=N.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,C){return!N.isUndefined(C[v])});const r=n.metaTokens,l=n.visitor||d,o=n.dots,i=n.indexes,s=(n.Blob||typeof Blob<"u"&&Blob)&&N.isSpecCompliantForm(t);if(!N.isFunction(l))throw new TypeError("visitor must be a function");function u(x){if(x===null)return"";if(N.isDate(x))return x.toISOString();if(N.isBoolean(x))return x.toString();if(!s&&N.isBlob(x))throw new K("Blob is not supported. Use a Buffer instead.");return N.isArrayBuffer(x)||N.isTypedArray(x)?s&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function d(x,v,C){let m=x;if(x&&!C&&typeof x=="object"){if(N.endsWith(v,"{}"))v=r?v:v.slice(0,-2),x=JSON.stringify(x);else if(N.isArray(x)&&gx(x)||(N.isFileList(x)||N.endsWith(v,"[]"))&&(m=N.toArray(x)))return v=_p(v),m.forEach(function(y,j){!(N.isUndefined(y)||y===null)&&t.append(i===!0?Sd([v],j,o):i===null?v:v+"[]",u(y))}),!1}return Ss(x)?!0:(t.append(Sd(C,v,o),u(x)),!1)}const f=[],p=Object.assign(vx,{defaultVisitor:d,convertValue:u,isVisitable:Ss});function S(x,v){if(!N.isUndefined(x)){if(f.indexOf(x)!==-1)throw Error("Circular reference detected in "+v.join("."));f.push(x),N.forEach(x,function(m,h){(!(N.isUndefined(m)||m===null)&&l.call(t,m,N.isString(h)?h.trim():h,v,p))===!0&&S(m,v?v.concat(h):[h])}),f.pop()}}if(!N.isObject(e))throw new TypeError("data must be an object");return S(e),t}function Ed(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function _u(e,t){this._pairs=[],e&&zi(e,this,t)}const Pp=_u.prototype;Pp.append=function(t,n){this._pairs.push([t,n])};Pp.toString=function(t){const n=t?function(r){return t.call(this,r,Ed)}:Ed;return this._pairs.map(function(l){return n(l[0])+"="+n(l[1])},"").join("&")};function xx(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Tp(e,t,n){if(!t)return e;const r=n&&n.encode||xx;N.isFunction(n)&&(n={serialize:n});const l=n&&n.serialize;let o;if(l?o=l(t,n):o=N.isURLSearchParams(t)?t.toString():new _u(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class kd{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){N.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Lp={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},wx=typeof URLSearchParams<"u"?URLSearchParams:_u,Sx=typeof FormData<"u"?FormData:null,Ex=typeof Blob<"u"?Blob:null,kx={isBrowser:!0,classes:{URLSearchParams:wx,FormData:Sx,Blob:Ex},protocols:["http","https","file","blob","url","data"]},Pu=typeof window<"u"&&typeof document<"u",Es=typeof navigator=="object"&&navigator||void 0,Nx=Pu&&(!Es||["ReactNative","NativeScript","NS"].indexOf(Es.product)<0),Cx=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",jx=Pu&&window.location.href||"http://localhost",Rx=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Pu,hasStandardBrowserEnv:Nx,hasStandardBrowserWebWorkerEnv:Cx,navigator:Es,origin:jx},Symbol.toStringTag,{value:"Module"})),Ue={...Rx,...kx};function _x(e,t){return zi(e,new Ue.classes.URLSearchParams,{visitor:function(n,r,l,o){return Ue.isNode&&N.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function Px(e){return N.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tx(e){const t={},n=Object.keys(e);let r;const l=n.length;let o;for(r=0;r<l;r++)o=n[r],t[o]=e[o];return t}function Op(e){function t(n,r,l,o){let i=n[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),s=o>=n.length;return i=!i&&N.isArray(l)?l.length:i,s?(N.hasOwnProp(l,i)?l[i]=[l[i],r]:l[i]=r,!a):((!l[i]||!N.isObject(l[i]))&&(l[i]=[]),t(n,r,l[i],o)&&N.isArray(l[i])&&(l[i]=Tx(l[i])),!a)}if(N.isFormData(e)&&N.isFunction(e.entries)){const n={};return N.forEachEntry(e,(r,l)=>{t(Px(r),l,n,0)}),n}return null}function Lx(e,t,n){if(N.isString(e))try{return(t||JSON.parse)(e),N.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const ql={transitional:Lp,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",l=r.indexOf("application/json")>-1,o=N.isObject(t);if(o&&N.isHTMLForm(t)&&(t=new FormData(t)),N.isFormData(t))return l?JSON.stringify(Op(t)):t;if(N.isArrayBuffer(t)||N.isBuffer(t)||N.isStream(t)||N.isFile(t)||N.isBlob(t)||N.isReadableStream(t))return t;if(N.isArrayBufferView(t))return t.buffer;if(N.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return _x(t,this.formSerializer).toString();if((a=N.isFileList(t))||r.indexOf("multipart/form-data")>-1){const s=this.env&&this.env.FormData;return zi(a?{"files[]":t}:t,s&&new s,this.formSerializer)}}return o||l?(n.setContentType("application/json",!1),Lx(t)):t}],transformResponse:[function(t){const n=this.transitional||ql.transitional,r=n&&n.forcedJSONParsing,l=this.responseType==="json";if(N.isResponse(t)||N.isReadableStream(t))return t;if(t&&N.isString(t)&&(r&&!this.responseType||l)){const i=!(n&&n.silentJSONParsing)&&l;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?K.from(a,K.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ue.classes.FormData,Blob:Ue.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};N.forEach(["delete","get","head","post","put","patch"],e=>{ql.headers[e]={}});const Ox=N.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Dx=e=>{const t={};let n,r,l;return e&&e.split(`
`).forEach(function(i){l=i.indexOf(":"),n=i.substring(0,l).trim().toLowerCase(),r=i.substring(l+1).trim(),!(!n||t[n]&&Ox[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Nd=Symbol("internals");function Gr(e){return e&&String(e).trim().toLowerCase()}function Ao(e){return e===!1||e==null?e:N.isArray(e)?e.map(Ao):String(e)}function Mx(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Ax=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function wa(e,t,n,r,l){if(N.isFunction(r))return r.call(this,t,n);if(l&&(t=n),!!N.isString(t)){if(N.isString(r))return t.indexOf(r)!==-1;if(N.isRegExp(r))return r.test(t)}}function Fx(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function zx(e,t){const n=N.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(l,o,i){return this[r].call(this,t,l,o,i)},configurable:!0})})}let tt=class{constructor(t){t&&this.set(t)}set(t,n,r){const l=this;function o(a,s,u){const d=Gr(s);if(!d)throw new Error("header name must be a non-empty string");const f=N.findKey(l,d);(!f||l[f]===void 0||u===!0||u===void 0&&l[f]!==!1)&&(l[f||s]=Ao(a))}const i=(a,s)=>N.forEach(a,(u,d)=>o(u,d,s));if(N.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(N.isString(t)&&(t=t.trim())&&!Ax(t))i(Dx(t),n);else if(N.isObject(t)&&N.isIterable(t)){let a={},s,u;for(const d of t){if(!N.isArray(d))throw TypeError("Object iterator must return a key-value pair");a[u=d[0]]=(s=a[u])?N.isArray(s)?[...s,d[1]]:[s,d[1]]:d[1]}i(a,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=Gr(t),t){const r=N.findKey(this,t);if(r){const l=this[r];if(!n)return l;if(n===!0)return Mx(l);if(N.isFunction(n))return n.call(this,l,r);if(N.isRegExp(n))return n.exec(l);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Gr(t),t){const r=N.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||wa(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let l=!1;function o(i){if(i=Gr(i),i){const a=N.findKey(r,i);a&&(!n||wa(r,r[a],a,n))&&(delete r[a],l=!0)}}return N.isArray(t)?t.forEach(o):o(t),l}clear(t){const n=Object.keys(this);let r=n.length,l=!1;for(;r--;){const o=n[r];(!t||wa(this,this[o],o,t,!0))&&(delete this[o],l=!0)}return l}normalize(t){const n=this,r={};return N.forEach(this,(l,o)=>{const i=N.findKey(r,o);if(i){n[i]=Ao(l),delete n[o];return}const a=t?Fx(o):String(o).trim();a!==o&&delete n[o],n[a]=Ao(l),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return N.forEach(this,(r,l)=>{r!=null&&r!==!1&&(n[l]=t&&N.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(l=>r.set(l)),r}static accessor(t){const r=(this[Nd]=this[Nd]={accessors:{}}).accessors,l=this.prototype;function o(i){const a=Gr(i);r[a]||(zx(l,i),r[a]=!0)}return N.isArray(t)?t.forEach(o):o(t),this}};tt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);N.reduceDescriptors(tt.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});N.freezeMethods(tt);function Sa(e,t){const n=this||ql,r=t||n,l=tt.from(r.headers);let o=r.data;return N.forEach(e,function(a){o=a.call(n,o,l.normalize(),t?t.status:void 0)}),l.normalize(),o}function Dp(e){return!!(e&&e.__CANCEL__)}function Fr(e,t,n){K.call(this,e??"canceled",K.ERR_CANCELED,t,n),this.name="CanceledError"}N.inherits(Fr,K,{__CANCEL__:!0});function Mp(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new K("Request failed with status code "+n.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Ux(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function bx(e,t){e=e||10;const n=new Array(e),r=new Array(e);let l=0,o=0,i;return t=t!==void 0?t:1e3,function(s){const u=Date.now(),d=r[o];i||(i=u),n[l]=s,r[l]=u;let f=o,p=0;for(;f!==l;)p+=n[f++],f=f%e;if(l=(l+1)%e,l===o&&(o=(o+1)%e),u-i<t)return;const S=d&&u-d;return S?Math.round(p*1e3/S):void 0}}function Ix(e,t){let n=0,r=1e3/t,l,o;const i=(u,d=Date.now())=>{n=d,l=null,o&&(clearTimeout(o),o=null),e(...u)};return[(...u)=>{const d=Date.now(),f=d-n;f>=r?i(u,d):(l=u,o||(o=setTimeout(()=>{o=null,i(l)},r-f)))},()=>l&&i(l)]}const pi=(e,t,n=3)=>{let r=0;const l=bx(50,250);return Ix(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,s=i-r,u=l(s),d=i<=a;r=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:s,rate:u||void 0,estimated:u&&a&&d?(a-i)/u:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},Cd=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},jd=e=>(...t)=>N.asap(()=>e(...t)),$x=Ue.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ue.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ue.origin),Ue.navigator&&/(msie|trident)/i.test(Ue.navigator.userAgent)):()=>!0,Bx=Ue.hasStandardBrowserEnv?{write(e,t,n,r,l,o){const i=[e+"="+encodeURIComponent(t)];N.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),N.isString(r)&&i.push("path="+r),N.isString(l)&&i.push("domain="+l),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Hx(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Vx(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ap(e,t,n){let r=!Hx(t);return e&&(r||n==!1)?Vx(e,t):t}const Rd=e=>e instanceof tt?{...e}:e;function Qn(e,t){t=t||{};const n={};function r(u,d,f,p){return N.isPlainObject(u)&&N.isPlainObject(d)?N.merge.call({caseless:p},u,d):N.isPlainObject(d)?N.merge({},d):N.isArray(d)?d.slice():d}function l(u,d,f,p){if(N.isUndefined(d)){if(!N.isUndefined(u))return r(void 0,u,f,p)}else return r(u,d,f,p)}function o(u,d){if(!N.isUndefined(d))return r(void 0,d)}function i(u,d){if(N.isUndefined(d)){if(!N.isUndefined(u))return r(void 0,u)}else return r(void 0,d)}function a(u,d,f){if(f in t)return r(u,d);if(f in e)return r(void 0,u)}const s={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(u,d,f)=>l(Rd(u),Rd(d),f,!0)};return N.forEach(Object.keys({...e,...t}),function(d){const f=s[d]||l,p=f(e[d],t[d],d);N.isUndefined(p)&&f!==a||(n[d]=p)}),n}const Fp=e=>{const t=Qn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:l,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=tt.from(i),t.url=Tp(Ap(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let s;if(N.isFormData(n)){if(Ue.hasStandardBrowserEnv||Ue.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((s=i.getContentType())!==!1){const[u,...d]=s?s.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...d].join("; "))}}if(Ue.hasStandardBrowserEnv&&(r&&N.isFunction(r)&&(r=r(t)),r||r!==!1&&$x(t.url))){const u=l&&o&&Bx.read(o);u&&i.set(l,u)}return t},Wx=typeof XMLHttpRequest<"u",Kx=Wx&&function(e){return new Promise(function(n,r){const l=Fp(e);let o=l.data;const i=tt.from(l.headers).normalize();let{responseType:a,onUploadProgress:s,onDownloadProgress:u}=l,d,f,p,S,x;function v(){S&&S(),x&&x(),l.cancelToken&&l.cancelToken.unsubscribe(d),l.signal&&l.signal.removeEventListener("abort",d)}let C=new XMLHttpRequest;C.open(l.method.toUpperCase(),l.url,!0),C.timeout=l.timeout;function m(){if(!C)return;const y=tt.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),P={data:!a||a==="text"||a==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:y,config:e,request:C};Mp(function(w){n(w),v()},function(w){r(w),v()},P),C=null}"onloadend"in C?C.onloadend=m:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(m)},C.onabort=function(){C&&(r(new K("Request aborted",K.ECONNABORTED,e,C)),C=null)},C.onerror=function(){r(new K("Network Error",K.ERR_NETWORK,e,C)),C=null},C.ontimeout=function(){let j=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded";const P=l.transitional||Lp;l.timeoutErrorMessage&&(j=l.timeoutErrorMessage),r(new K(j,P.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,C)),C=null},o===void 0&&i.setContentType(null),"setRequestHeader"in C&&N.forEach(i.toJSON(),function(j,P){C.setRequestHeader(P,j)}),N.isUndefined(l.withCredentials)||(C.withCredentials=!!l.withCredentials),a&&a!=="json"&&(C.responseType=l.responseType),u&&([p,x]=pi(u,!0),C.addEventListener("progress",p)),s&&C.upload&&([f,S]=pi(s),C.upload.addEventListener("progress",f),C.upload.addEventListener("loadend",S)),(l.cancelToken||l.signal)&&(d=y=>{C&&(r(!y||y.type?new Fr(null,e,C):y),C.abort(),C=null)},l.cancelToken&&l.cancelToken.subscribe(d),l.signal&&(l.signal.aborted?d():l.signal.addEventListener("abort",d)));const h=Ux(l.url);if(h&&Ue.protocols.indexOf(h)===-1){r(new K("Unsupported protocol "+h+":",K.ERR_BAD_REQUEST,e));return}C.send(o||null)})},Qx=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,l;const o=function(u){if(!l){l=!0,a();const d=u instanceof Error?u:this.reason;r.abort(d instanceof K?d:new Fr(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new K(`timeout ${t} of ms exceeded`,K.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:s}=r;return s.unsubscribe=()=>N.asap(a),s}},qx=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,l;for(;r<n;)l=r+t,yield e.slice(r,l),r=l},Yx=async function*(e,t){for await(const n of Jx(e))yield*qx(n,t)},Jx=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},_d=(e,t,n,r)=>{const l=Yx(e,t);let o=0,i,a=s=>{i||(i=!0,r&&r(s))};return new ReadableStream({async pull(s){try{const{done:u,value:d}=await l.next();if(u){a(),s.close();return}let f=d.byteLength;if(n){let p=o+=f;n(p)}s.enqueue(new Uint8Array(d))}catch(u){throw a(u),u}},cancel(s){return a(s),l.return()}},{highWaterMark:2})},Ui=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",zp=Ui&&typeof ReadableStream=="function",Xx=Ui&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Up=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Gx=zp&&Up(()=>{let e=!1;const t=new Request(Ue.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Pd=64*1024,ks=zp&&Up(()=>N.isReadableStream(new Response("").body)),mi={stream:ks&&(e=>e.body)};Ui&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!mi[t]&&(mi[t]=N.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new K(`Response type '${t}' is not supported`,K.ERR_NOT_SUPPORT,r)})})})(new Response);const Zx=async e=>{if(e==null)return 0;if(N.isBlob(e))return e.size;if(N.isSpecCompliantForm(e))return(await new Request(Ue.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(N.isArrayBufferView(e)||N.isArrayBuffer(e))return e.byteLength;if(N.isURLSearchParams(e)&&(e=e+""),N.isString(e))return(await Xx(e)).byteLength},e1=async(e,t)=>{const n=N.toFiniteNumber(e.getContentLength());return n??Zx(t)},t1=Ui&&(async e=>{let{url:t,method:n,data:r,signal:l,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:s,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:p}=Fp(e);u=u?(u+"").toLowerCase():"text";let S=Qx([l,o&&o.toAbortSignal()],i),x;const v=S&&S.unsubscribe&&(()=>{S.unsubscribe()});let C;try{if(s&&Gx&&n!=="get"&&n!=="head"&&(C=await e1(d,r))!==0){let P=new Request(t,{method:"POST",body:r,duplex:"half"}),T;if(N.isFormData(r)&&(T=P.headers.get("content-type"))&&d.setContentType(T),P.body){const[w,_]=Cd(C,pi(jd(s)));r=_d(P.body,Pd,w,_)}}N.isString(f)||(f=f?"include":"omit");const m="credentials"in Request.prototype;x=new Request(t,{...p,signal:S,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:m?f:void 0});let h=await fetch(x,p);const y=ks&&(u==="stream"||u==="response");if(ks&&(a||y&&v)){const P={};["status","statusText","headers"].forEach($=>{P[$]=h[$]});const T=N.toFiniteNumber(h.headers.get("content-length")),[w,_]=a&&Cd(T,pi(jd(a),!0))||[];h=new Response(_d(h.body,Pd,w,()=>{_&&_(),v&&v()}),P)}u=u||"text";let j=await mi[N.findKey(mi,u)||"text"](h,e);return!y&&v&&v(),await new Promise((P,T)=>{Mp(P,T,{data:j,headers:tt.from(h.headers),status:h.status,statusText:h.statusText,config:e,request:x})})}catch(m){throw v&&v(),m&&m.name==="TypeError"&&/Load failed|fetch/i.test(m.message)?Object.assign(new K("Network Error",K.ERR_NETWORK,e,x),{cause:m.cause||m}):K.from(m,m&&m.code,e,x)}}),Ns={http:yx,xhr:Kx,fetch:t1};N.forEach(Ns,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Td=e=>`- ${e}`,n1=e=>N.isFunction(e)||e===null||e===!1,bp={getAdapter:e=>{e=N.isArray(e)?e:[e];const{length:t}=e;let n,r;const l={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!n1(n)&&(r=Ns[(i=String(n)).toLowerCase()],r===void 0))throw new K(`Unknown adapter '${i}'`);if(r)break;l[i||"#"+o]=r}if(!r){const o=Object.entries(l).map(([a,s])=>`adapter ${a} `+(s===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Td).join(`
`):" "+Td(o[0]):"as no adapter specified";throw new K("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Ns};function Ea(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Fr(null,e)}function Ld(e){return Ea(e),e.headers=tt.from(e.headers),e.data=Sa.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),bp.getAdapter(e.adapter||ql.adapter)(e).then(function(r){return Ea(e),r.data=Sa.call(e,e.transformResponse,r),r.headers=tt.from(r.headers),r},function(r){return Dp(r)||(Ea(e),r&&r.response&&(r.response.data=Sa.call(e,e.transformResponse,r.response),r.response.headers=tt.from(r.response.headers))),Promise.reject(r)})}const Ip="1.11.0",bi={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{bi[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Od={};bi.transitional=function(t,n,r){function l(o,i){return"[Axios v"+Ip+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,a)=>{if(t===!1)throw new K(l(i," has been removed"+(n?" in "+n:"")),K.ERR_DEPRECATED);return n&&!Od[i]&&(Od[i]=!0,console.warn(l(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,a):!0}};bi.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function r1(e,t,n){if(typeof e!="object")throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let l=r.length;for(;l-- >0;){const o=r[l],i=t[o];if(i){const a=e[o],s=a===void 0||i(a,o,e);if(s!==!0)throw new K("option "+o+" must be "+s,K.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new K("Unknown option "+o,K.ERR_BAD_OPTION)}}const Fo={assertOptions:r1,validators:bi},Lt=Fo.validators;let bn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new kd,response:new kd}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let l={};Error.captureStackTrace?Error.captureStackTrace(l):l=new Error;const o=l.stack?l.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Qn(this.defaults,n);const{transitional:r,paramsSerializer:l,headers:o}=n;r!==void 0&&Fo.assertOptions(r,{silentJSONParsing:Lt.transitional(Lt.boolean),forcedJSONParsing:Lt.transitional(Lt.boolean),clarifyTimeoutError:Lt.transitional(Lt.boolean)},!1),l!=null&&(N.isFunction(l)?n.paramsSerializer={serialize:l}:Fo.assertOptions(l,{encode:Lt.function,serialize:Lt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Fo.assertOptions(n,{baseUrl:Lt.spelling("baseURL"),withXsrfToken:Lt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&N.merge(o.common,o[n.method]);o&&N.forEach(["delete","get","head","post","put","patch","common"],x=>{delete o[x]}),n.headers=tt.concat(i,o);const a=[];let s=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(s=s&&v.synchronous,a.unshift(v.fulfilled,v.rejected))});const u=[];this.interceptors.response.forEach(function(v){u.push(v.fulfilled,v.rejected)});let d,f=0,p;if(!s){const x=[Ld.bind(this),void 0];for(x.unshift(...a),x.push(...u),p=x.length,d=Promise.resolve(n);f<p;)d=d.then(x[f++],x[f++]);return d}p=a.length;let S=n;for(f=0;f<p;){const x=a[f++],v=a[f++];try{S=x(S)}catch(C){v.call(this,C);break}}try{d=Ld.call(this,S)}catch(x){return Promise.reject(x)}for(f=0,p=u.length;f<p;)d=d.then(u[f++],u[f++]);return d}getUri(t){t=Qn(this.defaults,t);const n=Ap(t.baseURL,t.url,t.allowAbsoluteUrls);return Tp(n,t.params,t.paramsSerializer)}};N.forEach(["delete","get","head","options"],function(t){bn.prototype[t]=function(n,r){return this.request(Qn(r||{},{method:t,url:n,data:(r||{}).data}))}});N.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,a){return this.request(Qn(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}bn.prototype[t]=n(),bn.prototype[t+"Form"]=n(!0)});let l1=class $p{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(l=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](l);r._listeners=null}),this.promise.then=l=>{let o;const i=new Promise(a=>{r.subscribe(a),o=a}).then(l);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,a){r.reason||(r.reason=new Fr(o,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new $p(function(l){t=l}),cancel:t}}};function o1(e){return function(n){return e.apply(null,n)}}function i1(e){return N.isObject(e)&&e.isAxiosError===!0}const Cs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Cs).forEach(([e,t])=>{Cs[t]=e});function Bp(e){const t=new bn(e),n=vp(bn.prototype.request,t);return N.extend(n,bn.prototype,t,{allOwnKeys:!0}),N.extend(n,t,null,{allOwnKeys:!0}),n.create=function(l){return Bp(Qn(e,l))},n}const Se=Bp(ql);Se.Axios=bn;Se.CanceledError=Fr;Se.CancelToken=l1;Se.isCancel=Dp;Se.VERSION=Ip;Se.toFormData=zi;Se.AxiosError=K;Se.Cancel=Se.CanceledError;Se.all=function(t){return Promise.all(t)};Se.spread=o1;Se.isAxiosError=i1;Se.mergeConfig=Qn;Se.AxiosHeaders=tt;Se.formToJSON=e=>Op(N.isHTMLForm(e)?new FormData(e):e);Se.getAdapter=bp.getAdapter;Se.HttpStatusCode=Cs;Se.default=Se;const{Axios:P1,AxiosError:T1,CanceledError:L1,isCancel:O1,CancelToken:D1,VERSION:M1,all:A1,Cancel:F1,isAxiosError:z1,spread:U1,toFormData:b1,AxiosHeaders:I1,HttpStatusCode:$1,formToJSON:B1,getAdapter:H1,mergeConfig:V1}=Se,Ut={TOKEN:"token",AUTHORIZATION:"Authorization",USER_INFO:"userInfo"},ht={getToken:()=>localStorage.getItem(Ut.TOKEN),setToken:e=>{localStorage.setItem(Ut.TOKEN,e)},getAuthorization:()=>localStorage.getItem(Ut.AUTHORIZATION),setAuthorization:e=>{localStorage.setItem(Ut.AUTHORIZATION,e)},getUserInfo:()=>{const e=localStorage.getItem(Ut.USER_INFO);return e?JSON.parse(e):null},setUserInfo:e=>{localStorage.setItem(Ut.USER_INFO,JSON.stringify(e))},setAuthData:e=>{e.token&&ht.setToken(e.token),e.authorization&&ht.setAuthorization(e.authorization),e.userInfo&&ht.setUserInfo(e.userInfo)},clearAuth:()=>{localStorage.removeItem(Ut.TOKEN),localStorage.removeItem(Ut.AUTHORIZATION),localStorage.removeItem(Ut.USER_INFO)},isAuthenticated:()=>!!(ht.getToken()||ht.getAuthorization())},Tu=Se.create({timeout:3e5,headers:{"Content-Type":"application/json"}});Tu.interceptors.request.use(e=>{const t=ht.getAuthorization(),n=ht.getToken();return e.headers.skipToken||(t?e.headers.Authorization=t:n&&(e.headers.Authorization=`Bearer ${n}`)),e},e=>Promise.reject(e));Tu.interceptors.response.use(e=>e,e=>{var t,n,r;return((t=e.response)==null?void 0:t.status)===401?(ht.clearAuth(),console.warn("Unauthorized access - auth data cleared")):((n=e.response)==null?void 0:n.status)===403?console.warn("Forbidden access"):(r=e.response)!=null&&r.status&&e.response.status>=500?console.error("Server error:",e.response.status):(e.code==="NETWORK_ERROR"||e.message==="Network Error")&&console.error("Network error - check if backend is running"),Promise.reject(e)});const Ii=async(e,t,n,r)=>{try{return(await Tu({method:e,url:t,data:n,...r})).data}catch(l){throw console.error(`API call failed: ${e} ${t}`,l),l}},In=(e,t)=>Ii("GET",e,void 0,t),hl=(e,t,n)=>Ii("POST",e,t,n),a1=(e,t,n)=>Ii("PUT",e,t,n),Dd=(e,t)=>Ii("DELETE",e,void 0,t);let q="/v1";const Ve={login:`${q}/user/login`,logout:`${q}/user/logout`,register:`${q}/user/register`,setting:`${q}/user/setting`,user_info:`${q}/user/info`,tenant_info:`${q}/user/tenant_info`,set_tenant_info:`${q}/user/set_tenant_info`,login_channels:`${q}/user/login/channels`,login_channel:e=>`${q}/user/login/${e}`,dataset:`${q}/dataset`,dataset_list:`${q}/dataset/list`,dataset_detail:e=>`${q}/dataset/${e}`,dataset_rename:e=>`${q}/dataset/${e}`,dataset_delete:e=>`${q}/dataset/${e}`,dataset_upload:e=>`${q}/dataset/${e}/document`,dataset_document_list:e=>`${q}/dataset/${e}/document/list`,dataset_document_delete:(e,t)=>`${q}/dataset/${e}/document/${t}`,dataset_document_rename:(e,t)=>`${q}/dataset/${e}/document/${t}`,dataset_document_run:(e,t)=>`${q}/dataset/${e}/document/${t}/run`,dataset_document_stop:(e,t)=>`${q}/dataset/${e}/document/${t}/stop`,dataset_document_chunk_list:(e,t)=>`${q}/dataset/${e}/document/${t}/chunk`,dataset_document_chunk_create:(e,t)=>`${q}/dataset/${e}/document/${t}/chunk`,dataset_document_chunk_delete:(e,t,n)=>`${q}/dataset/${e}/document/${t}/chunk/${n}`,dataset_document_chunk_edit:(e,t,n)=>`${q}/dataset/${e}/document/${t}/chunk/${n}`,dataset_retrieval_test:e=>`${q}/dataset/${e}/retrieval`,chat:`${q}/chat`,chat_list:`${q}/chat/list`,chat_detail:e=>`${q}/chat/${e}`,chat_delete:e=>`${q}/chat/${e}`,chat_rename:e=>`${q}/chat/${e}`,chat_conversation_list:e=>`${q}/chat/${e}/conversation`,chat_conversation_create:e=>`${q}/chat/${e}/conversation`,chat_conversation_delete:(e,t)=>`${q}/chat/${e}/conversation/${t}`,chat_conversation_completion:(e,t)=>`${q}/chat/${e}/conversation/${t}/completion`,file_upload:`${q}/file/upload`,file_list:`${q}/file/list`,file_delete:e=>`${q}/file/${e}`,file_rename:e=>`${q}/file/${e}`,system_status:`${q}/system/status`,system_version:`${q}/system/version`},Tr={login:async e=>{const t=await hl(Ve.login,e,{headers:{skipToken:!0}});if(t.code===0){const{data:n}=t,r={email:n.email,nickname:n.nickname,avatar:n.avatar};ht.setAuthData({token:n.access_token,userInfo:r})}return t},register:async e=>hl(Ve.register,e,{headers:{skipToken:!0}}),logout:async()=>{try{await In(Ve.logout)}catch(e){console.warn("Logout API call failed:",e)}finally{ht.clearAuth()}},getUserInfo:async()=>In(Ve.user_info),isAuthenticated:()=>ht.isAuthenticated(),getStoredUserInfo:()=>ht.getUserInfo()},s1=[{name:"Home",href:"/",icon:kv},{name:"Datasets",href:"/datasets",icon:Al},{name:"Chat",href:"/chat",icon:di},{name:"Search",href:"/search",icon:Di},{name:"Agents",href:"/agents",icon:Ml},{name:"Files",href:"/files",icon:yp}];function u1(){const e=Hl(),t=Xn(),[n,r]=R.useState(!1),[l,o]=R.useState(!1),i=Tr.getStoredUserInfo(),a=()=>{r(!n)},s=async()=>{await Tr.logout(),t("/login")};return c.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:c.jsx("div",{className:"container mx-auto px-4",children:c.jsxs("div",{className:"flex items-center justify-between h-16",children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:c.jsx("span",{className:"text-white font-bold text-sm",children:"AQ"})}),c.jsx("span",{className:"text-xl font-bold text-gray-900",children:"AgentQuest"})]}),c.jsx("nav",{className:"hidden md:flex items-center space-x-1",children:s1.map(u=>{const d=u.icon,f=e.pathname===u.href;return c.jsxs(hp,{to:u.href,className:`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${f?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[c.jsx(d,{className:"w-4 h-4"}),u.name]},u.name)})}),c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("button",{onClick:a,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:n?c.jsx(Tv,{className:"w-5 h-5"}):c.jsx(Rv,{className:"w-5 h-5"})}),c.jsxs("div",{className:"relative",children:[c.jsxs("button",{onClick:()=>o(!l),className:"flex items-center gap-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",children:[c.jsx("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:c.jsx(fi,{className:"w-4 h-4 text-primary-600"})}),c.jsx("span",{className:"text-sm font-medium hidden sm:block",children:(i==null?void 0:i.nickname)||(i==null?void 0:i.email)||"User"}),c.jsx(wv,{className:"w-4 h-4"})]}),l&&c.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:[c.jsxs("div",{className:"px-4 py-2 border-b border-gray-100",children:[c.jsx("p",{className:"text-sm font-medium text-gray-900",children:(i==null?void 0:i.nickname)||"User"}),c.jsx("p",{className:"text-xs text-gray-500",children:i==null?void 0:i.email})]}),c.jsxs("button",{onClick:s,className:"w-full flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:[c.jsx(jv,{className:"w-4 h-4"}),"Sign Out"]})]})]})]})]})})})}function c1(){const e=Xn();return R.useEffect(()=>{Tr.isAuthenticated()||e("/login")},[e]),Tr.isAuthenticated()?c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[c.jsx(u1,{}),c.jsx("main",{className:"container mx-auto px-4 py-8",children:c.jsx(q0,{})})]}):null}function d1(){const e=Xn();return c.jsxs("section",{className:"relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-blue-800 rounded-2xl p-8 text-white",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"}),c.jsx("div",{className:"absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"}),c.jsx("div",{className:"absolute bottom-4 left-4 w-16 h-16 bg-purple-300/20 rounded-full blur-lg"}),c.jsxs("div",{className:"relative z-10",children:[c.jsxs("div",{className:"flex items-center gap-2 mb-6",children:[c.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:c.jsx(Pv,{className:"w-5 h-5"})}),c.jsx("span",{className:"text-white/90 text-sm font-medium",children:"AI-Powered Knowledge Platform"})]}),c.jsxs("h1",{className:"text-5xl font-bold mb-6 leading-tight",children:["Welcome to"," ",c.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"AgentQuest"})]}),c.jsx("p",{className:"text-xl text-white/90 mb-8 max-w-2xl",children:"Transform your documents into intelligent conversations. Build powerful RAG applications with cutting-edge AI technology."}),c.jsxs("div",{className:"flex flex-wrap gap-4",children:[c.jsxs("button",{onClick:()=>e("/datasets"),className:"flex items-center gap-2 px-6 py-3 bg-white text-primary-600 font-semibold rounded-xl hover:bg-white/90 transition-colors shadow-lg",children:["Get Started",c.jsx(ci,{className:"w-4 h-4"})]}),c.jsx("button",{onClick:()=>window.open("https://github.com/your-repo/agentquest","_blank"),className:"px-6 py-3 bg-white/20 text-white font-semibold rounded-xl hover:bg-white/30 transition-colors backdrop-blur-sm border border-white/20",children:"Learn More"})]})]})]})}const f1=[{id:1,name:"Product Documentation",documents:45,status:"active"},{id:2,name:"Customer Support",documents:128,status:"active"},{id:3,name:"Technical Manuals",documents:67,status:"processing"}];function h1(){const e=Xn();return c.jsxs("section",{children:[c.jsxs("div",{className:"flex items-center justify-between mb-8",children:[c.jsxs("div",{className:"flex items-center gap-4",children:[c.jsx("div",{className:"p-3 bg-orange-100 rounded-xl",children:c.jsx(Al,{className:"w-6 h-6 text-orange-600"})}),c.jsxs("div",{children:[c.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Datasets"}),c.jsx("p",{className:"text-gray-600",children:"Manage your knowledge repositories"})]})]}),c.jsxs("button",{onClick:()=>e("/datasets"),className:"flex items-center gap-2 px-4 py-2 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors",children:[c.jsx(Vl,{className:"w-4 h-4"}),"Create Dataset"]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[f1.map(t=>c.jsxs("div",{onClick:()=>e("/datasets"),className:"card hover:shadow-md transition-shadow cursor-pointer group",children:[c.jsxs("div",{className:"flex items-start justify-between mb-4",children:[c.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:c.jsx(Al,{className:"w-5 h-5 text-orange-600"})}),c.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${t.status==="active"?"bg-green-100 text-green-700":"bg-yellow-100 text-yellow-700"}`,children:t.status})]}),c.jsx("h3",{className:"font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:t.name}),c.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:[t.documents," documents"]}),c.jsxs("div",{className:"flex items-center text-sm text-primary-600 font-medium",children:["View details",c.jsx(ci,{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"})]})]},t.id)),c.jsx(hp,{to:"/datasets",className:"card border-dashed border-2 border-gray-300 hover:border-primary-400 hover:bg-primary-50 transition-colors flex items-center justify-center min-h-[200px] group",children:c.jsxs("div",{className:"text-center",children:[c.jsx(ci,{className:"w-8 h-8 text-gray-400 group-hover:text-primary-600 mx-auto mb-2 transition-colors"}),c.jsx("span",{className:"text-gray-600 group-hover:text-primary-600 font-medium transition-colors",children:"View All Datasets"})]})})]})]})}const Md=[{id:"chat",name:"Chat",icon:di,href:"/chat"},{id:"search",name:"Search",icon:Di,href:"/search"},{id:"agents",name:"Agents",icon:Ml,href:"/agents"}],p1={chat:[{id:1,name:"Customer Support Bot",messages:1250,status:"active"},{id:2,name:"Product Q&A",messages:890,status:"active"}],search:[{id:1,name:"Document Search",queries:2340,status:"active"},{id:2,name:"Knowledge Base Search",queries:1560,status:"active"}],agents:[{id:1,name:"Sales Assistant",interactions:450,status:"active"},{id:2,name:"Technical Support",interactions:320,status:"draft"}]};function m1(){const[e,t]=R.useState("chat"),n=Xn(),r=Md.find(o=>o.id===e),l=p1[e];return c.jsxs("section",{children:[c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 mb-8",children:[c.jsxs("div",{className:"flex items-center gap-4",children:[c.jsx("div",{className:"p-3 bg-blue-100 rounded-xl",children:c.jsx(r.icon,{className:"w-6 h-6 text-blue-600"})}),c.jsxs("div",{children:[c.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:r.name}),c.jsx("p",{className:"text-gray-600",children:"Build and manage your AI applications"})]})]}),c.jsxs("div",{className:"flex items-center gap-4",children:[c.jsx("div",{className:"flex bg-gray-100 rounded-lg p-1",children:Md.map(o=>{const i=o.icon;return c.jsxs("button",{onClick:()=>t(o.id),className:`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${e===o.id?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[c.jsx(i,{className:"w-4 h-4"}),o.name]},o.id)})}),c.jsx("button",{onClick:()=>n(r.href),className:"px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"View All"})]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[l.map(o=>c.jsxs("div",{onClick:()=>n(r.href),className:"card hover:shadow-md transition-shadow cursor-pointer group",children:[c.jsxs("div",{className:"flex items-start justify-between mb-4",children:[c.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:c.jsx(r.icon,{className:"w-5 h-5 text-blue-600"})}),c.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${o.status==="active"?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:o.status})]}),c.jsx("h3",{className:"font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:o.name}),c.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:["messages"in o&&`${o.messages} messages`,"queries"in o&&`${o.queries} queries`,"interactions"in o&&`${o.interactions} interactions`]}),c.jsxs("div",{className:"flex items-center text-sm text-primary-600 font-medium",children:["Open application",c.jsx(ci,{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform"})]})]},o.id)),c.jsx("div",{onClick:()=>n(r.href),className:"card border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-colors flex items-center justify-center min-h-[200px] group cursor-pointer",children:c.jsxs("div",{className:"text-center",children:[c.jsx(r.icon,{className:"w-8 h-8 text-gray-400 group-hover:text-blue-600 mx-auto mb-2 transition-colors"}),c.jsxs("span",{className:"text-gray-600 group-hover:text-blue-600 font-medium transition-colors",children:["Create New ",r.name]})]})})]})]})}function y1(){return c.jsxs("div",{className:"space-y-12",children:[c.jsx(d1,{}),c.jsx(h1,{}),c.jsx(m1,{})]})}const ka={list:async()=>In(Ve.dataset_list),create:async e=>hl(Ve.dataset,e),get:async e=>In(Ve.dataset_detail(e)),update:async(e,t)=>a1(Ve.dataset_rename(e),t),delete:async e=>Dd(Ve.dataset_delete(e)),uploadDocument:async(e,t)=>{const n=new FormData;return n.append("file",t),hl(Ve.dataset_upload(e),n,{headers:{"Content-Type":"multipart/form-data"}})},getDocuments:async e=>In(Ve.dataset_document_list(e)),deleteDocument:async(e,t)=>Dd(Ve.dataset_document_delete(e,t)),testRetrieval:async(e,t)=>hl(Ve.dataset_retrieval_test(e),{query:t})};function g1(){const[e,t]=R.useState([]),[n,r]=R.useState(!0),[l,o]=R.useState(null),[i,a]=R.useState(""),[s,u]=R.useState(!1),[d,f]=R.useState(!1),[p,S]=R.useState(""),[x,v]=R.useState(""),[C,m]=R.useState(!1);R.useEffect(()=>{h()},[]);const h=async()=>{var T,w;try{r(!0),o(null);const _=await ka.list();t(_.data||[])}catch(_){console.error("Failed to load datasets:",_),o(((w=(T=_.response)==null?void 0:T.data)==null?void 0:w.message)||"Failed to load datasets. Please check if the backend is running.")}finally{r(!1)}},y=e.filter(T=>T.name.toLowerCase().includes(i.toLowerCase())),j=async()=>{var T,w;if(p.trim())try{m(!0);const _=await ka.create({name:p.trim(),description:x.trim()||void 0});t([...e,_.data]),S(""),v(""),u(!1)}catch(_){console.error("Failed to create dataset:",_),alert("Failed to create dataset: "+(((w=(T=_.response)==null?void 0:T.data)==null?void 0:w.message)||_.message))}finally{m(!1)}},P=async T=>{var w,_;if(confirm("Are you sure you want to delete this dataset?"))try{await ka.delete(T),t(e.filter($=>$.id!==T))}catch($){console.error("Failed to delete dataset:",$),alert("Failed to delete dataset: "+(((_=(w=$.response)==null?void 0:w.data)==null?void 0:_.message)||$.message))}};return n?c.jsxs("div",{className:"space-y-6",children:[c.jsx("div",{className:"flex items-center justify-between",children:c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Datasets"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your knowledge repositories"})]})}),c.jsx("div",{className:"flex items-center justify-center py-12",children:c.jsxs("div",{className:"text-center",children:[c.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),c.jsx("p",{className:"text-gray-600",children:"Loading datasets..."})]})})]}):l?c.jsxs("div",{className:"space-y-6",children:[c.jsx("div",{className:"flex items-center justify-between",children:c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Datasets"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your knowledge repositories"})]})}),c.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx(mp,{className:"w-5 h-5 text-red-500"}),c.jsxs("div",{children:[c.jsx("h3",{className:"font-medium text-red-800",children:"Connection Error"}),c.jsx("p",{className:"text-red-700 mt-1",children:l}),c.jsx("button",{onClick:h,className:"mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Retry"})]})]})})]}):c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Datasets"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your knowledge repositories"})]}),c.jsxs("button",{onClick:()=>u(!0),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[c.jsx(Vl,{className:"w-4 h-4"}),"Create Dataset"]})]}),c.jsxs("div",{className:"flex items-center gap-4",children:[c.jsxs("div",{className:"relative flex-1 max-w-md",children:[c.jsx(Di,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),c.jsx("input",{type:"text",placeholder:"Search datasets...",value:i,onChange:T=>a(T.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),c.jsxs("button",{onClick:()=>f(!d),className:`flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors ${d?"bg-primary-50 border-primary-300":"border-gray-300 hover:bg-gray-50"}`,children:[c.jsx(Ev,{className:"w-4 h-4"}),"Filter"]})]}),d&&c.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[c.jsx("h4",{className:"font-medium mb-3",children:"Filter Options"}),c.jsxs("div",{className:"flex gap-4",children:[c.jsxs("label",{className:"flex items-center",children:[c.jsx("input",{type:"checkbox",className:"mr-2"}),"Active datasets"]}),c.jsxs("label",{className:"flex items-center",children:[c.jsx("input",{type:"checkbox",className:"mr-2"}),"Processing datasets"]})]})]}),s&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:c.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Create New Dataset"}),c.jsx("button",{onClick:()=>u(!1),children:c.jsx(hi,{className:"w-5 h-5"})})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Dataset Name"}),c.jsx("input",{type:"text",value:p,onChange:T=>S(T.target.value),placeholder:"Enter dataset name...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",disabled:C})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Description (Optional)"}),c.jsx("textarea",{value:x,onChange:T=>v(T.target.value),placeholder:"Enter dataset description...",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",disabled:C})]}),c.jsxs("div",{className:"flex gap-3",children:[c.jsx("button",{onClick:j,disabled:C||!p.trim(),className:"flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:C?"Creating...":"Create Dataset"}),c.jsx("button",{onClick:()=>{u(!1),S(""),v("")},disabled:C,className:"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50",children:"Cancel"})]})]})]})}),y.length>0?c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(T=>c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[c.jsxs("div",{className:"flex items-start justify-between mb-4",children:[c.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:c.jsx(Al,{className:"w-5 h-5 text-orange-600"})}),c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${T.status==="completed"?"bg-green-100 text-green-700":T.status==="processing"?"bg-yellow-100 text-yellow-700":"bg-red-100 text-red-700"}`,children:T.status}),c.jsx("button",{onClick:()=>P(T.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:c.jsx(hi,{className:"w-4 h-4"})})]})]}),c.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:T.name}),T.description&&c.jsx("p",{className:"text-sm text-gray-600 mb-2",children:T.description}),c.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[T.document_count," documents"]}),c.jsxs("p",{className:"text-xs text-gray-500",children:["Created: ",new Date(T.created_time).toLocaleDateString()]}),c.jsxs("div",{className:"mt-4 flex gap-2",children:[c.jsxs("button",{className:"flex-1 text-sm bg-primary-50 text-primary-600 py-2 rounded-lg hover:bg-primary-100 transition-colors",children:[c.jsx(Sv,{className:"w-4 h-4 inline mr-1"}),"View"]}),c.jsxs("button",{className:"flex-1 text-sm bg-gray-50 text-gray-600 py-2 rounded-lg hover:bg-gray-100 transition-colors",children:[c.jsx(gp,{className:"w-4 h-4 inline mr-1"}),"Upload"]})]})]},T.id))}):c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[c.jsx(Al,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:i?"No datasets found":"No datasets yet"}),c.jsx("p",{className:"text-gray-600 mb-6",children:i?`No datasets match "${i}"`:"Create your first dataset to get started with AgentQuest"}),!i&&c.jsx("button",{onClick:()=>u(!0),className:"btn-primary",children:"Create Your First Dataset"})]})]})}const v1=[{id:1,name:"Customer Support Bot",messages:1250,status:"active",lastUsed:"2 hours ago"},{id:2,name:"Product Q&A",messages:890,status:"active",lastUsed:"1 day ago"},{id:3,name:"Technical Support",messages:456,status:"draft",lastUsed:"3 days ago"}],x1=[{id:1,type:"user",content:"Hello! Can you help me with product information?"},{id:2,type:"bot",content:"Hello! I'd be happy to help you with product information. What specific product are you interested in?"},{id:3,type:"user",content:"I'm looking for information about your pricing plans."},{id:4,type:"bot",content:"We offer several pricing plans to fit different needs. Our basic plan starts at $29/month and includes core features like document processing and basic chat functionality. Would you like me to explain the different tiers?"}];function w1(){const[e,t]=R.useState(v1),[n,r]=R.useState(null),[l,o]=R.useState(!1),[i,a]=R.useState(""),[s,u]=R.useState(x1),[d,f]=R.useState(""),p=()=>{if(i.trim()){const v={id:e.length+1,name:i,messages:0,status:"active",lastUsed:"Just now"};t([...e,v]),a(""),o(!1)}},S=()=>{if(d.trim()){const v={id:s.length+1,type:"user",content:d},C={id:s.length+2,type:"bot",content:"Thank you for your message! This is a demo response. In a real implementation, this would be powered by your AI model and knowledge base."};u([...s,v,C]),f("")}},x=e.find(v=>v.id===n);return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Chat"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage chat applications"})]}),c.jsxs("button",{onClick:()=>o(!0),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[c.jsx(Vl,{className:"w-4 h-4"}),"Create Chat"]})]}),l&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:c.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Create New Chat"}),c.jsx("button",{onClick:()=>o(!1),children:c.jsx(hi,{className:"w-5 h-5"})})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Chat Application Name"}),c.jsx("input",{type:"text",value:i,onChange:v=>a(v.target.value),placeholder:"Enter chat name...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),c.jsxs("div",{className:"flex gap-3",children:[c.jsx("button",{onClick:p,className:"flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors",children:"Create Chat"}),c.jsx("button",{onClick:()=>o(!1),className:"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors",children:"Cancel"})]})]})]})}),n?c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 h-96 flex flex-col",children:[c.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx(Ml,{className:"w-6 h-6 text-blue-600"}),c.jsxs("div",{children:[c.jsx("h3",{className:"font-semibold",children:x==null?void 0:x.name}),c.jsxs("p",{className:"text-sm text-gray-500",children:[x==null?void 0:x.messages," messages"]})]})]}),c.jsx("button",{onClick:()=>r(null),className:"text-gray-400 hover:text-gray-600",children:c.jsx(hi,{className:"w-5 h-5"})})]}),c.jsx("div",{className:"flex-1 p-4 overflow-y-auto space-y-4",children:s.map(v=>c.jsx("div",{className:`flex ${v.type==="user"?"justify-end":"justify-start"}`,children:c.jsxs("div",{className:`flex items-start gap-2 max-w-xs ${v.type==="user"?"flex-row-reverse":""}`,children:[c.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${v.type==="user"?"bg-primary-100":"bg-gray-100"}`,children:v.type==="user"?c.jsx(fi,{className:"w-4 h-4"}):c.jsx(Ml,{className:"w-4 h-4"})}),c.jsx("div",{className:`p-3 rounded-lg ${v.type==="user"?"bg-primary-600 text-white":"bg-gray-100 text-gray-900"}`,children:v.content})]})},v.id))}),c.jsx("div",{className:"p-4 border-t border-gray-200",children:c.jsxs("div",{className:"flex gap-2",children:[c.jsx("input",{type:"text",value:d,onChange:v=>f(v.target.value),onKeyPress:v=>v.key==="Enter"&&S(),placeholder:"Type your message...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),c.jsx("button",{onClick:S,className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:c.jsx(_v,{className:"w-4 h-4"})})]})})]}):e.length>0?c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(v=>c.jsxs("div",{onClick:()=>r(v.id),className:"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer",children:[c.jsxs("div",{className:"flex items-start justify-between mb-4",children:[c.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:c.jsx(di,{className:"w-5 h-5 text-blue-600"})}),c.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${v.status==="active"?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:v.status})]}),c.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:v.name}),c.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[v.messages," messages"]}),c.jsxs("p",{className:"text-xs text-gray-500",children:["Last used: ",v.lastUsed]}),c.jsx("button",{className:"mt-4 w-full text-sm bg-primary-50 text-primary-600 py-2 rounded-lg hover:bg-primary-100 transition-colors",children:"Open Chat"})]},v.id))}):c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[c.jsx(di,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No chat applications yet"}),c.jsx("p",{className:"text-gray-600 mb-6",children:"Create your first chat application to start conversations with your data"}),c.jsx("button",{onClick:()=>o(!0),className:"btn-primary",children:"Create Your First Chat"})]})]})}function S1(){return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Search"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage search applications"})]}),c.jsxs("button",{onClick:()=>alert("Create Search - This will open the search application creation form"),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[c.jsx(Vl,{className:"w-4 h-4"}),"Create Search"]})]}),c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[c.jsx(Di,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No search applications yet"}),c.jsx("p",{className:"text-gray-600 mb-6",children:"Create your first search application to enable intelligent document search"}),c.jsx("button",{onClick:()=>alert("Create Your First Search - This will open the search creation wizard"),className:"btn-primary",children:"Create Your First Search"})]})]})}function E1(){return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Agents"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Create and manage AI agents"})]}),c.jsxs("button",{onClick:()=>alert("Create Agent - This will open the AI agent creation form"),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[c.jsx(Vl,{className:"w-4 h-4"}),"Create Agent"]})]}),c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[c.jsx(Ml,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No agents yet"}),c.jsx("p",{className:"text-gray-600 mb-6",children:"Create your first AI agent to automate tasks and workflows"}),c.jsx("button",{onClick:()=>alert("Create Your First Agent - This will open the agent creation wizard"),className:"btn-primary",children:"Create Your First Agent"})]})]})}function k1(){return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Files"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your documents and files"})]}),c.jsxs("button",{onClick:()=>alert("Upload Files - This will open the file upload dialog"),className:"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors",children:[c.jsx(gp,{className:"w-4 h-4"}),"Upload Files"]})]}),c.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-8 text-center",children:[c.jsx(yp,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No files yet"}),c.jsx("p",{className:"text-gray-600 mb-6",children:"Upload your first files to start building your knowledge base"}),c.jsx("button",{onClick:()=>alert("Upload Your First Files - This will open the file upload wizard"),className:"btn-primary",children:"Upload Your First Files"})]})]})}function N1(){const e=Xn(),[t,n]=R.useState(!0),[r,l]=R.useState(!1),[o,i]=R.useState(null),[a,s]=R.useState({email:"",password:"",nickname:""}),u=f=>{const{name:p,value:S}=f.target;s(x=>({...x,[p]:S})),i(null)},d=async f=>{var p,S;f.preventDefault(),l(!0),i(null);try{if(t){const x=await Tr.login({email:a.email,password:a.password});x.code===0?e("/"):i(x.message||"Login failed")}else{const x=await Tr.register({email:a.email,password:a.password,nickname:a.nickname});x.code===0?(n(!0),s({email:"",password:"",nickname:""}),i(null),alert("Registration successful! Please login.")):i(x.message||"Registration failed")}}catch(x){console.error("Auth error:",x),i(((S=(p=x.response)==null?void 0:p.data)==null?void 0:S.message)||x.message||"An error occurred")}finally{l(!1)}};return c.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4",children:c.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8 w-full max-w-md",children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"mx-auto w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mb-4",children:c.jsx(Cv,{className:"w-8 h-8 text-white"})}),c.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"AgentQuest"}),c.jsx("p",{className:"text-gray-600 mt-2",children:t?"Sign in to your account":"Create your account"})]}),o&&c.jsxs("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3",children:[c.jsx(mp,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),c.jsx("p",{className:"text-red-700 text-sm",children:o})]}),c.jsxs("form",{onSubmit:d,className:"space-y-6",children:[!t&&c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),c.jsxs("div",{className:"relative",children:[c.jsx(fi,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),c.jsx("input",{type:"text",name:"nickname",value:a.nickname,onChange:u,required:!t,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter your full name"})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),c.jsxs("div",{className:"relative",children:[c.jsx(fi,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),c.jsx("input",{type:"email",name:"email",value:a.email,onChange:u,required:!0,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter your email"})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),c.jsxs("div",{className:"relative",children:[c.jsx(Nv,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),c.jsx("input",{type:"password",name:"password",value:a.password,onChange:u,required:!0,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Enter your password"})]})]}),c.jsx("button",{type:"submit",disabled:r,className:"w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium",children:r?"Please wait...":t?"Sign In":"Create Account"})]}),c.jsx("div",{className:"mt-6 text-center",children:c.jsx("button",{onClick:()=>{n(!t),i(null),s({email:"",password:"",nickname:""})},className:"text-primary-600 hover:text-primary-700 font-medium",children:t?"Don't have an account? Sign up":"Already have an account? Sign in"})}),c.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200 text-center",children:c.jsx("p",{className:"text-sm text-gray-500",children:"For testing, you can try creating a new account or use existing credentials."})})]})})}const C1=nv([{path:"/login",element:c.jsx(N1,{})},{path:"/",element:c.jsx(c1,{}),children:[{index:!0,element:c.jsx(y1,{})},{path:"datasets",element:c.jsx(g1,{})},{path:"chat",element:c.jsx(w1,{})},{path:"search",element:c.jsx(S1,{})},{path:"agents",element:c.jsx(E1,{})},{path:"files",element:c.jsx(k1,{})}]}]),Hp={getStatus:async()=>In(Ve.system_status),getVersion:async()=>In(Ve.system_version),testConnection:async()=>{try{return await Hp.getStatus(),!0}catch(e){return console.error("Backend connection test failed:",e),!1}}};function j1(){const[e,t]=R.useState(null);return R.useEffect(()=>{(async()=>{try{const r=await Hp.testConnection();t(r),r?console.log("✅ Backend connection successful"):console.warn("❌ Backend connection failed")}catch(r){console.error("❌ Backend connection test error:",r),t(!1)}})()},[]),R.useEffect(()=>{e!==null&&console.log(`Backend Status: ${e?"Connected":"Disconnected"}`)},[e]),c.jsx(dv,{router:C1})}Na.createRoot(document.getElementById("root")).render(c.jsx(Qd.StrictMode,{children:c.jsx(j1,{})}));
//# sourceMappingURL=index-DR_0F8BP.js.map
