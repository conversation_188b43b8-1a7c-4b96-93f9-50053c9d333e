<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://unpkg.com/jsencrypt@3.3.2/bin/jsencrypt.min.js"></script>
    <script src="https://unpkg.com/js-base64@3.7.5/base64.min.js"></script>
</head>
<body>
    <h1>Test Login & Registration</h1>

    <h2>Register</h2>
    <form id="registerForm">
        <div>
            <label>Email:</label>
            <input type="email" id="regEmail" value="<EMAIL>" required>
        </div>
        <div>
            <label>Nickname:</label>
            <input type="text" id="regNickname" value="Test User" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="regPassword" value="test123" required>
        </div>
        <button type="submit">Register</button>
    </form>

    <h2>Login</h2>
    <form id="loginForm">
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="root" required>
        </div>
        <button type="submit">Login</button>
    </form>

    <div id="result"></div>

    <script>
        // RSA encryption function (same as in the app)
        function rsaPsw(password) {
            const pub = '-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOOUEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVKRNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs2wIDAQAB-----END PUBLIC KEY-----';
            
            try {
                console.log('🔐 Encrypting password:', password);
                const encryptor = new JSEncrypt();
                encryptor.setPublicKey(pub);
                
                const base64Password = Base64.encode(password);
                console.log('📝 Base64 encoded:', base64Password);
                
                const encryptedPassword = encryptor.encrypt(base64Password);
                console.log('🔒 Encrypted password:', encryptedPassword);
                console.log('📏 Encrypted length:', encryptedPassword ? encryptedPassword.length : 'null');
                
                return encryptedPassword;
            } catch (error) {
                console.error('❌ Password encryption failed:', error);
                return false;
            }
        }

        // Registration form handler
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('regEmail').value;
            const nickname = document.getElementById('regNickname').value;
            const password = document.getElementById('regPassword').value;

            console.log('🚀 Starting registration for:', email);

            // Encrypt password
            const encryptedPassword = rsaPsw(password);
            if (!encryptedPassword) {
                document.getElementById('result').innerHTML = '<p style="color: red;">❌ Failed to encrypt password</p>';
                return;
            }

            console.log('✅ Password encrypted successfully');

            try {
                console.log('📡 Sending registration request...');

                const response = await axios.post('/v1/user/register', {
                    email: email,
                    nickname: nickname,
                    password: encryptedPassword
                });

                console.log('📥 Registration response:', response.data);

                if (response.data.code === 0) {
                    document.getElementById('result').innerHTML = '<p style="color: green;">✅ Registration successful! You can now login.</p>';
                } else {
                    document.getElementById('result').innerHTML = `<p style="color: red;">❌ Registration failed: ${response.data.message}</p>`;
                }

            } catch (error) {
                console.error('❌ Registration error:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">❌ Registration error: ${error.response?.data?.message || error.message}</p>`;
            }
        });

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            console.log('🚀 Starting login for:', email);

            // Encrypt password
            const encryptedPassword = rsaPsw(password);
            if (!encryptedPassword) {
                document.getElementById('result').innerHTML = '<p style="color: red;">❌ Failed to encrypt password</p>';
                return;
            }

            console.log('✅ Password encrypted successfully');

            try {
                console.log('📡 Sending login request...');

                const response = await axios.post('/v1/user/login', {
                    email: email,
                    password: encryptedPassword
                });

                console.log('📥 Login response:', response.data);

                if (response.data.code === 0) {
                    document.getElementById('result').innerHTML = '<p style="color: green;">✅ Login successful!</p>';
                    console.log('🔑 Authorization header:', response.headers['authorization']);
                } else {
                    document.getElementById('result').innerHTML = `<p style="color: red;">❌ Login failed: ${response.data.message}</p>`;
                }

            } catch (error) {
                console.error('❌ Login error:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">❌ Login error: ${error.response?.data?.message || error.message}</p>`;
            }
        });
    </script>
</body>
</html>
