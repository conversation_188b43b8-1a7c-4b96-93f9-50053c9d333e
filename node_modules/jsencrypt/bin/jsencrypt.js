/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["JSEncrypt"] = factory();
	else
		root["JSEncrypt"] = factory();
})(this, function() {
return /******/ (function() { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./lib/JSEncrypt.js":
/*!**************************!*\
  !*** ./lib/JSEncrypt.js ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JSEncrypt: function() { return /* binding */ JSEncrypt; }\n/* harmony export */ });\n/* harmony import */ var _lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/jsbn/base64 */ \"./lib/lib/jsbn/base64.js\");\n/* harmony import */ var _JSEncryptRSAKey__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./JSEncryptRSAKey */ \"./lib/JSEncryptRSAKey.js\");\n/* harmony import */ var _lib_jsbn_rsa__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/jsbn/rsa */ \"./lib/lib/jsbn/rsa.js\");\n/* harmony import */ var _lib_jsbn_sha256__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/jsbn/sha256 */ \"./lib/lib/jsbn/sha256.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process/browser */ \"./node_modules/process/browser.js\");\nvar _a;\n\n\n\n\nvar version = typeof process !== \"undefined\" ? (_a = process.env) === null || _a === void 0 ? void 0 : \"3.5.4\" : undefined;\n/**\n *\n * @param {Object} [options = {}] - An object to customize JSEncrypt behaviour\n * possible parameters are:\n * - key                     {JSEncryptRSAKey}  default: null\n * - default_key_size        {number}  default: 1024 the key size in bit\n * - default_public_exponent {string}  default: '010001' the hexadecimal representation of the public exponent\n * - log                     {boolean} default: false whether log warn/error or not\n * @constructor\n */\nvar JSEncrypt = /** @class */ (function () {\n    function JSEncrypt(options) {\n        if (options === void 0) { options = {}; }\n        this.default_key_size = options.default_key_size\n            ? parseInt(options.default_key_size, 10)\n            : 1024;\n        this.default_public_exponent = options.default_public_exponent || \"010001\"; // 65537 default openssl public exponent for rsa key type\n        this.log = options.log || false;\n        // The private and public key.\n        this.key = options.key || null;\n    }\n    /**\n     * Method to set the rsa key parameter (one method is enough to set both the public\n     * and the private key, since the private key contains the public key paramenters)\n     * Log a warning if logs are enabled\n     * @param {Object|string} key the pem encoded string or an object (with or without header/footer)\n     * @public\n     */\n    JSEncrypt.prototype.setKey = function (key) {\n        if (key) {\n            if (this.log && this.key) {\n                console.warn(\"A key was already set, overriding existing.\");\n            }\n            this.key = new _JSEncryptRSAKey__WEBPACK_IMPORTED_MODULE_1__.JSEncryptRSAKey(key);\n        }\n        else if (!this.key && this.log) {\n            console.error(\"A key was not set.\");\n        }\n    };\n    /**\n     * Proxy method for setKey, for api compatibility\n     * @see setKey\n     * @public\n     */\n    JSEncrypt.prototype.setPrivateKey = function (privkey) {\n        // Create the key.\n        this.setKey(privkey);\n    };\n    /**\n     * Proxy method for setKey, for api compatibility\n     * @see setKey\n     * @public\n     */\n    JSEncrypt.prototype.setPublicKey = function (pubkey) {\n        // Sets the public key.\n        this.setKey(pubkey);\n    };\n    /**\n     * Proxy method for RSAKey object's decrypt, decrypt the string using the private\n     * components of the rsa key object. Note that if the object was not set will be created\n     * on the fly (by the getKey method) using the parameters passed in the JSEncrypt constructor\n     * @param {string} str base64 encoded crypted string to decrypt\n     * @return {string} the decrypted string\n     * @public\n     */\n    JSEncrypt.prototype.decrypt = function (str) {\n        // Return the decrypted string.\n        try {\n            return this.getKey().decrypt((0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.b64tohex)(str));\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Proxy method for RSAKey object's encrypt, encrypt the string using the public\n     * components of the rsa key object. Note that if the object was not set will be created\n     * on the fly (by the getKey method) using the parameters passed in the JSEncrypt constructor\n     * @param {string} str the string to encrypt\n     * @return {string} the encrypted string encoded in base64\n     * @public\n     */\n    JSEncrypt.prototype.encrypt = function (str) {\n        // Return the encrypted string.\n        try {\n            return (0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.hex2b64)(this.getKey().encrypt(str));\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Proxy method for RSAKey object's encrypt with padding: RSA_PKCS1_OAEP_PADDING and oaepHash: sha256,\n     * encrypt the string using the public\n     * components of the rsa key object. Note that if the object was not set will be created\n     * on the fly (by the getKey method) using the parameters passed in the JSEncrypt constructor\n     * @param {string} str the string to encrypt\n     * @return {string} the encrypted string encoded in base64\n     * @public\n     */\n    JSEncrypt.prototype.encryptOAEP = function (str) {\n        // Return the encrypted string.\n        try {\n            return (0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.hex2b64)(this.getKey().encrypt(str, _lib_jsbn_rsa__WEBPACK_IMPORTED_MODULE_2__.oaep_pad));\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Proxy method for RSAKey object's sign.\n     * @param {string} str the string to sign\n     * @param {function} digestMethod hash method\n     * @param {string} digestName the name of the hash algorithm\n     * @return {string} the signature encoded in base64\n     * @public\n     */\n    JSEncrypt.prototype.sign = function (str, digestMethod, digestName) {\n        if (digestMethod === void 0) { digestMethod = function (raw) { return raw; }; }\n        if (digestName === void 0) { digestName = \"\"; }\n        // return the RSA signature of 'str' in 'hex' format.\n        try {\n            return (0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.hex2b64)(this.getKey().sign(str, digestMethod, digestName));\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Signs a string using the SHA-256 hash algorithm.\n     * @param str the string to sign\n     * @returns the base64 encoded signature or false on failure\n     */\n    JSEncrypt.prototype.signSha256 = function (str) {\n        return this.sign(str, function (text) {\n            return (0,_lib_jsbn_sha256__WEBPACK_IMPORTED_MODULE_3__.rstr2hex)((0,_lib_jsbn_sha256__WEBPACK_IMPORTED_MODULE_3__.rstr_sha256)(text));\n        }, \"sha256\");\n    };\n    /**\n     * Proxy method for RSAKey object's verify.\n     * @param {string} str the string to verify\n     * @param {string} signature the signature encoded in base64 to compare the string to\n     * @param {function} digestMethod hash method\n     * @return {boolean} whether the data and signature match\n     * @public\n     */\n    JSEncrypt.prototype.verify = function (str, signature, digestMethod) {\n        if (digestMethod === void 0) { digestMethod = function (raw) { return raw; }; }\n        // Return the decrypted 'digest' of the signature.\n        try {\n            return this.getKey().verify(str, (0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.b64tohex)(signature), digestMethod);\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Verifies a string using the SHA-256 hash algorithm.\n     * @param str the string to verify\n     * @param signature the base64 encoded signature to compare against\n     * @returns whether the signature is valid\n     */\n    JSEncrypt.prototype.verifySha256 = function (str, signature) {\n        return this.verify(str, signature, function (text) {\n            return (0,_lib_jsbn_sha256__WEBPACK_IMPORTED_MODULE_3__.rstr2hex)((0,_lib_jsbn_sha256__WEBPACK_IMPORTED_MODULE_3__.rstr_sha256)(text));\n        });\n    };\n    /**\n     * Getter for the current JSEncryptRSAKey object. If it doesn't exists a new object\n     * will be created and returned\n     * @param {callback} [cb] the callback to be called if we want the key to be generated\n     * in an async fashion\n     * @returns {JSEncryptRSAKey} the JSEncryptRSAKey object\n     * @public\n     */\n    JSEncrypt.prototype.getKey = function (cb) {\n        // Only create new if it does not exist.\n        if (!this.key) {\n            // Get a new private key.\n            this.key = new _JSEncryptRSAKey__WEBPACK_IMPORTED_MODULE_1__.JSEncryptRSAKey();\n            if (cb && {}.toString.call(cb) === \"[object Function]\") {\n                this.key.generateAsync(this.default_key_size, this.default_public_exponent, cb);\n                return;\n            }\n            // Generate the key.\n            this.key.generate(this.default_key_size, this.default_public_exponent);\n        }\n        return this.key;\n    };\n    /**\n     * Returns the pem encoded representation of the private key\n     * If the key doesn't exists a new key will be created\n     * @returns {string} pem encoded representation of the private key WITH header and footer\n     * @public\n     */\n    JSEncrypt.prototype.getPrivateKey = function () {\n        // Return the private representation of this key.\n        return this.getKey().getPrivateKey();\n    };\n    /**\n     * Returns the pem encoded representation of the private key\n     * If the key doesn't exists a new key will be created\n     * @returns {string} pem encoded representation of the private key WITHOUT header and footer\n     * @public\n     */\n    JSEncrypt.prototype.getPrivateKeyB64 = function () {\n        // Return the private representation of this key.\n        return this.getKey().getPrivateBaseKeyB64();\n    };\n    /**\n     * Returns the pem encoded representation of the public key\n     * If the key doesn't exists a new key will be created\n     * @returns {string} pem encoded representation of the public key WITH header and footer\n     * @public\n     */\n    JSEncrypt.prototype.getPublicKey = function () {\n        // Return the private representation of this key.\n        return this.getKey().getPublicKey();\n    };\n    /**\n     * Returns the pem encoded representation of the public key\n     * If the key doesn't exists a new key will be created\n     * @returns {string} pem encoded representation of the public key WITHOUT header and footer\n     * @public\n     */\n    JSEncrypt.prototype.getPublicKeyB64 = function () {\n        // Return the private representation of this key.\n        return this.getKey().getPublicBaseKeyB64();\n    };\n    JSEncrypt.version = version;\n    return JSEncrypt;\n}());\n\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/JSEncrypt.js?\n}");

/***/ }),

/***/ "./lib/JSEncryptRSAKey.js":
/*!********************************!*\
  !*** ./lib/JSEncryptRSAKey.js ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JSEncryptRSAKey: function() { return /* binding */ JSEncryptRSAKey; }\n/* harmony export */ });\n/* harmony import */ var _lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/jsbn/base64 */ \"./lib/lib/jsbn/base64.js\");\n/* harmony import */ var _lib_asn1js_hex__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/asn1js/hex */ \"./lib/lib/asn1js/hex.js\");\n/* harmony import */ var _lib_asn1js_base64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/asn1js/base64 */ \"./lib/lib/asn1js/base64.js\");\n/* harmony import */ var _lib_asn1js_asn1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/asn1js/asn1 */ \"./lib/lib/asn1js/asn1.js\");\n/* harmony import */ var _lib_jsbn_rsa__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/jsbn/rsa */ \"./lib/lib/jsbn/rsa.js\");\n/* harmony import */ var _lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/jsbn/jsbn */ \"./lib/lib/jsbn/jsbn.js\");\n/* harmony import */ var _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/jsrsasign/asn1-1.0 */ \"./lib/lib/jsrsasign/asn1-1.0.js\");\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\n\n\n\n\n/**\n * Create a new JSEncryptRSAKey that extends Tom Wu's RSA key object.\n * This object is just a decorator for parsing the key parameter\n * @param {string|Object} key - The key in string format, or an object containing\n * the parameters needed to build a RSAKey object.\n * @constructor\n */\nvar JSEncryptRSAKey = /** @class */ (function (_super) {\n    __extends(JSEncryptRSAKey, _super);\n    function JSEncryptRSAKey(key) {\n        var _this = _super.call(this) || this;\n        // Call the super constructor.\n        //  RSAKey.call(this);\n        // If a key key was provided.\n        if (key) {\n            // If this is a string...\n            if (typeof key === \"string\") {\n                _this.parseKey(key);\n            }\n            else if (JSEncryptRSAKey.hasPrivateKeyProperty(key) ||\n                JSEncryptRSAKey.hasPublicKeyProperty(key)) {\n                // Set the values for the key.\n                _this.parsePropertiesFrom(key);\n            }\n        }\n        return _this;\n    }\n    /**\n     * Method to parse a pem encoded string containing both a public or private key.\n     * The method will translate the pem encoded string in a der encoded string and\n     * will parse private key and public key parameters. This method accepts public key\n     * in the rsaencryption pkcs #1 format (oid: 1.2.840.113549.1.1.1).\n     *\n     * @todo Check how many rsa formats use the same format of pkcs #1.\n     *\n     * The format is defined as:\n     * PublicKeyInfo ::= SEQUENCE {\n     *   algorithm       AlgorithmIdentifier,\n     *   PublicKey       BIT STRING\n     * }\n     * Where AlgorithmIdentifier is:\n     * AlgorithmIdentifier ::= SEQUENCE {\n     *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm\n     *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)\n     * }\n     * and PublicKey is a SEQUENCE encapsulated in a BIT STRING\n     * RSAPublicKey ::= SEQUENCE {\n     *   modulus           INTEGER,  -- n\n     *   publicExponent    INTEGER   -- e\n     * }\n     * it's possible to examine the structure of the keys obtained from openssl using\n     * an asn.1 dumper as the one used here to parse the components: http://lapo.it/asn1js/\n     * @argument {string} pem the pem encoded string, can include the BEGIN/END header/footer\n     * @private\n     */\n    JSEncryptRSAKey.prototype.parseKey = function (pem) {\n        try {\n            var modulus = 0;\n            var public_exponent = 0;\n            var reHex = /^\\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\\s*)+$/;\n            var der = reHex.test(pem) ? _lib_asn1js_hex__WEBPACK_IMPORTED_MODULE_1__.Hex.decode(pem) : _lib_asn1js_base64__WEBPACK_IMPORTED_MODULE_2__.Base64.unarmor(pem);\n            var asn1 = _lib_asn1js_asn1__WEBPACK_IMPORTED_MODULE_3__.ASN1.decode(der);\n            // Fixes a bug with OpenSSL 1.0+ private keys\n            if (asn1.sub.length === 3) {\n                asn1 = asn1.sub[2].sub[0];\n            }\n            if (asn1.sub.length === 9) {\n                // Parse the private key.\n                modulus = asn1.sub[1].getHexStringValue(); // bigint\n                this.n = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(modulus, 16);\n                public_exponent = asn1.sub[2].getHexStringValue(); // int\n                this.e = parseInt(public_exponent, 16);\n                var private_exponent = asn1.sub[3].getHexStringValue(); // bigint\n                this.d = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(private_exponent, 16);\n                var prime1 = asn1.sub[4].getHexStringValue(); // bigint\n                this.p = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(prime1, 16);\n                var prime2 = asn1.sub[5].getHexStringValue(); // bigint\n                this.q = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(prime2, 16);\n                var exponent1 = asn1.sub[6].getHexStringValue(); // bigint\n                this.dmp1 = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(exponent1, 16);\n                var exponent2 = asn1.sub[7].getHexStringValue(); // bigint\n                this.dmq1 = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(exponent2, 16);\n                var coefficient = asn1.sub[8].getHexStringValue(); // bigint\n                this.coeff = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(coefficient, 16);\n            }\n            else if (asn1.sub.length === 2) {\n                if (asn1.sub[0].sub) {\n                    // Parse ASN.1 SubjectPublicKeyInfo type as defined by X.509\n                    var bit_string = asn1.sub[1];\n                    var sequence = bit_string.sub[0];\n                    modulus = sequence.sub[0].getHexStringValue();\n                    this.n = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(modulus, 16);\n                    public_exponent = sequence.sub[1].getHexStringValue();\n                    this.e = parseInt(public_exponent, 16);\n                }\n                else {\n                    // Parse ASN.1 RSAPublicKey type as defined by PKCS #1\n                    modulus = asn1.sub[0].getHexStringValue();\n                    this.n = (0,_lib_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_5__.parseBigInt)(modulus, 16);\n                    public_exponent = asn1.sub[1].getHexStringValue();\n                    this.e = parseInt(public_exponent, 16);\n                }\n            }\n            else {\n                return false;\n            }\n            return true;\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    /**\n     * Translate rsa parameters in a hex encoded string representing the rsa key.\n     *\n     * The translation follow the ASN.1 notation :\n     * RSAPrivateKey ::= SEQUENCE {\n     *   version           Version,\n     *   modulus           INTEGER,  -- n\n     *   publicExponent    INTEGER,  -- e\n     *   privateExponent   INTEGER,  -- d\n     *   prime1            INTEGER,  -- p\n     *   prime2            INTEGER,  -- q\n     *   exponent1         INTEGER,  -- d mod (p1)\n     *   exponent2         INTEGER,  -- d mod (q-1)\n     *   coefficient       INTEGER,  -- (inverse of q) mod p\n     * }\n     * @returns {string}  DER Encoded String representing the rsa private key\n     * @private\n     */\n    JSEncryptRSAKey.prototype.getPrivateBaseKey = function () {\n        var options = {\n            array: [\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ int: 0 }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.n }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ int: this.e }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.d }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.p }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.q }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.dmp1 }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.dmq1 }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.coeff }),\n            ],\n        };\n        var seq = new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERSequence(options);\n        return seq.getEncodedHex();\n    };\n    /**\n     * base64 (pem) encoded version of the DER encoded representation\n     * @returns {string} pem encoded representation without header and footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPrivateBaseKeyB64 = function () {\n        return (0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.hex2b64)(this.getPrivateBaseKey());\n    };\n    /**\n     * Translate rsa parameters in a hex encoded string representing the rsa public key.\n     * The representation follow the ASN.1 notation :\n     * PublicKeyInfo ::= SEQUENCE {\n     *   algorithm       AlgorithmIdentifier,\n     *   PublicKey       BIT STRING\n     * }\n     * Where AlgorithmIdentifier is:\n     * AlgorithmIdentifier ::= SEQUENCE {\n     *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm\n     *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)\n     * }\n     * and PublicKey is a SEQUENCE encapsulated in a BIT STRING\n     * RSAPublicKey ::= SEQUENCE {\n     *   modulus           INTEGER,  -- n\n     *   publicExponent    INTEGER   -- e\n     * }\n     * @returns {string} DER Encoded String representing the rsa public key\n     * @private\n     */\n    JSEncryptRSAKey.prototype.getPublicBaseKey = function () {\n        var first_sequence = new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERSequence({\n            array: [\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERObjectIdentifier({ oid: \"1.2.840.113549.1.1.1\" }), // RSA Encryption pkcs #1 oid\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERNull(),\n            ],\n        });\n        var second_sequence = new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERSequence({\n            array: [\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ bigint: this.n }),\n                new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERInteger({ int: this.e }),\n            ],\n        });\n        var bit_string = new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERBitString({\n            hex: \"00\" + second_sequence.getEncodedHex(),\n        });\n        var seq = new _lib_jsrsasign_asn1_1_0__WEBPACK_IMPORTED_MODULE_6__.KJUR.asn1.DERSequence({\n            array: [first_sequence, bit_string],\n        });\n        return seq.getEncodedHex();\n    };\n    /**\n     * base64 (pem) encoded version of the DER encoded representation\n     * @returns {string} pem encoded representation without header and footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPublicBaseKeyB64 = function () {\n        return (0,_lib_jsbn_base64__WEBPACK_IMPORTED_MODULE_0__.hex2b64)(this.getPublicBaseKey());\n    };\n    /**\n     * wrap the string in block of width chars. The default value for rsa keys is 64\n     * characters.\n     * @param {string} str the pem encoded string without header and footer\n     * @param {Number} [width=64] - the length the string has to be wrapped at\n     * @returns {string}\n     * @private\n     */\n    JSEncryptRSAKey.wordwrap = function (str, width) {\n        width = width || 64;\n        if (!str) {\n            return str;\n        }\n        var regex = \"(.{1,\" + width + \"})( +|$\\n?)|(.{1,\" + width + \"})\";\n        return str.match(RegExp(regex, \"g\")).join(\"\\n\");\n    };\n    /**\n     * Retrieve the pem encoded private key\n     * @returns {string} the pem encoded private key with header/footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPrivateKey = function () {\n        var key = \"-----BEGIN RSA PRIVATE KEY-----\\n\";\n        key += JSEncryptRSAKey.wordwrap(this.getPrivateBaseKeyB64()) + \"\\n\";\n        key += \"-----END RSA PRIVATE KEY-----\";\n        return key;\n    };\n    /**\n     * Retrieve the pem encoded public key\n     * @returns {string} the pem encoded public key with header/footer\n     * @public\n     */\n    JSEncryptRSAKey.prototype.getPublicKey = function () {\n        var key = \"-----BEGIN PUBLIC KEY-----\\n\";\n        key += JSEncryptRSAKey.wordwrap(this.getPublicBaseKeyB64()) + \"\\n\";\n        key += \"-----END PUBLIC KEY-----\";\n        return key;\n    };\n    /**\n     * Check if the object contains the necessary parameters to populate the rsa modulus\n     * and public exponent parameters.\n     * @param {Object} [obj={}] - An object that may contain the two public key\n     * parameters\n     * @returns {boolean} true if the object contains both the modulus and the public exponent\n     * properties (n and e)\n     * @todo check for types of n and e. N should be a parseable bigInt object, E should\n     * be a parseable integer number\n     * @private\n     */\n    JSEncryptRSAKey.hasPublicKeyProperty = function (obj) {\n        obj = obj || {};\n        return obj.hasOwnProperty(\"n\") && obj.hasOwnProperty(\"e\");\n    };\n    /**\n     * Check if the object contains ALL the parameters of an RSA key.\n     * @param {Object} [obj={}] - An object that may contain nine rsa key\n     * parameters\n     * @returns {boolean} true if the object contains all the parameters needed\n     * @todo check for types of the parameters all the parameters but the public exponent\n     * should be parseable bigint objects, the public exponent should be a parseable integer number\n     * @private\n     */\n    JSEncryptRSAKey.hasPrivateKeyProperty = function (obj) {\n        obj = obj || {};\n        return (obj.hasOwnProperty(\"n\") &&\n            obj.hasOwnProperty(\"e\") &&\n            obj.hasOwnProperty(\"d\") &&\n            obj.hasOwnProperty(\"p\") &&\n            obj.hasOwnProperty(\"q\") &&\n            obj.hasOwnProperty(\"dmp1\") &&\n            obj.hasOwnProperty(\"dmq1\") &&\n            obj.hasOwnProperty(\"coeff\"));\n    };\n    /**\n     * Parse the properties of obj in the current rsa object. Obj should AT LEAST\n     * include the modulus and public exponent (n, e) parameters.\n     * @param {Object} obj - the object containing rsa parameters\n     * @private\n     */\n    JSEncryptRSAKey.prototype.parsePropertiesFrom = function (obj) {\n        this.n = obj.n;\n        this.e = obj.e;\n        if (obj.hasOwnProperty(\"d\")) {\n            this.d = obj.d;\n            this.p = obj.p;\n            this.q = obj.q;\n            this.dmp1 = obj.dmp1;\n            this.dmq1 = obj.dmq1;\n            this.coeff = obj.coeff;\n        }\n    };\n    return JSEncryptRSAKey;\n}(_lib_jsbn_rsa__WEBPACK_IMPORTED_MODULE_4__.RSAKey));\n\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/JSEncryptRSAKey.js?\n}");

/***/ }),

/***/ "./lib/index.js":
/*!**********************!*\
  !*** ./lib/index.js ***!
  \**********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JSEncrypt: function() { return /* reexport safe */ _JSEncrypt__WEBPACK_IMPORTED_MODULE_0__.JSEncrypt; }\n/* harmony export */ });\n/* harmony import */ var _JSEncrypt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./JSEncrypt */ \"./lib/JSEncrypt.js\");\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_JSEncrypt__WEBPACK_IMPORTED_MODULE_0__.JSEncrypt);\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/index.js?\n}");

/***/ }),

/***/ "./lib/lib/asn1js/asn1.js":
/*!********************************!*\
  !*** ./lib/lib/asn1js/asn1.js ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ASN1: function() { return /* binding */ ASN1; },\n/* harmony export */   ASN1Tag: function() { return /* binding */ ASN1Tag; },\n/* harmony export */   Stream: function() { return /* binding */ Stream; }\n/* harmony export */ });\n/* harmony import */ var _int10__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./int10 */ \"./lib/lib/asn1js/int10.js\");\n// ASN.1 JavaScript decoder\n// Copyright (c) 2008-2014 Lapo Luchini <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\n/*global oids */\n\nvar ellipsis = \"\\u2026\";\nvar reTimeS = /^(\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;\nvar reTimeL = /^(\\d\\d\\d\\d)(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])(?:([0-5]\\d)(?:([0-5]\\d)(?:[.,](\\d{1,3}))?)?)?(Z|[-+](?:[0]\\d|1[0-2])([0-5]\\d)?)?$/;\nfunction stringCut(str, len) {\n    if (str.length > len) {\n        str = str.substring(0, len) + ellipsis;\n    }\n    return str;\n}\nvar Stream = /** @class */ (function () {\n    function Stream(enc, pos) {\n        this.hexDigits = \"0123456789ABCDEF\";\n        if (enc instanceof Stream) {\n            this.enc = enc.enc;\n            this.pos = enc.pos;\n        }\n        else {\n            // enc should be an array or a binary string\n            this.enc = enc;\n            this.pos = pos;\n        }\n    }\n    Stream.prototype.get = function (pos) {\n        if (pos === undefined) {\n            pos = this.pos++;\n        }\n        if (pos >= this.enc.length) {\n            throw new Error(\"Requesting byte offset \".concat(pos, \" on a stream of length \").concat(this.enc.length));\n        }\n        return (\"string\" === typeof this.enc) ? this.enc.charCodeAt(pos) : this.enc[pos];\n    };\n    Stream.prototype.hexByte = function (b) {\n        return this.hexDigits.charAt((b >> 4) & 0xF) + this.hexDigits.charAt(b & 0xF);\n    };\n    Stream.prototype.hexDump = function (start, end, raw) {\n        var s = \"\";\n        for (var i = start; i < end; ++i) {\n            s += this.hexByte(this.get(i));\n            if (raw !== true) {\n                switch (i & 0xF) {\n                    case 0x7:\n                        s += \"  \";\n                        break;\n                    case 0xF:\n                        s += \"\\n\";\n                        break;\n                    default:\n                        s += \" \";\n                }\n            }\n        }\n        return s;\n    };\n    Stream.prototype.isASCII = function (start, end) {\n        for (var i = start; i < end; ++i) {\n            var c = this.get(i);\n            if (c < 32 || c > 176) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Stream.prototype.parseStringISO = function (start, end) {\n        var s = \"\";\n        for (var i = start; i < end; ++i) {\n            s += String.fromCharCode(this.get(i));\n        }\n        return s;\n    };\n    Stream.prototype.parseStringUTF = function (start, end) {\n        var s = \"\";\n        for (var i = start; i < end;) {\n            var c = this.get(i++);\n            if (c < 128) {\n                s += String.fromCharCode(c);\n            }\n            else if ((c > 191) && (c < 224)) {\n                s += String.fromCharCode(((c & 0x1F) << 6) | (this.get(i++) & 0x3F));\n            }\n            else {\n                s += String.fromCharCode(((c & 0x0F) << 12) | ((this.get(i++) & 0x3F) << 6) | (this.get(i++) & 0x3F));\n            }\n        }\n        return s;\n    };\n    Stream.prototype.parseStringBMP = function (start, end) {\n        var str = \"\";\n        var hi;\n        var lo;\n        for (var i = start; i < end;) {\n            hi = this.get(i++);\n            lo = this.get(i++);\n            str += String.fromCharCode((hi << 8) | lo);\n        }\n        return str;\n    };\n    Stream.prototype.parseTime = function (start, end, shortYear) {\n        var s = this.parseStringISO(start, end);\n        var m = (shortYear ? reTimeS : reTimeL).exec(s);\n        if (!m) {\n            return \"Unrecognized time: \" + s;\n        }\n        if (shortYear) {\n            // to avoid querying the timer, use the fixed range [1970, 2069]\n            // it will conform with ITU X.400 [-10, +40] sliding window until 2030\n            m[1] = +m[1];\n            m[1] += (+m[1] < 70) ? 2000 : 1900;\n        }\n        s = m[1] + \"-\" + m[2] + \"-\" + m[3] + \" \" + m[4];\n        if (m[5]) {\n            s += \":\" + m[5];\n            if (m[6]) {\n                s += \":\" + m[6];\n                if (m[7]) {\n                    s += \".\" + m[7];\n                }\n            }\n        }\n        if (m[8]) {\n            s += \" UTC\";\n            if (m[8] != \"Z\") {\n                s += m[8];\n                if (m[9]) {\n                    s += \":\" + m[9];\n                }\n            }\n        }\n        return s;\n    };\n    Stream.prototype.parseInteger = function (start, end) {\n        var v = this.get(start);\n        var neg = (v > 127);\n        var pad = neg ? 255 : 0;\n        var len;\n        var s = \"\";\n        // skip unuseful bits (not allowed in DER)\n        while (v == pad && ++start < end) {\n            v = this.get(start);\n        }\n        len = end - start;\n        if (len === 0) {\n            return neg ? -1 : 0;\n        }\n        // show bit length of huge integers\n        if (len > 4) {\n            s = v;\n            len <<= 3;\n            while (((+s ^ pad) & 0x80) == 0) {\n                s = +s << 1;\n                --len;\n            }\n            s = \"(\" + len + \" bit)\\n\";\n        }\n        // decode the integer\n        if (neg) {\n            v = v - 256;\n        }\n        var n = new _int10__WEBPACK_IMPORTED_MODULE_0__.Int10(v);\n        for (var i = start + 1; i < end; ++i) {\n            n.mulAdd(256, this.get(i));\n        }\n        return s + n.toString();\n    };\n    Stream.prototype.parseBitString = function (start, end, maxLength) {\n        var unusedBit = this.get(start);\n        var lenBit = ((end - start - 1) << 3) - unusedBit;\n        var intro = \"(\" + lenBit + \" bit)\\n\";\n        var s = \"\";\n        for (var i = start + 1; i < end; ++i) {\n            var b = this.get(i);\n            var skip = (i == end - 1) ? unusedBit : 0;\n            for (var j = 7; j >= skip; --j) {\n                s += (b >> j) & 1 ? \"1\" : \"0\";\n            }\n            if (s.length > maxLength) {\n                return intro + stringCut(s, maxLength);\n            }\n        }\n        return intro + s;\n    };\n    Stream.prototype.parseOctetString = function (start, end, maxLength) {\n        if (this.isASCII(start, end)) {\n            return stringCut(this.parseStringISO(start, end), maxLength);\n        }\n        var len = end - start;\n        var s = \"(\" + len + \" byte)\\n\";\n        maxLength /= 2; // we work in bytes\n        if (len > maxLength) {\n            end = start + maxLength;\n        }\n        for (var i = start; i < end; ++i) {\n            s += this.hexByte(this.get(i));\n        }\n        if (len > maxLength) {\n            s += ellipsis;\n        }\n        return s;\n    };\n    Stream.prototype.parseOID = function (start, end, maxLength) {\n        var s = \"\";\n        var n = new _int10__WEBPACK_IMPORTED_MODULE_0__.Int10();\n        var bits = 0;\n        for (var i = start; i < end; ++i) {\n            var v = this.get(i);\n            n.mulAdd(128, v & 0x7F);\n            bits += 7;\n            if (!(v & 0x80)) { // finished\n                if (s === \"\") {\n                    n = n.simplify();\n                    if (n instanceof _int10__WEBPACK_IMPORTED_MODULE_0__.Int10) {\n                        n.sub(80);\n                        s = \"2.\" + n.toString();\n                    }\n                    else {\n                        var m = n < 80 ? n < 40 ? 0 : 1 : 2;\n                        s = m + \".\" + (n - m * 40);\n                    }\n                }\n                else {\n                    s += \".\" + n.toString();\n                }\n                if (s.length > maxLength) {\n                    return stringCut(s, maxLength);\n                }\n                n = new _int10__WEBPACK_IMPORTED_MODULE_0__.Int10();\n                bits = 0;\n            }\n        }\n        if (bits > 0) {\n            s += \".incomplete\";\n        }\n        return s;\n    };\n    return Stream;\n}());\n\nvar ASN1 = /** @class */ (function () {\n    function ASN1(stream, header, length, tag, sub) {\n        if (!(tag instanceof ASN1Tag)) {\n            throw new Error(\"Invalid tag value.\");\n        }\n        this.stream = stream;\n        this.header = header;\n        this.length = length;\n        this.tag = tag;\n        this.sub = sub;\n    }\n    ASN1.prototype.typeName = function () {\n        switch (this.tag.tagClass) {\n            case 0: // universal\n                switch (this.tag.tagNumber) {\n                    case 0x00:\n                        return \"EOC\";\n                    case 0x01:\n                        return \"BOOLEAN\";\n                    case 0x02:\n                        return \"INTEGER\";\n                    case 0x03:\n                        return \"BIT_STRING\";\n                    case 0x04:\n                        return \"OCTET_STRING\";\n                    case 0x05:\n                        return \"NULL\";\n                    case 0x06:\n                        return \"OBJECT_IDENTIFIER\";\n                    case 0x07:\n                        return \"ObjectDescriptor\";\n                    case 0x08:\n                        return \"EXTERNAL\";\n                    case 0x09:\n                        return \"REAL\";\n                    case 0x0A:\n                        return \"ENUMERATED\";\n                    case 0x0B:\n                        return \"EMBEDDED_PDV\";\n                    case 0x0C:\n                        return \"UTF8String\";\n                    case 0x10:\n                        return \"SEQUENCE\";\n                    case 0x11:\n                        return \"SET\";\n                    case 0x12:\n                        return \"NumericString\";\n                    case 0x13:\n                        return \"PrintableString\"; // ASCII subset\n                    case 0x14:\n                        return \"TeletexString\"; // aka T61String\n                    case 0x15:\n                        return \"VideotexString\";\n                    case 0x16:\n                        return \"IA5String\"; // ASCII\n                    case 0x17:\n                        return \"UTCTime\";\n                    case 0x18:\n                        return \"GeneralizedTime\";\n                    case 0x19:\n                        return \"GraphicString\";\n                    case 0x1A:\n                        return \"VisibleString\"; // ASCII subset\n                    case 0x1B:\n                        return \"GeneralString\";\n                    case 0x1C:\n                        return \"UniversalString\";\n                    case 0x1E:\n                        return \"BMPString\";\n                }\n                return \"Universal_\" + this.tag.tagNumber.toString();\n            case 1:\n                return \"Application_\" + this.tag.tagNumber.toString();\n            case 2:\n                return \"[\" + this.tag.tagNumber.toString() + \"]\"; // Context\n            case 3:\n                return \"Private_\" + this.tag.tagNumber.toString();\n        }\n    };\n    ASN1.prototype.content = function (maxLength) {\n        if (this.tag === undefined) {\n            return null;\n        }\n        if (maxLength === undefined) {\n            maxLength = Infinity;\n        }\n        var content = this.posContent();\n        var len = Math.abs(this.length);\n        if (!this.tag.isUniversal()) {\n            if (this.sub !== null) {\n                return \"(\" + this.sub.length + \" elem)\";\n            }\n            return this.stream.parseOctetString(content, content + len, maxLength);\n        }\n        switch (this.tag.tagNumber) {\n            case 0x01: // BOOLEAN\n                return (this.stream.get(content) === 0) ? \"false\" : \"true\";\n            case 0x02: // INTEGER\n                return this.stream.parseInteger(content, content + len);\n            case 0x03: // BIT_STRING\n                return this.sub ? \"(\" + this.sub.length + \" elem)\" :\n                    this.stream.parseBitString(content, content + len, maxLength);\n            case 0x04: // OCTET_STRING\n                return this.sub ? \"(\" + this.sub.length + \" elem)\" :\n                    this.stream.parseOctetString(content, content + len, maxLength);\n            // case 0x05: // NULL\n            case 0x06: // OBJECT_IDENTIFIER\n                return this.stream.parseOID(content, content + len, maxLength);\n            // case 0x07: // ObjectDescriptor\n            // case 0x08: // EXTERNAL\n            // case 0x09: // REAL\n            // case 0x0A: // ENUMERATED\n            // case 0x0B: // EMBEDDED_PDV\n            case 0x10: // SEQUENCE\n            case 0x11: // SET\n                if (this.sub !== null) {\n                    return \"(\" + this.sub.length + \" elem)\";\n                }\n                else {\n                    return \"(no elem)\";\n                }\n            case 0x0C: // UTF8String\n                return stringCut(this.stream.parseStringUTF(content, content + len), maxLength);\n            case 0x12: // NumericString\n            case 0x13: // PrintableString\n            case 0x14: // TeletexString\n            case 0x15: // VideotexString\n            case 0x16: // IA5String\n            // case 0x19: // GraphicString\n            case 0x1A: // VisibleString\n                // case 0x1B: // GeneralString\n                // case 0x1C: // UniversalString\n                return stringCut(this.stream.parseStringISO(content, content + len), maxLength);\n            case 0x1E: // BMPString\n                return stringCut(this.stream.parseStringBMP(content, content + len), maxLength);\n            case 0x17: // UTCTime\n            case 0x18: // GeneralizedTime\n                return this.stream.parseTime(content, content + len, (this.tag.tagNumber == 0x17));\n        }\n        return null;\n    };\n    ASN1.prototype.toString = function () {\n        return this.typeName() + \"@\" + this.stream.pos + \"[header:\" + this.header + \",length:\" + this.length + \",sub:\" + ((this.sub === null) ? \"null\" : this.sub.length) + \"]\";\n    };\n    ASN1.prototype.toPrettyString = function (indent) {\n        if (indent === undefined) {\n            indent = \"\";\n        }\n        var s = indent + this.typeName() + \" @\" + this.stream.pos;\n        if (this.length >= 0) {\n            s += \"+\";\n        }\n        s += this.length;\n        if (this.tag.tagConstructed) {\n            s += \" (constructed)\";\n        }\n        else if ((this.tag.isUniversal() && ((this.tag.tagNumber == 0x03) || (this.tag.tagNumber == 0x04))) && (this.sub !== null)) {\n            s += \" (encapsulates)\";\n        }\n        s += \"\\n\";\n        if (this.sub !== null) {\n            indent += \"  \";\n            for (var i = 0, max = this.sub.length; i < max; ++i) {\n                s += this.sub[i].toPrettyString(indent);\n            }\n        }\n        return s;\n    };\n    ASN1.prototype.posStart = function () {\n        return this.stream.pos;\n    };\n    ASN1.prototype.posContent = function () {\n        return this.stream.pos + this.header;\n    };\n    ASN1.prototype.posEnd = function () {\n        return this.stream.pos + this.header + Math.abs(this.length);\n    };\n    ASN1.prototype.toHexString = function () {\n        return this.stream.hexDump(this.posStart(), this.posEnd(), true);\n    };\n    ASN1.decodeLength = function (stream) {\n        var buf = stream.get();\n        var len = buf & 0x7F;\n        if (len == buf) {\n            return len;\n        }\n        // no reason to use Int10, as it would be a huge buffer anyways\n        if (len > 6) {\n            throw new Error(\"Length over 48 bits not supported at position \" + (stream.pos - 1));\n        }\n        if (len === 0) {\n            return null;\n        } // undefined\n        buf = 0;\n        for (var i = 0; i < len; ++i) {\n            buf = (buf * 256) + stream.get();\n        }\n        return buf;\n    };\n    /**\n     * Retrieve the hexadecimal value (as a string) of the current ASN.1 element\n     * @returns {string}\n     * @public\n     */\n    ASN1.prototype.getHexStringValue = function () {\n        var hexString = this.toHexString();\n        var offset = this.header * 2;\n        var length = this.length * 2;\n        return hexString.substring(offset, offset + length);\n    };\n    ASN1.decode = function (str) {\n        var stream;\n        if (!(str instanceof Stream)) {\n            stream = new Stream(str, 0);\n        }\n        else {\n            stream = str;\n        }\n        var streamStart = new Stream(stream);\n        var tag = new ASN1Tag(stream);\n        var len = ASN1.decodeLength(stream);\n        var start = stream.pos;\n        var header = start - streamStart.pos;\n        var sub = null;\n        var getSub = function () {\n            var ret = [];\n            if (len !== null) {\n                // definite length\n                var end = start + len;\n                while (stream.pos < end) {\n                    ret[ret.length] = ASN1.decode(stream);\n                }\n                if (stream.pos != end) {\n                    throw new Error(\"Content size is not correct for container starting at offset \" + start);\n                }\n            }\n            else {\n                // undefined length\n                try {\n                    for (;;) {\n                        var s = ASN1.decode(stream);\n                        if (s.tag.isEOC()) {\n                            break;\n                        }\n                        ret[ret.length] = s;\n                    }\n                    len = start - stream.pos; // undefined lengths are represented as negative values\n                }\n                catch (e) {\n                    throw new Error(\"Exception while decoding undefined length content: \" + e);\n                }\n            }\n            return ret;\n        };\n        if (tag.tagConstructed) {\n            // must have valid content\n            sub = getSub();\n        }\n        else if (tag.isUniversal() && ((tag.tagNumber == 0x03) || (tag.tagNumber == 0x04))) {\n            // sometimes BitString and OctetString are used to encapsulate ASN.1\n            try {\n                if (tag.tagNumber == 0x03) {\n                    if (stream.get() != 0) {\n                        throw new Error(\"BIT STRINGs with unused bits cannot encapsulate.\");\n                    }\n                }\n                sub = getSub();\n                for (var i = 0; i < sub.length; ++i) {\n                    if (sub[i].tag.isEOC()) {\n                        throw new Error(\"EOC is not supposed to be actual content.\");\n                    }\n                }\n            }\n            catch (e) {\n                // but silently ignore when they don't\n                sub = null;\n            }\n        }\n        if (sub === null) {\n            if (len === null) {\n                throw new Error(\"We can't skip over an invalid tag with undefined length at offset \" + start);\n            }\n            stream.pos = start + Math.abs(len);\n        }\n        return new ASN1(streamStart, header, len, tag, sub);\n    };\n    return ASN1;\n}());\n\nvar ASN1Tag = /** @class */ (function () {\n    function ASN1Tag(stream) {\n        var buf = stream.get();\n        this.tagClass = buf >> 6;\n        this.tagConstructed = ((buf & 0x20) !== 0);\n        this.tagNumber = buf & 0x1F;\n        if (this.tagNumber == 0x1F) { // long tag\n            var n = new _int10__WEBPACK_IMPORTED_MODULE_0__.Int10();\n            do {\n                buf = stream.get();\n                n.mulAdd(128, buf & 0x7F);\n            } while (buf & 0x80);\n            this.tagNumber = n.simplify();\n        }\n    }\n    ASN1Tag.prototype.isUniversal = function () {\n        return this.tagClass === 0x00;\n    };\n    ASN1Tag.prototype.isEOC = function () {\n        return this.tagClass === 0x00 && this.tagNumber === 0x00;\n    };\n    return ASN1Tag;\n}());\n\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/asn1js/asn1.js?\n}");

/***/ }),

/***/ "./lib/lib/asn1js/base64.js":
/*!**********************************!*\
  !*** ./lib/lib/asn1js/base64.js ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Base64: function() { return /* binding */ Base64; }\n/* harmony export */ });\n// Base64 JavaScript decoder\n// Copyright (c) 2008-2013 Lapo Luchini <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar decoder;\nvar Base64 = {\n    decode: function (a) {\n        var i;\n        if (decoder === undefined) {\n            var b64 = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n            var ignore = \"= \\f\\n\\r\\t\\u00A0\\u2028\\u2029\";\n            decoder = Object.create(null);\n            for (i = 0; i < 64; ++i) {\n                decoder[b64.charAt(i)] = i;\n            }\n            decoder['-'] = 62; //+\n            decoder['_'] = 63; //-\n            for (i = 0; i < ignore.length; ++i) {\n                decoder[ignore.charAt(i)] = -1;\n            }\n        }\n        var out = [];\n        var bits = 0;\n        var char_count = 0;\n        for (i = 0; i < a.length; ++i) {\n            var c = a.charAt(i);\n            if (c == \"=\") {\n                break;\n            }\n            c = decoder[c];\n            if (c == -1) {\n                continue;\n            }\n            if (c === undefined) {\n                throw new Error(\"Illegal character at offset \" + i);\n            }\n            bits |= c;\n            if (++char_count >= 4) {\n                out[out.length] = (bits >> 16);\n                out[out.length] = (bits >> 8) & 0xFF;\n                out[out.length] = bits & 0xFF;\n                bits = 0;\n                char_count = 0;\n            }\n            else {\n                bits <<= 6;\n            }\n        }\n        switch (char_count) {\n            case 1:\n                throw new Error(\"Base64 encoding incomplete: at least 2 bits missing\");\n            case 2:\n                out[out.length] = (bits >> 10);\n                break;\n            case 3:\n                out[out.length] = (bits >> 16);\n                out[out.length] = (bits >> 8) & 0xFF;\n                break;\n        }\n        return out;\n    },\n    re: /-----BEGIN [^-]+-----([A-Za-z0-9+\\/=\\s]+)-----END [^-]+-----|begin-base64[^\\n]+\\n([A-Za-z0-9+\\/=\\s]+)====/,\n    unarmor: function (a) {\n        var m = Base64.re.exec(a);\n        if (m) {\n            if (m[1]) {\n                a = m[1];\n            }\n            else if (m[2]) {\n                a = m[2];\n            }\n            else {\n                throw new Error(\"RegExp out of sync\");\n            }\n        }\n        return Base64.decode(a);\n    }\n};\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/asn1js/base64.js?\n}");

/***/ }),

/***/ "./lib/lib/asn1js/hex.js":
/*!*******************************!*\
  !*** ./lib/lib/asn1js/hex.js ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hex: function() { return /* binding */ Hex; }\n/* harmony export */ });\n// Hex JavaScript decoder\n// Copyright (c) 2008-2013 Lapo Luchini <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar decoder;\nvar Hex = {\n    decode: function (a) {\n        var i;\n        if (decoder === undefined) {\n            var hex = \"0123456789ABCDEF\";\n            var ignore = \" \\f\\n\\r\\t\\u00A0\\u2028\\u2029\";\n            decoder = {};\n            for (i = 0; i < 16; ++i) {\n                decoder[hex.charAt(i)] = i;\n            }\n            hex = hex.toLowerCase();\n            for (i = 10; i < 16; ++i) {\n                decoder[hex.charAt(i)] = i;\n            }\n            for (i = 0; i < ignore.length; ++i) {\n                decoder[ignore.charAt(i)] = -1;\n            }\n        }\n        var out = [];\n        var bits = 0;\n        var char_count = 0;\n        for (i = 0; i < a.length; ++i) {\n            var c = a.charAt(i);\n            if (c == \"=\") {\n                break;\n            }\n            c = decoder[c];\n            if (c == -1) {\n                continue;\n            }\n            if (c === undefined) {\n                throw new Error(\"Illegal character at offset \" + i);\n            }\n            bits |= c;\n            if (++char_count >= 2) {\n                out[out.length] = bits;\n                bits = 0;\n                char_count = 0;\n            }\n            else {\n                bits <<= 4;\n            }\n        }\n        if (char_count) {\n            throw new Error(\"Hex encoding incomplete: 4 bits missing\");\n        }\n        return out;\n    }\n};\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/asn1js/hex.js?\n}");

/***/ }),

/***/ "./lib/lib/asn1js/int10.js":
/*!*********************************!*\
  !*** ./lib/lib/asn1js/int10.js ***!
  \*********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Int10: function() { return /* binding */ Int10; }\n/* harmony export */ });\n// Big integer base-10 printing library\n// Copyright (c) 2014 Lapo Luchini <<EMAIL>>\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n/*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */\nvar max = 10000000000000; // biggest integer that can still fit 2^53 when multiplied by 256\nvar Int10 = /** @class */ (function () {\n    function Int10(value) {\n        this.buf = [+value || 0];\n    }\n    Int10.prototype.mulAdd = function (m, c) {\n        // assert(m <= 256)\n        var b = this.buf;\n        var l = b.length;\n        var i;\n        var t;\n        for (i = 0; i < l; ++i) {\n            t = b[i] * m + c;\n            if (t < max) {\n                c = 0;\n            }\n            else {\n                c = 0 | (t / max);\n                t -= c * max;\n            }\n            b[i] = t;\n        }\n        if (c > 0) {\n            b[i] = c;\n        }\n    };\n    Int10.prototype.sub = function (c) {\n        // assert(m <= 256)\n        var b = this.buf;\n        var l = b.length;\n        var i;\n        var t;\n        for (i = 0; i < l; ++i) {\n            t = b[i] - c;\n            if (t < 0) {\n                t += max;\n                c = 1;\n            }\n            else {\n                c = 0;\n            }\n            b[i] = t;\n        }\n        while (b[b.length - 1] === 0) {\n            b.pop();\n        }\n    };\n    Int10.prototype.toString = function (base) {\n        if ((base || 10) != 10) {\n            throw new Error(\"only base 10 is supported\");\n        }\n        var b = this.buf;\n        var s = b[b.length - 1].toString();\n        for (var i = b.length - 2; i >= 0; --i) {\n            s += (max + b[i]).toString().substring(1);\n        }\n        return s;\n    };\n    Int10.prototype.valueOf = function () {\n        var b = this.buf;\n        var v = 0;\n        for (var i = b.length - 1; i >= 0; --i) {\n            v = v * max + b[i];\n        }\n        return v;\n    };\n    Int10.prototype.simplify = function () {\n        var b = this.buf;\n        return (b.length == 1) ? b[0] : this;\n    };\n    return Int10;\n}());\n\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/asn1js/int10.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/base64.js":
/*!********************************!*\
  !*** ./lib/lib/jsbn/base64.js ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   b64toBA: function() { return /* binding */ b64toBA; },\n/* harmony export */   b64tohex: function() { return /* binding */ b64tohex; },\n/* harmony export */   hex2b64: function() { return /* binding */ hex2b64; }\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"./lib/lib/jsbn/util.js\");\n\nvar b64map = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nvar b64pad = \"=\";\nfunction hex2b64(h) {\n    var i;\n    var c;\n    var ret = \"\";\n    for (i = 0; i + 3 <= h.length; i += 3) {\n        c = parseInt(h.substring(i, i + 3), 16);\n        ret += b64map.charAt(c >> 6) + b64map.charAt(c & 63);\n    }\n    if (i + 1 == h.length) {\n        c = parseInt(h.substring(i, i + 1), 16);\n        ret += b64map.charAt(c << 2);\n    }\n    else if (i + 2 == h.length) {\n        c = parseInt(h.substring(i, i + 2), 16);\n        ret += b64map.charAt(c >> 2) + b64map.charAt((c & 3) << 4);\n    }\n    while ((ret.length & 3) > 0) {\n        ret += b64pad;\n    }\n    return ret;\n}\n// convert a base64 string to hex\nfunction b64tohex(s) {\n    var ret = \"\";\n    var i;\n    var k = 0; // b64 state, 0-3\n    var slop = 0;\n    for (i = 0; i < s.length; ++i) {\n        if (s.charAt(i) == b64pad) {\n            break;\n        }\n        var v = b64map.indexOf(s.charAt(i));\n        if (v < 0) {\n            continue;\n        }\n        if (k == 0) {\n            ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(v >> 2);\n            slop = v & 3;\n            k = 1;\n        }\n        else if (k == 1) {\n            ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)((slop << 2) | (v >> 4));\n            slop = v & 0xf;\n            k = 2;\n        }\n        else if (k == 2) {\n            ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(slop);\n            ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(v >> 2);\n            slop = v & 3;\n            k = 3;\n        }\n        else {\n            ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)((slop << 2) | (v >> 4));\n            ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(v & 0xf);\n            k = 0;\n        }\n    }\n    if (k == 1) {\n        ret += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(slop << 2);\n    }\n    return ret;\n}\n// convert a base64 string to a byte/number array\nfunction b64toBA(s) {\n    // piggyback on b64tohex for now, optimize later\n    var h = b64tohex(s);\n    var i;\n    var a = [];\n    for (i = 0; 2 * i < h.length; ++i) {\n        a[i] = parseInt(h.substring(2 * i, 2 * i + 2), 16);\n    }\n    return a;\n}\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/base64.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/jsbn.js":
/*!******************************!*\
  !*** ./lib/lib/jsbn/jsbn.js ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BigInteger: function() { return /* binding */ BigInteger; },\n/* harmony export */   intAt: function() { return /* binding */ intAt; },\n/* harmony export */   nbi: function() { return /* binding */ nbi; },\n/* harmony export */   nbits: function() { return /* binding */ nbits; },\n/* harmony export */   nbv: function() { return /* binding */ nbv; },\n/* harmony export */   parseBigInt: function() { return /* binding */ parseBigInt; }\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"./lib/lib/jsbn/util.js\");\n// Copyright (c) 2005  Tom Wu\n// All Rights Reserved.\n// See \"LICENSE\" for details.\n// Basic JavaScript BN library - subset useful for RSA encryption.\n\n// Bits per digit\nvar dbits;\n// JavaScript engine analysis\nvar canary = 0xdeadbeefcafe;\nvar j_lm = ((canary & 0xffffff) == 0xefcafe);\n//#region\nvar lowprimes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997];\nvar lplim = (1 << 26) / lowprimes[lowprimes.length - 1];\n//#endregion\n// (public) Constructor\nvar BigInteger = /** @class */ (function () {\n    function BigInteger(a, b, c) {\n        if (a != null) {\n            if (\"number\" == typeof a) {\n                this.fromNumber(a, b, c);\n            }\n            else if (b == null && \"string\" != typeof a) {\n                this.fromString(a, 256);\n            }\n            else {\n                this.fromString(a, b);\n            }\n        }\n    }\n    //#region PUBLIC\n    // BigInteger.prototype.toString = bnToString;\n    // (public) return string representation in given radix\n    BigInteger.prototype.toString = function (b) {\n        if (this.s < 0) {\n            return \"-\" + this.negate().toString(b);\n        }\n        var k;\n        if (b == 16) {\n            k = 4;\n        }\n        else if (b == 8) {\n            k = 3;\n        }\n        else if (b == 2) {\n            k = 1;\n        }\n        else if (b == 32) {\n            k = 5;\n        }\n        else if (b == 4) {\n            k = 2;\n        }\n        else {\n            return this.toRadix(b);\n        }\n        var km = (1 << k) - 1;\n        var d;\n        var m = false;\n        var r = \"\";\n        var i = this.t;\n        var p = this.DB - (i * this.DB) % k;\n        if (i-- > 0) {\n            if (p < this.DB && (d = this[i] >> p) > 0) {\n                m = true;\n                r = (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(d);\n            }\n            while (i >= 0) {\n                if (p < k) {\n                    d = (this[i] & ((1 << p) - 1)) << (k - p);\n                    d |= this[--i] >> (p += this.DB - k);\n                }\n                else {\n                    d = (this[i] >> (p -= k)) & km;\n                    if (p <= 0) {\n                        p += this.DB;\n                        --i;\n                    }\n                }\n                if (d > 0) {\n                    m = true;\n                }\n                if (m) {\n                    r += (0,_util__WEBPACK_IMPORTED_MODULE_0__.int2char)(d);\n                }\n            }\n        }\n        return m ? r : \"0\";\n    };\n    // BigInteger.prototype.negate = bnNegate;\n    // (public) -this\n    BigInteger.prototype.negate = function () {\n        var r = nbi();\n        BigInteger.ZERO.subTo(this, r);\n        return r;\n    };\n    // BigInteger.prototype.abs = bnAbs;\n    // (public) |this|\n    BigInteger.prototype.abs = function () {\n        return (this.s < 0) ? this.negate() : this;\n    };\n    // BigInteger.prototype.compareTo = bnCompareTo;\n    // (public) return + if this > a, - if this < a, 0 if equal\n    BigInteger.prototype.compareTo = function (a) {\n        var r = this.s - a.s;\n        if (r != 0) {\n            return r;\n        }\n        var i = this.t;\n        r = i - a.t;\n        if (r != 0) {\n            return (this.s < 0) ? -r : r;\n        }\n        while (--i >= 0) {\n            if ((r = this[i] - a[i]) != 0) {\n                return r;\n            }\n        }\n        return 0;\n    };\n    // BigInteger.prototype.bitLength = bnBitLength;\n    // (public) return the number of bits in \"this\"\n    BigInteger.prototype.bitLength = function () {\n        if (this.t <= 0) {\n            return 0;\n        }\n        return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ (this.s & this.DM));\n    };\n    // BigInteger.prototype.mod = bnMod;\n    // (public) this mod a\n    BigInteger.prototype.mod = function (a) {\n        var r = nbi();\n        this.abs().divRemTo(a, null, r);\n        if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {\n            a.subTo(r, r);\n        }\n        return r;\n    };\n    // BigInteger.prototype.modPowInt = bnModPowInt;\n    // (public) this^e % m, 0 <= e < 2^32\n    BigInteger.prototype.modPowInt = function (e, m) {\n        var z;\n        if (e < 256 || m.isEven()) {\n            z = new Classic(m);\n        }\n        else {\n            z = new Montgomery(m);\n        }\n        return this.exp(e, z);\n    };\n    // BigInteger.prototype.clone = bnClone;\n    // (public)\n    BigInteger.prototype.clone = function () {\n        var r = nbi();\n        this.copyTo(r);\n        return r;\n    };\n    // BigInteger.prototype.intValue = bnIntValue;\n    // (public) return value as integer\n    BigInteger.prototype.intValue = function () {\n        if (this.s < 0) {\n            if (this.t == 1) {\n                return this[0] - this.DV;\n            }\n            else if (this.t == 0) {\n                return -1;\n            }\n        }\n        else if (this.t == 1) {\n            return this[0];\n        }\n        else if (this.t == 0) {\n            return 0;\n        }\n        // assumes 16 < DB < 32\n        return ((this[1] & ((1 << (32 - this.DB)) - 1)) << this.DB) | this[0];\n    };\n    // BigInteger.prototype.byteValue = bnByteValue;\n    // (public) return value as byte\n    BigInteger.prototype.byteValue = function () {\n        return (this.t == 0) ? this.s : (this[0] << 24) >> 24;\n    };\n    // BigInteger.prototype.shortValue = bnShortValue;\n    // (public) return value as short (assumes DB>=16)\n    BigInteger.prototype.shortValue = function () {\n        return (this.t == 0) ? this.s : (this[0] << 16) >> 16;\n    };\n    // BigInteger.prototype.signum = bnSigNum;\n    // (public) 0 if this == 0, 1 if this > 0\n    BigInteger.prototype.signum = function () {\n        if (this.s < 0) {\n            return -1;\n        }\n        else if (this.t <= 0 || (this.t == 1 && this[0] <= 0)) {\n            return 0;\n        }\n        else {\n            return 1;\n        }\n    };\n    // BigInteger.prototype.toByteArray = bnToByteArray;\n    // (public) convert to bigendian byte array\n    BigInteger.prototype.toByteArray = function () {\n        var i = this.t;\n        var r = [];\n        r[0] = this.s;\n        var p = this.DB - (i * this.DB) % 8;\n        var d;\n        var k = 0;\n        if (i-- > 0) {\n            if (p < this.DB && (d = this[i] >> p) != (this.s & this.DM) >> p) {\n                r[k++] = d | (this.s << (this.DB - p));\n            }\n            while (i >= 0) {\n                if (p < 8) {\n                    d = (this[i] & ((1 << p) - 1)) << (8 - p);\n                    d |= this[--i] >> (p += this.DB - 8);\n                }\n                else {\n                    d = (this[i] >> (p -= 8)) & 0xff;\n                    if (p <= 0) {\n                        p += this.DB;\n                        --i;\n                    }\n                }\n                if ((d & 0x80) != 0) {\n                    d |= -256;\n                }\n                if (k == 0 && (this.s & 0x80) != (d & 0x80)) {\n                    ++k;\n                }\n                if (k > 0 || d != this.s) {\n                    r[k++] = d;\n                }\n            }\n        }\n        return r;\n    };\n    // BigInteger.prototype.equals = bnEquals;\n    BigInteger.prototype.equals = function (a) {\n        return (this.compareTo(a) == 0);\n    };\n    // BigInteger.prototype.min = bnMin;\n    BigInteger.prototype.min = function (a) {\n        return (this.compareTo(a) < 0) ? this : a;\n    };\n    // BigInteger.prototype.max = bnMax;\n    BigInteger.prototype.max = function (a) {\n        return (this.compareTo(a) > 0) ? this : a;\n    };\n    // BigInteger.prototype.and = bnAnd;\n    BigInteger.prototype.and = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, _util__WEBPACK_IMPORTED_MODULE_0__.op_and, r);\n        return r;\n    };\n    // BigInteger.prototype.or = bnOr;\n    BigInteger.prototype.or = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, _util__WEBPACK_IMPORTED_MODULE_0__.op_or, r);\n        return r;\n    };\n    // BigInteger.prototype.xor = bnXor;\n    BigInteger.prototype.xor = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, _util__WEBPACK_IMPORTED_MODULE_0__.op_xor, r);\n        return r;\n    };\n    // BigInteger.prototype.andNot = bnAndNot;\n    BigInteger.prototype.andNot = function (a) {\n        var r = nbi();\n        this.bitwiseTo(a, _util__WEBPACK_IMPORTED_MODULE_0__.op_andnot, r);\n        return r;\n    };\n    // BigInteger.prototype.not = bnNot;\n    // (public) ~this\n    BigInteger.prototype.not = function () {\n        var r = nbi();\n        for (var i = 0; i < this.t; ++i) {\n            r[i] = this.DM & ~this[i];\n        }\n        r.t = this.t;\n        r.s = ~this.s;\n        return r;\n    };\n    // BigInteger.prototype.shiftLeft = bnShiftLeft;\n    // (public) this << n\n    BigInteger.prototype.shiftLeft = function (n) {\n        var r = nbi();\n        if (n < 0) {\n            this.rShiftTo(-n, r);\n        }\n        else {\n            this.lShiftTo(n, r);\n        }\n        return r;\n    };\n    // BigInteger.prototype.shiftRight = bnShiftRight;\n    // (public) this >> n\n    BigInteger.prototype.shiftRight = function (n) {\n        var r = nbi();\n        if (n < 0) {\n            this.lShiftTo(-n, r);\n        }\n        else {\n            this.rShiftTo(n, r);\n        }\n        return r;\n    };\n    // BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;\n    // (public) returns index of lowest 1-bit (or -1 if none)\n    BigInteger.prototype.getLowestSetBit = function () {\n        for (var i = 0; i < this.t; ++i) {\n            if (this[i] != 0) {\n                return i * this.DB + (0,_util__WEBPACK_IMPORTED_MODULE_0__.lbit)(this[i]);\n            }\n        }\n        if (this.s < 0) {\n            return this.t * this.DB;\n        }\n        return -1;\n    };\n    // BigInteger.prototype.bitCount = bnBitCount;\n    // (public) return number of set bits\n    BigInteger.prototype.bitCount = function () {\n        var r = 0;\n        var x = this.s & this.DM;\n        for (var i = 0; i < this.t; ++i) {\n            r += (0,_util__WEBPACK_IMPORTED_MODULE_0__.cbit)(this[i] ^ x);\n        }\n        return r;\n    };\n    // BigInteger.prototype.testBit = bnTestBit;\n    // (public) true iff nth bit is set\n    BigInteger.prototype.testBit = function (n) {\n        var j = Math.floor(n / this.DB);\n        if (j >= this.t) {\n            return (this.s != 0);\n        }\n        return ((this[j] & (1 << (n % this.DB))) != 0);\n    };\n    // BigInteger.prototype.setBit = bnSetBit;\n    // (public) this | (1<<n)\n    BigInteger.prototype.setBit = function (n) {\n        return this.changeBit(n, _util__WEBPACK_IMPORTED_MODULE_0__.op_or);\n    };\n    // BigInteger.prototype.clearBit = bnClearBit;\n    // (public) this & ~(1<<n)\n    BigInteger.prototype.clearBit = function (n) {\n        return this.changeBit(n, _util__WEBPACK_IMPORTED_MODULE_0__.op_andnot);\n    };\n    // BigInteger.prototype.flipBit = bnFlipBit;\n    // (public) this ^ (1<<n)\n    BigInteger.prototype.flipBit = function (n) {\n        return this.changeBit(n, _util__WEBPACK_IMPORTED_MODULE_0__.op_xor);\n    };\n    // BigInteger.prototype.add = bnAdd;\n    // (public) this + a\n    BigInteger.prototype.add = function (a) {\n        var r = nbi();\n        this.addTo(a, r);\n        return r;\n    };\n    // BigInteger.prototype.subtract = bnSubtract;\n    // (public) this - a\n    BigInteger.prototype.subtract = function (a) {\n        var r = nbi();\n        this.subTo(a, r);\n        return r;\n    };\n    // BigInteger.prototype.multiply = bnMultiply;\n    // (public) this * a\n    BigInteger.prototype.multiply = function (a) {\n        var r = nbi();\n        this.multiplyTo(a, r);\n        return r;\n    };\n    // BigInteger.prototype.divide = bnDivide;\n    // (public) this / a\n    BigInteger.prototype.divide = function (a) {\n        var r = nbi();\n        this.divRemTo(a, r, null);\n        return r;\n    };\n    // BigInteger.prototype.remainder = bnRemainder;\n    // (public) this % a\n    BigInteger.prototype.remainder = function (a) {\n        var r = nbi();\n        this.divRemTo(a, null, r);\n        return r;\n    };\n    // BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;\n    // (public) [this/a,this%a]\n    BigInteger.prototype.divideAndRemainder = function (a) {\n        var q = nbi();\n        var r = nbi();\n        this.divRemTo(a, q, r);\n        return [q, r];\n    };\n    // BigInteger.prototype.modPow = bnModPow;\n    // (public) this^e % m (HAC 14.85)\n    BigInteger.prototype.modPow = function (e, m) {\n        var i = e.bitLength();\n        var k;\n        var r = nbv(1);\n        var z;\n        if (i <= 0) {\n            return r;\n        }\n        else if (i < 18) {\n            k = 1;\n        }\n        else if (i < 48) {\n            k = 3;\n        }\n        else if (i < 144) {\n            k = 4;\n        }\n        else if (i < 768) {\n            k = 5;\n        }\n        else {\n            k = 6;\n        }\n        if (i < 8) {\n            z = new Classic(m);\n        }\n        else if (m.isEven()) {\n            z = new Barrett(m);\n        }\n        else {\n            z = new Montgomery(m);\n        }\n        // precomputation\n        var g = [];\n        var n = 3;\n        var k1 = k - 1;\n        var km = (1 << k) - 1;\n        g[1] = z.convert(this);\n        if (k > 1) {\n            var g2 = nbi();\n            z.sqrTo(g[1], g2);\n            while (n <= km) {\n                g[n] = nbi();\n                z.mulTo(g2, g[n - 2], g[n]);\n                n += 2;\n            }\n        }\n        var j = e.t - 1;\n        var w;\n        var is1 = true;\n        var r2 = nbi();\n        var t;\n        i = nbits(e[j]) - 1;\n        while (j >= 0) {\n            if (i >= k1) {\n                w = (e[j] >> (i - k1)) & km;\n            }\n            else {\n                w = (e[j] & ((1 << (i + 1)) - 1)) << (k1 - i);\n                if (j > 0) {\n                    w |= e[j - 1] >> (this.DB + i - k1);\n                }\n            }\n            n = k;\n            while ((w & 1) == 0) {\n                w >>= 1;\n                --n;\n            }\n            if ((i -= n) < 0) {\n                i += this.DB;\n                --j;\n            }\n            if (is1) { // ret == 1, don't bother squaring or multiplying it\n                g[w].copyTo(r);\n                is1 = false;\n            }\n            else {\n                while (n > 1) {\n                    z.sqrTo(r, r2);\n                    z.sqrTo(r2, r);\n                    n -= 2;\n                }\n                if (n > 0) {\n                    z.sqrTo(r, r2);\n                }\n                else {\n                    t = r;\n                    r = r2;\n                    r2 = t;\n                }\n                z.mulTo(r2, g[w], r);\n            }\n            while (j >= 0 && (e[j] & (1 << i)) == 0) {\n                z.sqrTo(r, r2);\n                t = r;\n                r = r2;\n                r2 = t;\n                if (--i < 0) {\n                    i = this.DB - 1;\n                    --j;\n                }\n            }\n        }\n        return z.revert(r);\n    };\n    // BigInteger.prototype.modInverse = bnModInverse;\n    // (public) 1/this % m (HAC 14.61)\n    BigInteger.prototype.modInverse = function (m) {\n        var ac = m.isEven();\n        if ((this.isEven() && ac) || m.signum() == 0) {\n            return BigInteger.ZERO;\n        }\n        var u = m.clone();\n        var v = this.clone();\n        var a = nbv(1);\n        var b = nbv(0);\n        var c = nbv(0);\n        var d = nbv(1);\n        while (u.signum() != 0) {\n            while (u.isEven()) {\n                u.rShiftTo(1, u);\n                if (ac) {\n                    if (!a.isEven() || !b.isEven()) {\n                        a.addTo(this, a);\n                        b.subTo(m, b);\n                    }\n                    a.rShiftTo(1, a);\n                }\n                else if (!b.isEven()) {\n                    b.subTo(m, b);\n                }\n                b.rShiftTo(1, b);\n            }\n            while (v.isEven()) {\n                v.rShiftTo(1, v);\n                if (ac) {\n                    if (!c.isEven() || !d.isEven()) {\n                        c.addTo(this, c);\n                        d.subTo(m, d);\n                    }\n                    c.rShiftTo(1, c);\n                }\n                else if (!d.isEven()) {\n                    d.subTo(m, d);\n                }\n                d.rShiftTo(1, d);\n            }\n            if (u.compareTo(v) >= 0) {\n                u.subTo(v, u);\n                if (ac) {\n                    a.subTo(c, a);\n                }\n                b.subTo(d, b);\n            }\n            else {\n                v.subTo(u, v);\n                if (ac) {\n                    c.subTo(a, c);\n                }\n                d.subTo(b, d);\n            }\n        }\n        if (v.compareTo(BigInteger.ONE) != 0) {\n            return BigInteger.ZERO;\n        }\n        if (d.compareTo(m) >= 0) {\n            return d.subtract(m);\n        }\n        if (d.signum() < 0) {\n            d.addTo(m, d);\n        }\n        else {\n            return d;\n        }\n        if (d.signum() < 0) {\n            return d.add(m);\n        }\n        else {\n            return d;\n        }\n    };\n    // BigInteger.prototype.pow = bnPow;\n    // (public) this^e\n    BigInteger.prototype.pow = function (e) {\n        return this.exp(e, new NullExp());\n    };\n    // BigInteger.prototype.gcd = bnGCD;\n    // (public) gcd(this,a) (HAC 14.54)\n    BigInteger.prototype.gcd = function (a) {\n        var x = (this.s < 0) ? this.negate() : this.clone();\n        var y = (a.s < 0) ? a.negate() : a.clone();\n        if (x.compareTo(y) < 0) {\n            var t = x;\n            x = y;\n            y = t;\n        }\n        var i = x.getLowestSetBit();\n        var g = y.getLowestSetBit();\n        if (g < 0) {\n            return x;\n        }\n        if (i < g) {\n            g = i;\n        }\n        if (g > 0) {\n            x.rShiftTo(g, x);\n            y.rShiftTo(g, y);\n        }\n        while (x.signum() > 0) {\n            if ((i = x.getLowestSetBit()) > 0) {\n                x.rShiftTo(i, x);\n            }\n            if ((i = y.getLowestSetBit()) > 0) {\n                y.rShiftTo(i, y);\n            }\n            if (x.compareTo(y) >= 0) {\n                x.subTo(y, x);\n                x.rShiftTo(1, x);\n            }\n            else {\n                y.subTo(x, y);\n                y.rShiftTo(1, y);\n            }\n        }\n        if (g > 0) {\n            y.lShiftTo(g, y);\n        }\n        return y;\n    };\n    // BigInteger.prototype.isProbablePrime = bnIsProbablePrime;\n    // (public) test primality with certainty >= 1-.5^t\n    BigInteger.prototype.isProbablePrime = function (t) {\n        var i;\n        var x = this.abs();\n        if (x.t == 1 && x[0] <= lowprimes[lowprimes.length - 1]) {\n            for (i = 0; i < lowprimes.length; ++i) {\n                if (x[0] == lowprimes[i]) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        if (x.isEven()) {\n            return false;\n        }\n        i = 1;\n        while (i < lowprimes.length) {\n            var m = lowprimes[i];\n            var j = i + 1;\n            while (j < lowprimes.length && m < lplim) {\n                m *= lowprimes[j++];\n            }\n            m = x.modInt(m);\n            while (i < j) {\n                if (m % lowprimes[i++] == 0) {\n                    return false;\n                }\n            }\n        }\n        return x.millerRabin(t);\n    };\n    //#endregion PUBLIC\n    //#region PROTECTED\n    // BigInteger.prototype.copyTo = bnpCopyTo;\n    // (protected) copy this to r\n    BigInteger.prototype.copyTo = function (r) {\n        for (var i = this.t - 1; i >= 0; --i) {\n            r[i] = this[i];\n        }\n        r.t = this.t;\n        r.s = this.s;\n    };\n    // BigInteger.prototype.fromInt = bnpFromInt;\n    // (protected) set from integer value x, -DV <= x < DV\n    BigInteger.prototype.fromInt = function (x) {\n        this.t = 1;\n        this.s = (x < 0) ? -1 : 0;\n        if (x > 0) {\n            this[0] = x;\n        }\n        else if (x < -1) {\n            this[0] = x + this.DV;\n        }\n        else {\n            this.t = 0;\n        }\n    };\n    // BigInteger.prototype.fromString = bnpFromString;\n    // (protected) set from string and radix\n    BigInteger.prototype.fromString = function (s, b) {\n        var k;\n        if (b == 16) {\n            k = 4;\n        }\n        else if (b == 8) {\n            k = 3;\n        }\n        else if (b == 256) {\n            k = 8;\n            /* byte array */\n        }\n        else if (b == 2) {\n            k = 1;\n        }\n        else if (b == 32) {\n            k = 5;\n        }\n        else if (b == 4) {\n            k = 2;\n        }\n        else {\n            this.fromRadix(s, b);\n            return;\n        }\n        this.t = 0;\n        this.s = 0;\n        var i = s.length;\n        var mi = false;\n        var sh = 0;\n        while (--i >= 0) {\n            var x = (k == 8) ? (+s[i]) & 0xff : intAt(s, i);\n            if (x < 0) {\n                if (s.charAt(i) == \"-\") {\n                    mi = true;\n                }\n                continue;\n            }\n            mi = false;\n            if (sh == 0) {\n                this[this.t++] = x;\n            }\n            else if (sh + k > this.DB) {\n                this[this.t - 1] |= (x & ((1 << (this.DB - sh)) - 1)) << sh;\n                this[this.t++] = (x >> (this.DB - sh));\n            }\n            else {\n                this[this.t - 1] |= x << sh;\n            }\n            sh += k;\n            if (sh >= this.DB) {\n                sh -= this.DB;\n            }\n        }\n        if (k == 8 && ((+s[0]) & 0x80) != 0) {\n            this.s = -1;\n            if (sh > 0) {\n                this[this.t - 1] |= ((1 << (this.DB - sh)) - 1) << sh;\n            }\n        }\n        this.clamp();\n        if (mi) {\n            BigInteger.ZERO.subTo(this, this);\n        }\n    };\n    // BigInteger.prototype.clamp = bnpClamp;\n    // (protected) clamp off excess high words\n    BigInteger.prototype.clamp = function () {\n        var c = this.s & this.DM;\n        while (this.t > 0 && this[this.t - 1] == c) {\n            --this.t;\n        }\n    };\n    // BigInteger.prototype.dlShiftTo = bnpDLShiftTo;\n    // (protected) r = this << n*DB\n    BigInteger.prototype.dlShiftTo = function (n, r) {\n        var i;\n        for (i = this.t - 1; i >= 0; --i) {\n            r[i + n] = this[i];\n        }\n        for (i = n - 1; i >= 0; --i) {\n            r[i] = 0;\n        }\n        r.t = this.t + n;\n        r.s = this.s;\n    };\n    // BigInteger.prototype.drShiftTo = bnpDRShiftTo;\n    // (protected) r = this >> n*DB\n    BigInteger.prototype.drShiftTo = function (n, r) {\n        for (var i = n; i < this.t; ++i) {\n            r[i - n] = this[i];\n        }\n        r.t = Math.max(this.t - n, 0);\n        r.s = this.s;\n    };\n    // BigInteger.prototype.lShiftTo = bnpLShiftTo;\n    // (protected) r = this << n\n    BigInteger.prototype.lShiftTo = function (n, r) {\n        var bs = n % this.DB;\n        var cbs = this.DB - bs;\n        var bm = (1 << cbs) - 1;\n        var ds = Math.floor(n / this.DB);\n        var c = (this.s << bs) & this.DM;\n        for (var i = this.t - 1; i >= 0; --i) {\n            r[i + ds + 1] = (this[i] >> cbs) | c;\n            c = (this[i] & bm) << bs;\n        }\n        for (var i = ds - 1; i >= 0; --i) {\n            r[i] = 0;\n        }\n        r[ds] = c;\n        r.t = this.t + ds + 1;\n        r.s = this.s;\n        r.clamp();\n    };\n    // BigInteger.prototype.rShiftTo = bnpRShiftTo;\n    // (protected) r = this >> n\n    BigInteger.prototype.rShiftTo = function (n, r) {\n        r.s = this.s;\n        var ds = Math.floor(n / this.DB);\n        if (ds >= this.t) {\n            r.t = 0;\n            return;\n        }\n        var bs = n % this.DB;\n        var cbs = this.DB - bs;\n        var bm = (1 << bs) - 1;\n        r[0] = this[ds] >> bs;\n        for (var i = ds + 1; i < this.t; ++i) {\n            r[i - ds - 1] |= (this[i] & bm) << cbs;\n            r[i - ds] = this[i] >> bs;\n        }\n        if (bs > 0) {\n            r[this.t - ds - 1] |= (this.s & bm) << cbs;\n        }\n        r.t = this.t - ds;\n        r.clamp();\n    };\n    // BigInteger.prototype.subTo = bnpSubTo;\n    // (protected) r = this - a\n    BigInteger.prototype.subTo = function (a, r) {\n        var i = 0;\n        var c = 0;\n        var m = Math.min(a.t, this.t);\n        while (i < m) {\n            c += this[i] - a[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        if (a.t < this.t) {\n            c -= a.s;\n            while (i < this.t) {\n                c += this[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c += this.s;\n        }\n        else {\n            c += this.s;\n            while (i < a.t) {\n                c -= a[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c -= a.s;\n        }\n        r.s = (c < 0) ? -1 : 0;\n        if (c < -1) {\n            r[i++] = this.DV + c;\n        }\n        else if (c > 0) {\n            r[i++] = c;\n        }\n        r.t = i;\n        r.clamp();\n    };\n    // BigInteger.prototype.multiplyTo = bnpMultiplyTo;\n    // (protected) r = this * a, r != this,a (HAC 14.12)\n    // \"this\" should be the larger one if appropriate.\n    BigInteger.prototype.multiplyTo = function (a, r) {\n        var x = this.abs();\n        var y = a.abs();\n        var i = x.t;\n        r.t = i + y.t;\n        while (--i >= 0) {\n            r[i] = 0;\n        }\n        for (i = 0; i < y.t; ++i) {\n            r[i + x.t] = x.am(0, y[i], r, i, 0, x.t);\n        }\n        r.s = 0;\n        r.clamp();\n        if (this.s != a.s) {\n            BigInteger.ZERO.subTo(r, r);\n        }\n    };\n    // BigInteger.prototype.squareTo = bnpSquareTo;\n    // (protected) r = this^2, r != this (HAC 14.16)\n    BigInteger.prototype.squareTo = function (r) {\n        var x = this.abs();\n        var i = r.t = 2 * x.t;\n        while (--i >= 0) {\n            r[i] = 0;\n        }\n        for (i = 0; i < x.t - 1; ++i) {\n            var c = x.am(i, x[i], r, 2 * i, 0, 1);\n            if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >= x.DV) {\n                r[i + x.t] -= x.DV;\n                r[i + x.t + 1] = 1;\n            }\n        }\n        if (r.t > 0) {\n            r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1);\n        }\n        r.s = 0;\n        r.clamp();\n    };\n    // BigInteger.prototype.divRemTo = bnpDivRemTo;\n    // (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n    // r != q, this != m.  q or r may be null.\n    BigInteger.prototype.divRemTo = function (m, q, r) {\n        var pm = m.abs();\n        if (pm.t <= 0) {\n            return;\n        }\n        var pt = this.abs();\n        if (pt.t < pm.t) {\n            if (q != null) {\n                q.fromInt(0);\n            }\n            if (r != null) {\n                this.copyTo(r);\n            }\n            return;\n        }\n        if (r == null) {\n            r = nbi();\n        }\n        var y = nbi();\n        var ts = this.s;\n        var ms = m.s;\n        var nsh = this.DB - nbits(pm[pm.t - 1]); // normalize modulus\n        if (nsh > 0) {\n            pm.lShiftTo(nsh, y);\n            pt.lShiftTo(nsh, r);\n        }\n        else {\n            pm.copyTo(y);\n            pt.copyTo(r);\n        }\n        var ys = y.t;\n        var y0 = y[ys - 1];\n        if (y0 == 0) {\n            return;\n        }\n        var yt = y0 * (1 << this.F1) + ((ys > 1) ? y[ys - 2] >> this.F2 : 0);\n        var d1 = this.FV / yt;\n        var d2 = (1 << this.F1) / yt;\n        var e = 1 << this.F2;\n        var i = r.t;\n        var j = i - ys;\n        var t = (q == null) ? nbi() : q;\n        y.dlShiftTo(j, t);\n        if (r.compareTo(t) >= 0) {\n            r[r.t++] = 1;\n            r.subTo(t, r);\n        }\n        BigInteger.ONE.dlShiftTo(ys, t);\n        t.subTo(y, y); // \"negative\" y so we can replace sub with am later\n        while (y.t < ys) {\n            y[y.t++] = 0;\n        }\n        while (--j >= 0) {\n            // Estimate quotient digit\n            var qd = (r[--i] == y0) ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2);\n            if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) { // Try it out\n                y.dlShiftTo(j, t);\n                r.subTo(t, r);\n                while (r[i] < --qd) {\n                    r.subTo(t, r);\n                }\n            }\n        }\n        if (q != null) {\n            r.drShiftTo(ys, q);\n            if (ts != ms) {\n                BigInteger.ZERO.subTo(q, q);\n            }\n        }\n        r.t = ys;\n        r.clamp();\n        if (nsh > 0) {\n            r.rShiftTo(nsh, r);\n        } // Denormalize remainder\n        if (ts < 0) {\n            BigInteger.ZERO.subTo(r, r);\n        }\n    };\n    // BigInteger.prototype.invDigit = bnpInvDigit;\n    // (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n    // justification:\n    //         xy == 1 (mod m)\n    //         xy =  1+km\n    //   xy(2-xy) = (1+km)(1-km)\n    // x[y(2-xy)] = 1-k^2m^2\n    // x[y(2-xy)] == 1 (mod m^2)\n    // if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n    // should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n    // JS multiply \"overflows\" differently from C/C++, so care is needed here.\n    BigInteger.prototype.invDigit = function () {\n        if (this.t < 1) {\n            return 0;\n        }\n        var x = this[0];\n        if ((x & 1) == 0) {\n            return 0;\n        }\n        var y = x & 3; // y == 1/x mod 2^2\n        y = (y * (2 - (x & 0xf) * y)) & 0xf; // y == 1/x mod 2^4\n        y = (y * (2 - (x & 0xff) * y)) & 0xff; // y == 1/x mod 2^8\n        y = (y * (2 - (((x & 0xffff) * y) & 0xffff))) & 0xffff; // y == 1/x mod 2^16\n        // last step - calculate inverse mod DV directly;\n        // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n        y = (y * (2 - x * y % this.DV)) % this.DV; // y == 1/x mod 2^dbits\n        // we really want the negative inverse, and -DV < y < DV\n        return (y > 0) ? this.DV - y : -y;\n    };\n    // BigInteger.prototype.isEven = bnpIsEven;\n    // (protected) true iff this is even\n    BigInteger.prototype.isEven = function () {\n        return ((this.t > 0) ? (this[0] & 1) : this.s) == 0;\n    };\n    // BigInteger.prototype.exp = bnpExp;\n    // (protected) this^e, e < 2^32, doing sqr and mul with \"r\" (HAC 14.79)\n    BigInteger.prototype.exp = function (e, z) {\n        if (e > 0xffffffff || e < 1) {\n            return BigInteger.ONE;\n        }\n        var r = nbi();\n        var r2 = nbi();\n        var g = z.convert(this);\n        var i = nbits(e) - 1;\n        g.copyTo(r);\n        while (--i >= 0) {\n            z.sqrTo(r, r2);\n            if ((e & (1 << i)) > 0) {\n                z.mulTo(r2, g, r);\n            }\n            else {\n                var t = r;\n                r = r2;\n                r2 = t;\n            }\n        }\n        return z.revert(r);\n    };\n    // BigInteger.prototype.chunkSize = bnpChunkSize;\n    // (protected) return x s.t. r^x < DV\n    BigInteger.prototype.chunkSize = function (r) {\n        return Math.floor(Math.LN2 * this.DB / Math.log(r));\n    };\n    // BigInteger.prototype.toRadix = bnpToRadix;\n    // (protected) convert to radix string\n    BigInteger.prototype.toRadix = function (b) {\n        if (b == null) {\n            b = 10;\n        }\n        if (this.signum() == 0 || b < 2 || b > 36) {\n            return \"0\";\n        }\n        var cs = this.chunkSize(b);\n        var a = Math.pow(b, cs);\n        var d = nbv(a);\n        var y = nbi();\n        var z = nbi();\n        var r = \"\";\n        this.divRemTo(d, y, z);\n        while (y.signum() > 0) {\n            r = (a + z.intValue()).toString(b).substring(1) + r;\n            y.divRemTo(d, y, z);\n        }\n        return z.intValue().toString(b) + r;\n    };\n    // BigInteger.prototype.fromRadix = bnpFromRadix;\n    // (protected) convert from radix string\n    BigInteger.prototype.fromRadix = function (s, b) {\n        this.fromInt(0);\n        if (b == null) {\n            b = 10;\n        }\n        var cs = this.chunkSize(b);\n        var d = Math.pow(b, cs);\n        var mi = false;\n        var j = 0;\n        var w = 0;\n        for (var i = 0; i < s.length; ++i) {\n            var x = intAt(s, i);\n            if (x < 0) {\n                if (s.charAt(i) == \"-\" && this.signum() == 0) {\n                    mi = true;\n                }\n                continue;\n            }\n            w = b * w + x;\n            if (++j >= cs) {\n                this.dMultiply(d);\n                this.dAddOffset(w, 0);\n                j = 0;\n                w = 0;\n            }\n        }\n        if (j > 0) {\n            this.dMultiply(Math.pow(b, j));\n            this.dAddOffset(w, 0);\n        }\n        if (mi) {\n            BigInteger.ZERO.subTo(this, this);\n        }\n    };\n    // BigInteger.prototype.fromNumber = bnpFromNumber;\n    // (protected) alternate constructor\n    BigInteger.prototype.fromNumber = function (a, b, c) {\n        if (\"number\" == typeof b) {\n            // new BigInteger(int,int,RNG)\n            if (a < 2) {\n                this.fromInt(1);\n            }\n            else {\n                this.fromNumber(a, c);\n                if (!this.testBit(a - 1)) {\n                    // force MSB set\n                    this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), _util__WEBPACK_IMPORTED_MODULE_0__.op_or, this);\n                }\n                if (this.isEven()) {\n                    this.dAddOffset(1, 0);\n                } // force odd\n                while (!this.isProbablePrime(b)) {\n                    this.dAddOffset(2, 0);\n                    if (this.bitLength() > a) {\n                        this.subTo(BigInteger.ONE.shiftLeft(a - 1), this);\n                    }\n                }\n            }\n        }\n        else {\n            // new BigInteger(int,RNG)\n            var x = [];\n            var t = a & 7;\n            x.length = (a >> 3) + 1;\n            b.nextBytes(x);\n            if (t > 0) {\n                x[0] &= ((1 << t) - 1);\n            }\n            else {\n                x[0] = 0;\n            }\n            this.fromString(x, 256);\n        }\n    };\n    // BigInteger.prototype.bitwiseTo = bnpBitwiseTo;\n    // (protected) r = this op a (bitwise)\n    BigInteger.prototype.bitwiseTo = function (a, op, r) {\n        var i;\n        var f;\n        var m = Math.min(a.t, this.t);\n        for (i = 0; i < m; ++i) {\n            r[i] = op(this[i], a[i]);\n        }\n        if (a.t < this.t) {\n            f = a.s & this.DM;\n            for (i = m; i < this.t; ++i) {\n                r[i] = op(this[i], f);\n            }\n            r.t = this.t;\n        }\n        else {\n            f = this.s & this.DM;\n            for (i = m; i < a.t; ++i) {\n                r[i] = op(f, a[i]);\n            }\n            r.t = a.t;\n        }\n        r.s = op(this.s, a.s);\n        r.clamp();\n    };\n    // BigInteger.prototype.changeBit = bnpChangeBit;\n    // (protected) this op (1<<n)\n    BigInteger.prototype.changeBit = function (n, op) {\n        var r = BigInteger.ONE.shiftLeft(n);\n        this.bitwiseTo(r, op, r);\n        return r;\n    };\n    // BigInteger.prototype.addTo = bnpAddTo;\n    // (protected) r = this + a\n    BigInteger.prototype.addTo = function (a, r) {\n        var i = 0;\n        var c = 0;\n        var m = Math.min(a.t, this.t);\n        while (i < m) {\n            c += this[i] + a[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        if (a.t < this.t) {\n            c += a.s;\n            while (i < this.t) {\n                c += this[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c += this.s;\n        }\n        else {\n            c += this.s;\n            while (i < a.t) {\n                c += a[i];\n                r[i++] = c & this.DM;\n                c >>= this.DB;\n            }\n            c += a.s;\n        }\n        r.s = (c < 0) ? -1 : 0;\n        if (c > 0) {\n            r[i++] = c;\n        }\n        else if (c < -1) {\n            r[i++] = this.DV + c;\n        }\n        r.t = i;\n        r.clamp();\n    };\n    // BigInteger.prototype.dMultiply = bnpDMultiply;\n    // (protected) this *= n, this >= 0, 1 < n < DV\n    BigInteger.prototype.dMultiply = function (n) {\n        this[this.t] = this.am(0, n - 1, this, 0, 0, this.t);\n        ++this.t;\n        this.clamp();\n    };\n    // BigInteger.prototype.dAddOffset = bnpDAddOffset;\n    // (protected) this += n << w words, this >= 0\n    BigInteger.prototype.dAddOffset = function (n, w) {\n        if (n == 0) {\n            return;\n        }\n        while (this.t <= w) {\n            this[this.t++] = 0;\n        }\n        this[w] += n;\n        while (this[w] >= this.DV) {\n            this[w] -= this.DV;\n            if (++w >= this.t) {\n                this[this.t++] = 0;\n            }\n            ++this[w];\n        }\n    };\n    // BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;\n    // (protected) r = lower n words of \"this * a\", a.t <= n\n    // \"this\" should be the larger one if appropriate.\n    BigInteger.prototype.multiplyLowerTo = function (a, n, r) {\n        var i = Math.min(this.t + a.t, n);\n        r.s = 0; // assumes a,this >= 0\n        r.t = i;\n        while (i > 0) {\n            r[--i] = 0;\n        }\n        for (var j = r.t - this.t; i < j; ++i) {\n            r[i + this.t] = this.am(0, a[i], r, i, 0, this.t);\n        }\n        for (var j = Math.min(a.t, n); i < j; ++i) {\n            this.am(0, a[i], r, i, 0, n - i);\n        }\n        r.clamp();\n    };\n    // BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;\n    // (protected) r = \"this * a\" without lower n words, n > 0\n    // \"this\" should be the larger one if appropriate.\n    BigInteger.prototype.multiplyUpperTo = function (a, n, r) {\n        --n;\n        var i = r.t = this.t + a.t - n;\n        r.s = 0; // assumes a,this >= 0\n        while (--i >= 0) {\n            r[i] = 0;\n        }\n        for (i = Math.max(n - this.t, 0); i < a.t; ++i) {\n            r[this.t + i - n] = this.am(n - i, a[i], r, 0, 0, this.t + i - n);\n        }\n        r.clamp();\n        r.drShiftTo(1, r);\n    };\n    // BigInteger.prototype.modInt = bnpModInt;\n    // (protected) this % n, n < 2^26\n    BigInteger.prototype.modInt = function (n) {\n        if (n <= 0) {\n            return 0;\n        }\n        var d = this.DV % n;\n        var r = (this.s < 0) ? n - 1 : 0;\n        if (this.t > 0) {\n            if (d == 0) {\n                r = this[0] % n;\n            }\n            else {\n                for (var i = this.t - 1; i >= 0; --i) {\n                    r = (d * r + this[i]) % n;\n                }\n            }\n        }\n        return r;\n    };\n    // BigInteger.prototype.millerRabin = bnpMillerRabin;\n    // (protected) true if probably prime (HAC 4.24, Miller-Rabin)\n    BigInteger.prototype.millerRabin = function (t) {\n        var n1 = this.subtract(BigInteger.ONE);\n        var k = n1.getLowestSetBit();\n        if (k <= 0) {\n            return false;\n        }\n        var r = n1.shiftRight(k);\n        t = (t + 1) >> 1;\n        if (t > lowprimes.length) {\n            t = lowprimes.length;\n        }\n        var a = nbi();\n        for (var i = 0; i < t; ++i) {\n            // Pick bases at random, instead of starting at 2\n            a.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)]);\n            var y = a.modPow(r, this);\n            if (y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {\n                var j = 1;\n                while (j++ < k && y.compareTo(n1) != 0) {\n                    y = y.modPowInt(2, this);\n                    if (y.compareTo(BigInteger.ONE) == 0) {\n                        return false;\n                    }\n                }\n                if (y.compareTo(n1) != 0) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    // BigInteger.prototype.square = bnSquare;\n    // (public) this^2\n    BigInteger.prototype.square = function () {\n        var r = nbi();\n        this.squareTo(r);\n        return r;\n    };\n    //#region ASYNC\n    // Public API method\n    BigInteger.prototype.gcda = function (a, callback) {\n        var x = (this.s < 0) ? this.negate() : this.clone();\n        var y = (a.s < 0) ? a.negate() : a.clone();\n        if (x.compareTo(y) < 0) {\n            var t = x;\n            x = y;\n            y = t;\n        }\n        var i = x.getLowestSetBit();\n        var g = y.getLowestSetBit();\n        if (g < 0) {\n            callback(x);\n            return;\n        }\n        if (i < g) {\n            g = i;\n        }\n        if (g > 0) {\n            x.rShiftTo(g, x);\n            y.rShiftTo(g, y);\n        }\n        // Workhorse of the algorithm, gets called 200 - 800 times per 512 bit keygen.\n        var gcda1 = function () {\n            if ((i = x.getLowestSetBit()) > 0) {\n                x.rShiftTo(i, x);\n            }\n            if ((i = y.getLowestSetBit()) > 0) {\n                y.rShiftTo(i, y);\n            }\n            if (x.compareTo(y) >= 0) {\n                x.subTo(y, x);\n                x.rShiftTo(1, x);\n            }\n            else {\n                y.subTo(x, y);\n                y.rShiftTo(1, y);\n            }\n            if (!(x.signum() > 0)) {\n                if (g > 0) {\n                    y.lShiftTo(g, y);\n                }\n                setTimeout(function () { callback(y); }, 0); // escape\n            }\n            else {\n                setTimeout(gcda1, 0);\n            }\n        };\n        setTimeout(gcda1, 10);\n    };\n    // (protected) alternate constructor\n    BigInteger.prototype.fromNumberAsync = function (a, b, c, callback) {\n        if (\"number\" == typeof b) {\n            if (a < 2) {\n                this.fromInt(1);\n            }\n            else {\n                this.fromNumber(a, c);\n                if (!this.testBit(a - 1)) {\n                    this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), _util__WEBPACK_IMPORTED_MODULE_0__.op_or, this);\n                }\n                if (this.isEven()) {\n                    this.dAddOffset(1, 0);\n                }\n                var bnp_1 = this;\n                var bnpfn1_1 = function () {\n                    bnp_1.dAddOffset(2, 0);\n                    if (bnp_1.bitLength() > a) {\n                        bnp_1.subTo(BigInteger.ONE.shiftLeft(a - 1), bnp_1);\n                    }\n                    if (bnp_1.isProbablePrime(b)) {\n                        setTimeout(function () { callback(); }, 0); // escape\n                    }\n                    else {\n                        setTimeout(bnpfn1_1, 0);\n                    }\n                };\n                setTimeout(bnpfn1_1, 0);\n            }\n        }\n        else {\n            var x = [];\n            var t = a & 7;\n            x.length = (a >> 3) + 1;\n            b.nextBytes(x);\n            if (t > 0) {\n                x[0] &= ((1 << t) - 1);\n            }\n            else {\n                x[0] = 0;\n            }\n            this.fromString(x, 256);\n        }\n    };\n    return BigInteger;\n}());\n\n//#region REDUCERS\n//#region NullExp\nvar NullExp = /** @class */ (function () {\n    function NullExp() {\n    }\n    // NullExp.prototype.convert = nNop;\n    NullExp.prototype.convert = function (x) {\n        return x;\n    };\n    // NullExp.prototype.revert = nNop;\n    NullExp.prototype.revert = function (x) {\n        return x;\n    };\n    // NullExp.prototype.mulTo = nMulTo;\n    NullExp.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n    };\n    // NullExp.prototype.sqrTo = nSqrTo;\n    NullExp.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n    };\n    return NullExp;\n}());\n// Modular reduction using \"classic\" algorithm\nvar Classic = /** @class */ (function () {\n    function Classic(m) {\n        this.m = m;\n    }\n    // Classic.prototype.convert = cConvert;\n    Classic.prototype.convert = function (x) {\n        if (x.s < 0 || x.compareTo(this.m) >= 0) {\n            return x.mod(this.m);\n        }\n        else {\n            return x;\n        }\n    };\n    // Classic.prototype.revert = cRevert;\n    Classic.prototype.revert = function (x) {\n        return x;\n    };\n    // Classic.prototype.reduce = cReduce;\n    Classic.prototype.reduce = function (x) {\n        x.divRemTo(this.m, null, x);\n    };\n    // Classic.prototype.mulTo = cMulTo;\n    Classic.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n        this.reduce(r);\n    };\n    // Classic.prototype.sqrTo = cSqrTo;\n    Classic.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n        this.reduce(r);\n    };\n    return Classic;\n}());\n//#endregion\n//#region Montgomery\n// Montgomery reduction\nvar Montgomery = /** @class */ (function () {\n    function Montgomery(m) {\n        this.m = m;\n        this.mp = m.invDigit();\n        this.mpl = this.mp & 0x7fff;\n        this.mph = this.mp >> 15;\n        this.um = (1 << (m.DB - 15)) - 1;\n        this.mt2 = 2 * m.t;\n    }\n    // Montgomery.prototype.convert = montConvert;\n    // xR mod m\n    Montgomery.prototype.convert = function (x) {\n        var r = nbi();\n        x.abs().dlShiftTo(this.m.t, r);\n        r.divRemTo(this.m, null, r);\n        if (x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) {\n            this.m.subTo(r, r);\n        }\n        return r;\n    };\n    // Montgomery.prototype.revert = montRevert;\n    // x/R mod m\n    Montgomery.prototype.revert = function (x) {\n        var r = nbi();\n        x.copyTo(r);\n        this.reduce(r);\n        return r;\n    };\n    // Montgomery.prototype.reduce = montReduce;\n    // x = x/R mod m (HAC 14.32)\n    Montgomery.prototype.reduce = function (x) {\n        while (x.t <= this.mt2) {\n            // pad x so am has enough room later\n            x[x.t++] = 0;\n        }\n        for (var i = 0; i < this.m.t; ++i) {\n            // faster way of calculating u0 = x[i]*mp mod DV\n            var j = x[i] & 0x7fff;\n            var u0 = (j * this.mpl + (((j * this.mph + (x[i] >> 15) * this.mpl) & this.um) << 15)) & x.DM;\n            // use am to combine the multiply-shift-add into one call\n            j = i + this.m.t;\n            x[j] += this.m.am(0, u0, x, i, 0, this.m.t);\n            // propagate carry\n            while (x[j] >= x.DV) {\n                x[j] -= x.DV;\n                x[++j]++;\n            }\n        }\n        x.clamp();\n        x.drShiftTo(this.m.t, x);\n        if (x.compareTo(this.m) >= 0) {\n            x.subTo(this.m, x);\n        }\n    };\n    // Montgomery.prototype.mulTo = montMulTo;\n    // r = \"xy/R mod m\"; x,y != r\n    Montgomery.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n        this.reduce(r);\n    };\n    // Montgomery.prototype.sqrTo = montSqrTo;\n    // r = \"x^2/R mod m\"; x != r\n    Montgomery.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n        this.reduce(r);\n    };\n    return Montgomery;\n}());\n//#endregion Montgomery\n//#region Barrett\n// Barrett modular reduction\nvar Barrett = /** @class */ (function () {\n    function Barrett(m) {\n        this.m = m;\n        // setup Barrett\n        this.r2 = nbi();\n        this.q3 = nbi();\n        BigInteger.ONE.dlShiftTo(2 * m.t, this.r2);\n        this.mu = this.r2.divide(m);\n    }\n    // Barrett.prototype.convert = barrettConvert;\n    Barrett.prototype.convert = function (x) {\n        if (x.s < 0 || x.t > 2 * this.m.t) {\n            return x.mod(this.m);\n        }\n        else if (x.compareTo(this.m) < 0) {\n            return x;\n        }\n        else {\n            var r = nbi();\n            x.copyTo(r);\n            this.reduce(r);\n            return r;\n        }\n    };\n    // Barrett.prototype.revert = barrettRevert;\n    Barrett.prototype.revert = function (x) {\n        return x;\n    };\n    // Barrett.prototype.reduce = barrettReduce;\n    // x = x mod m (HAC 14.42)\n    Barrett.prototype.reduce = function (x) {\n        x.drShiftTo(this.m.t - 1, this.r2);\n        if (x.t > this.m.t + 1) {\n            x.t = this.m.t + 1;\n            x.clamp();\n        }\n        this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3);\n        this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2);\n        while (x.compareTo(this.r2) < 0) {\n            x.dAddOffset(1, this.m.t + 1);\n        }\n        x.subTo(this.r2, x);\n        while (x.compareTo(this.m) >= 0) {\n            x.subTo(this.m, x);\n        }\n    };\n    // Barrett.prototype.mulTo = barrettMulTo;\n    // r = x*y mod m; x,y != r\n    Barrett.prototype.mulTo = function (x, y, r) {\n        x.multiplyTo(y, r);\n        this.reduce(r);\n    };\n    // Barrett.prototype.sqrTo = barrettSqrTo;\n    // r = x^2 mod m; x != r\n    Barrett.prototype.sqrTo = function (x, r) {\n        x.squareTo(r);\n        this.reduce(r);\n    };\n    return Barrett;\n}());\n//#endregion\n//#endregion REDUCERS\n// return new, unset BigInteger\nfunction nbi() { return new BigInteger(null); }\nfunction parseBigInt(str, r) {\n    return new BigInteger(str, r);\n}\n// am: Compute w_j += (x*this_i), propagate carries,\n// c is initial carry, returns final carry.\n// c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n// We need to select the fastest one that works in this environment.\nvar inBrowser = typeof navigator !== \"undefined\";\nif (inBrowser && j_lm && (navigator.appName == \"Microsoft Internet Explorer\")) {\n    // am2 avoids a big mult-and-extract completely.\n    // Max digit bits should be <= 30 because we do bitwise ops\n    // on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\n    BigInteger.prototype.am = function am2(i, x, w, j, c, n) {\n        var xl = x & 0x7fff;\n        var xh = x >> 15;\n        while (--n >= 0) {\n            var l = this[i] & 0x7fff;\n            var h = this[i++] >> 15;\n            var m = xh * l + h * xl;\n            l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff);\n            c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30);\n            w[j++] = l & 0x3fffffff;\n        }\n        return c;\n    };\n    dbits = 30;\n}\nelse if (inBrowser && j_lm && (navigator.appName != \"Netscape\")) {\n    // am1: use a single mult and divide to get the high bits,\n    // max digit bits should be 26 because\n    // max internal value = 2*dvalue^2-2*dvalue (< 2^53)\n    BigInteger.prototype.am = function am1(i, x, w, j, c, n) {\n        while (--n >= 0) {\n            var v = x * this[i++] + w[j] + c;\n            c = Math.floor(v / 0x4000000);\n            w[j++] = v & 0x3ffffff;\n        }\n        return c;\n    };\n    dbits = 26;\n}\nelse { // Mozilla/Netscape seems to prefer am3\n    // Alternately, set max digit bits to 28 since some\n    // browsers slow down when dealing with 32-bit numbers.\n    BigInteger.prototype.am = function am3(i, x, w, j, c, n) {\n        var xl = x & 0x3fff;\n        var xh = x >> 14;\n        while (--n >= 0) {\n            var l = this[i] & 0x3fff;\n            var h = this[i++] >> 14;\n            var m = xh * l + h * xl;\n            l = xl * l + ((m & 0x3fff) << 14) + w[j] + c;\n            c = (l >> 28) + (m >> 14) + xh * h;\n            w[j++] = l & 0xfffffff;\n        }\n        return c;\n    };\n    dbits = 28;\n}\nBigInteger.prototype.DB = dbits;\nBigInteger.prototype.DM = ((1 << dbits) - 1);\nBigInteger.prototype.DV = (1 << dbits);\nvar BI_FP = 52;\nBigInteger.prototype.FV = Math.pow(2, BI_FP);\nBigInteger.prototype.F1 = BI_FP - dbits;\nBigInteger.prototype.F2 = 2 * dbits - BI_FP;\n// Digit conversions\nvar BI_RC = [];\nvar rr;\nvar vv;\nrr = \"0\".charCodeAt(0);\nfor (vv = 0; vv <= 9; ++vv) {\n    BI_RC[rr++] = vv;\n}\nrr = \"a\".charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) {\n    BI_RC[rr++] = vv;\n}\nrr = \"A\".charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) {\n    BI_RC[rr++] = vv;\n}\nfunction intAt(s, i) {\n    var c = BI_RC[s.charCodeAt(i)];\n    return (c == null) ? -1 : c;\n}\n// return bigint initialized to value\nfunction nbv(i) {\n    var r = nbi();\n    r.fromInt(i);\n    return r;\n}\n// returns bit length of the integer x\nfunction nbits(x) {\n    var r = 1;\n    var t;\n    if ((t = x >>> 16) != 0) {\n        x = t;\n        r += 16;\n    }\n    if ((t = x >> 8) != 0) {\n        x = t;\n        r += 8;\n    }\n    if ((t = x >> 4) != 0) {\n        x = t;\n        r += 4;\n    }\n    if ((t = x >> 2) != 0) {\n        x = t;\n        r += 2;\n    }\n    if ((t = x >> 1) != 0) {\n        x = t;\n        r += 1;\n    }\n    return r;\n}\n// \"constants\"\nBigInteger.ZERO = nbv(0);\nBigInteger.ONE = nbv(1);\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/jsbn.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/prng4.js":
/*!*******************************!*\
  !*** ./lib/lib/jsbn/prng4.js ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arcfour: function() { return /* binding */ Arcfour; },\n/* harmony export */   prng_newstate: function() { return /* binding */ prng_newstate; },\n/* harmony export */   rng_psize: function() { return /* binding */ rng_psize; }\n/* harmony export */ });\n// prng4.js - uses Arcfour as a PRNG\nvar Arcfour = /** @class */ (function () {\n    function Arcfour() {\n        this.i = 0;\n        this.j = 0;\n        this.S = [];\n    }\n    // Arcfour.prototype.init = ARC4init;\n    // Initialize arcfour context from key, an array of ints, each from [0..255]\n    Arcfour.prototype.init = function (key) {\n        var i;\n        var j;\n        var t;\n        for (i = 0; i < 256; ++i) {\n            this.S[i] = i;\n        }\n        j = 0;\n        for (i = 0; i < 256; ++i) {\n            j = (j + this.S[i] + key[i % key.length]) & 255;\n            t = this.S[i];\n            this.S[i] = this.S[j];\n            this.S[j] = t;\n        }\n        this.i = 0;\n        this.j = 0;\n    };\n    // Arcfour.prototype.next = ARC4next;\n    Arcfour.prototype.next = function () {\n        var t;\n        this.i = (this.i + 1) & 255;\n        this.j = (this.j + this.S[this.i]) & 255;\n        t = this.S[this.i];\n        this.S[this.i] = this.S[this.j];\n        this.S[this.j] = t;\n        return this.S[(t + this.S[this.i]) & 255];\n    };\n    return Arcfour;\n}());\n\n// Plug in your RNG constructor here\nfunction prng_newstate() {\n    return new Arcfour();\n}\n// Pool size must be a multiple of 4 and greater than 32.\n// An array of bytes the size of the pool will be passed to init()\nvar rng_psize = 256;\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/prng4.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/rng.js":
/*!*****************************!*\
  !*** ./lib/lib/jsbn/rng.js ***!
  \*****************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureRandom: function() { return /* binding */ SecureRandom; }\n/* harmony export */ });\n/* harmony import */ var _prng4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prng4 */ \"./lib/lib/jsbn/prng4.js\");\n// Random number generator - requires a PRNG backend, e.g. prng4.js\n\nvar rng_state;\nvar rng_pool = null;\nvar rng_pptr;\n// Initialize the pool with junk if needed.\nif (rng_pool == null) {\n    rng_pool = [];\n    rng_pptr = 0;\n    var t = void 0;\n    if (typeof window !== 'undefined' && self.crypto && self.crypto.getRandomValues) {\n        // Extract entropy (2048 bits) from RNG if available\n        var z = new Uint32Array(256);\n        self.crypto.getRandomValues(z);\n        for (t = 0; t < z.length; ++t) {\n            rng_pool[rng_pptr++] = z[t] & 255;\n        }\n    }\n    // Use mouse events for entropy, if we do not have enough entropy by the time\n    // we need it, entropy will be generated by Math.random.\n    var count = 0;\n    var onMouseMoveListener_1 = function (ev) {\n        count = count || 0;\n        if (count >= 256 || rng_pptr >= _prng4__WEBPACK_IMPORTED_MODULE_0__.rng_psize) {\n            if (self.removeEventListener) {\n                self.removeEventListener(\"mousemove\", onMouseMoveListener_1, false);\n            }\n            else if (self.detachEvent) {\n                self.detachEvent(\"onmousemove\", onMouseMoveListener_1);\n            }\n            return;\n        }\n        try {\n            var mouseCoordinates = ev.x + ev.y;\n            rng_pool[rng_pptr++] = mouseCoordinates & 255;\n            count += 1;\n        }\n        catch (e) {\n            // Sometimes Firefox will deny permission to access event properties for some reason. Ignore.\n        }\n    };\n    if (typeof window !== 'undefined') {\n        if (self.addEventListener) {\n            self.addEventListener(\"mousemove\", onMouseMoveListener_1, false);\n        }\n        else if (self.attachEvent) {\n            self.attachEvent(\"onmousemove\", onMouseMoveListener_1);\n        }\n    }\n}\nfunction rng_get_byte() {\n    if (rng_state == null) {\n        rng_state = (0,_prng4__WEBPACK_IMPORTED_MODULE_0__.prng_newstate)();\n        // At this point, we may not have collected enough entropy.  If not, fall back to Math.random\n        while (rng_pptr < _prng4__WEBPACK_IMPORTED_MODULE_0__.rng_psize) {\n            var random = Math.floor(65536 * Math.random());\n            rng_pool[rng_pptr++] = random & 255;\n        }\n        rng_state.init(rng_pool);\n        for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) {\n            rng_pool[rng_pptr] = 0;\n        }\n        rng_pptr = 0;\n    }\n    // TODO: allow reseeding after first request\n    return rng_state.next();\n}\nvar SecureRandom = /** @class */ (function () {\n    function SecureRandom() {\n    }\n    SecureRandom.prototype.nextBytes = function (ba) {\n        for (var i = 0; i < ba.length; ++i) {\n            ba[i] = rng_get_byte();\n        }\n    };\n    return SecureRandom;\n}());\n\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/rng.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/rsa.js":
/*!*****************************!*\
  !*** ./lib/lib/jsbn/rsa.js ***!
  \*****************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RSAKey: function() { return /* binding */ RSAKey; },\n/* harmony export */   oaep_pad: function() { return /* binding */ oaep_pad; }\n/* harmony export */ });\n/* harmony import */ var _jsbn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./jsbn */ \"./lib/lib/jsbn/jsbn.js\");\n/* harmony import */ var _rng__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng */ \"./lib/lib/jsbn/rng.js\");\n/* harmony import */ var _sha256__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sha256 */ \"./lib/lib/jsbn/sha256.js\");\n// Depends on jsbn.js and rng.js\n// Version 1.1: support utf-8 encoding in pkcs1pad2\n// convert a (hex) string to a bignum object\n\n\n\n// function linebrk(s,n) {\n//   var ret = \"\";\n//   var i = 0;\n//   while(i + n < s.length) {\n//     ret += s.substring(i,i+n) + \"\\n\";\n//     i += n;\n//   }\n//   return ret + s.substring(i,s.length);\n// }\n// function byte2Hex(b) {\n//   if(b < 0x10)\n//     return \"0\" + b.toString(16);\n//   else\n//     return b.toString(16);\n// }\nfunction pkcs1pad1(s, n) {\n    if (n < s.length + 22) {\n        console.error(\"Message too long for RSA\");\n        return null;\n    }\n    var len = n - s.length - 6;\n    var filler = \"\";\n    for (var f = 0; f < len; f += 2) {\n        filler += \"ff\";\n    }\n    var m = \"0001\" + filler + \"00\" + s;\n    return (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(m, 16);\n}\n// PKCS#1 (type 2, random) pad input string s to n bytes, and return a bigint\nfunction pkcs1pad2(s, n) {\n    if (n < s.length + 11) { // TODO: fix for utf-8\n        console.error(\"Message too long for RSA\");\n        return null;\n    }\n    var ba = [];\n    var i = s.length - 1;\n    while (i >= 0 && n > 0) {\n        var c = s.charCodeAt(i--);\n        if (c < 128) { // encode using utf-8\n            ba[--n] = c;\n        }\n        else if ((c > 127) && (c < 2048)) {\n            ba[--n] = (c & 63) | 128;\n            ba[--n] = (c >> 6) | 192;\n        }\n        else {\n            ba[--n] = (c & 63) | 128;\n            ba[--n] = ((c >> 6) & 63) | 128;\n            ba[--n] = (c >> 12) | 224;\n        }\n    }\n    ba[--n] = 0;\n    var rng = new _rng__WEBPACK_IMPORTED_MODULE_1__.SecureRandom();\n    var x = [];\n    while (n > 2) { // random non-zero pad\n        x[0] = 0;\n        while (x[0] == 0) {\n            rng.nextBytes(x);\n        }\n        ba[--n] = x[0];\n    }\n    ba[--n] = 2;\n    ba[--n] = 0;\n    return new _jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(ba);\n}\n// PKCS#1 (OAEP) mask generation function, using SHA-256\nfunction oaep_mgf1_arr(seed, len, hashFunc) {\n    var mask = \"\", i = 0;\n    while (mask.length < len) {\n        mask += hashFunc(String.fromCharCode.apply(String, seed.concat([\n            (i & 0xff000000) >> 24,\n            (i & 0x00ff0000) >> 16,\n            (i & 0x0000ff00) >> 8,\n            i & 0x000000ff,\n        ])));\n        i += 1;\n    }\n    return mask;\n}\nvar SHA256_SIZE = 32;\n// PKCS#1 (OAEP) pad input string s to n bytes, and return a BigInteger\nfunction oaep_pad(s, n) {\n    var hashLen = SHA256_SIZE;\n    var hashFunc = _sha256__WEBPACK_IMPORTED_MODULE_2__.rstr_sha256;\n    if (s.length + 2 * hashLen + 2 > n) {\n        throw \"Message too long for RSA\";\n    }\n    var PS = \"\", i;\n    for (i = 0; i < n - s.length - 2 * hashLen - 2; i += 1) {\n        PS += \"\\x00\";\n    }\n    var DB = hashFunc(\"\") + PS + \"\\x01\" + s, seed = new Array(hashLen);\n    new _rng__WEBPACK_IMPORTED_MODULE_1__.SecureRandom().nextBytes(seed);\n    var dbMask = oaep_mgf1_arr(seed, DB.length, hashFunc), maskedDB = [];\n    for (i = 0; i < DB.length; i += 1) {\n        maskedDB[i] = DB.charCodeAt(i) ^ dbMask.charCodeAt(i);\n    }\n    var seedMask = oaep_mgf1_arr(maskedDB, seed.length, hashFunc), maskedSeed = [0];\n    for (i = 0; i < seed.length; i += 1) {\n        maskedSeed[i + 1] = seed[i] ^ seedMask.charCodeAt(i);\n    }\n    return new _jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(maskedSeed.concat(maskedDB));\n}\n// \"empty\" RSA key constructor\nvar RSAKey = /** @class */ (function () {\n    function RSAKey() {\n        this.n = null;\n        this.e = 0;\n        this.d = null;\n        this.p = null;\n        this.q = null;\n        this.dmp1 = null;\n        this.dmq1 = null;\n        this.coeff = null;\n    }\n    //#region PROTECTED\n    // protected\n    // RSAKey.prototype.doPublic = RSADoPublic;\n    // Perform raw public operation on \"x\": return x^e (mod n)\n    RSAKey.prototype.doPublic = function (x) {\n        return x.modPowInt(this.e, this.n);\n    };\n    // RSAKey.prototype.doPrivate = RSADoPrivate;\n    // Perform raw private operation on \"x\": return x^d (mod n)\n    RSAKey.prototype.doPrivate = function (x) {\n        if (this.p == null || this.q == null) {\n            return x.modPow(this.d, this.n);\n        }\n        // TODO: re-calculate any missing CRT params\n        var xp = x.mod(this.p).modPow(this.dmp1, this.p);\n        var xq = x.mod(this.q).modPow(this.dmq1, this.q);\n        while (xp.compareTo(xq) < 0) {\n            xp = xp.add(this.p);\n        }\n        return xp.subtract(xq).multiply(this.coeff).mod(this.p).multiply(this.q).add(xq);\n    };\n    //#endregion PROTECTED\n    //#region PUBLIC\n    // RSAKey.prototype.setPublic = RSASetPublic;\n    // Set the public key fields N and e from hex strings\n    RSAKey.prototype.setPublic = function (N, E) {\n        if (N != null && E != null && N.length > 0 && E.length > 0) {\n            this.n = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(N, 16);\n            this.e = parseInt(E, 16);\n        }\n        else {\n            console.error(\"Invalid RSA public key\");\n        }\n    };\n    // RSAKey.prototype.encrypt = RSAEncrypt;\n    // Return the PKCS#1 RSA encryption of \"text\" as an even-length hex string\n    RSAKey.prototype.encrypt = function (text, paddingFunction) {\n        if (typeof paddingFunction === 'undefined') {\n            paddingFunction = pkcs1pad2;\n        }\n        var maxLength = (this.n.bitLength() + 7) >> 3;\n        var m = paddingFunction(text, maxLength);\n        if (m == null) {\n            return null;\n        }\n        var c = this.doPublic(m);\n        if (c == null) {\n            return null;\n        }\n        var h = c.toString(16);\n        var length = h.length;\n        // fix zero before result\n        for (var i = 0; i < maxLength * 2 - length; i++) {\n            h = \"0\" + h;\n        }\n        return h;\n    };\n    // RSAKey.prototype.setPrivate = RSASetPrivate;\n    // Set the private key fields N, e, and d from hex strings\n    RSAKey.prototype.setPrivate = function (N, E, D) {\n        if (N != null && E != null && N.length > 0 && E.length > 0) {\n            this.n = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(N, 16);\n            this.e = parseInt(E, 16);\n            this.d = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(D, 16);\n        }\n        else {\n            console.error(\"Invalid RSA private key\");\n        }\n    };\n    // RSAKey.prototype.setPrivateEx = RSASetPrivateEx;\n    // Set the private key fields N, e, d and CRT params from hex strings\n    RSAKey.prototype.setPrivateEx = function (N, E, D, P, Q, DP, DQ, C) {\n        if (N != null && E != null && N.length > 0 && E.length > 0) {\n            this.n = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(N, 16);\n            this.e = parseInt(E, 16);\n            this.d = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(D, 16);\n            this.p = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(P, 16);\n            this.q = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(Q, 16);\n            this.dmp1 = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(DP, 16);\n            this.dmq1 = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(DQ, 16);\n            this.coeff = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(C, 16);\n        }\n        else {\n            console.error(\"Invalid RSA private key\");\n        }\n    };\n    // RSAKey.prototype.generate = RSAGenerate;\n    // Generate a new random private key B bits long, using public expt E\n    RSAKey.prototype.generate = function (B, E) {\n        var rng = new _rng__WEBPACK_IMPORTED_MODULE_1__.SecureRandom();\n        var qs = B >> 1;\n        this.e = parseInt(E, 16);\n        var ee = new _jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(E, 16);\n        for (;;) {\n            for (;;) {\n                this.p = new _jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(B - qs, 1, rng);\n                if (this.p.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE).gcd(ee).compareTo(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE) == 0 && this.p.isProbablePrime(10)) {\n                    break;\n                }\n            }\n            for (;;) {\n                this.q = new _jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(qs, 1, rng);\n                if (this.q.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE).gcd(ee).compareTo(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE) == 0 && this.q.isProbablePrime(10)) {\n                    break;\n                }\n            }\n            if (this.p.compareTo(this.q) <= 0) {\n                var t = this.p;\n                this.p = this.q;\n                this.q = t;\n            }\n            var p1 = this.p.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE);\n            var q1 = this.q.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE);\n            var phi = p1.multiply(q1);\n            if (phi.gcd(ee).compareTo(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE) == 0) {\n                this.n = this.p.multiply(this.q);\n                this.d = ee.modInverse(phi);\n                this.dmp1 = this.d.mod(p1);\n                this.dmq1 = this.d.mod(q1);\n                this.coeff = this.q.modInverse(this.p);\n                break;\n            }\n        }\n    };\n    // RSAKey.prototype.decrypt = RSADecrypt;\n    // Return the PKCS#1 RSA decryption of \"ctext\".\n    // \"ctext\" is an even-length hex string and the output is a plain string.\n    RSAKey.prototype.decrypt = function (ctext) {\n        var c = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(ctext, 16);\n        var m = this.doPrivate(c);\n        if (m == null) {\n            return null;\n        }\n        return pkcs1unpad2(m, (this.n.bitLength() + 7) >> 3);\n    };\n    // Generate a new random private key B bits long, using public expt E\n    RSAKey.prototype.generateAsync = function (B, E, callback) {\n        var rng = new _rng__WEBPACK_IMPORTED_MODULE_1__.SecureRandom();\n        var qs = B >> 1;\n        this.e = parseInt(E, 16);\n        var ee = new _jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(E, 16);\n        var rsa = this;\n        // These functions have non-descript names because they were originally for(;;) loops.\n        // I don't know about cryptography to give them better names than loop1-4.\n        var loop1 = function () {\n            var loop4 = function () {\n                if (rsa.p.compareTo(rsa.q) <= 0) {\n                    var t = rsa.p;\n                    rsa.p = rsa.q;\n                    rsa.q = t;\n                }\n                var p1 = rsa.p.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE);\n                var q1 = rsa.q.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE);\n                var phi = p1.multiply(q1);\n                if (phi.gcd(ee).compareTo(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE) == 0) {\n                    rsa.n = rsa.p.multiply(rsa.q);\n                    rsa.d = ee.modInverse(phi);\n                    rsa.dmp1 = rsa.d.mod(p1);\n                    rsa.dmq1 = rsa.d.mod(q1);\n                    rsa.coeff = rsa.q.modInverse(rsa.p);\n                    setTimeout(function () { callback(); }, 0); // escape\n                }\n                else {\n                    setTimeout(loop1, 0);\n                }\n            };\n            var loop3 = function () {\n                rsa.q = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.nbi)();\n                rsa.q.fromNumberAsync(qs, 1, rng, function () {\n                    rsa.q.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE).gcda(ee, function (r) {\n                        if (r.compareTo(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE) == 0 && rsa.q.isProbablePrime(10)) {\n                            setTimeout(loop4, 0);\n                        }\n                        else {\n                            setTimeout(loop3, 0);\n                        }\n                    });\n                });\n            };\n            var loop2 = function () {\n                rsa.p = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.nbi)();\n                rsa.p.fromNumberAsync(B - qs, 1, rng, function () {\n                    rsa.p.subtract(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE).gcda(ee, function (r) {\n                        if (r.compareTo(_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE) == 0 && rsa.p.isProbablePrime(10)) {\n                            setTimeout(loop3, 0);\n                        }\n                        else {\n                            setTimeout(loop2, 0);\n                        }\n                    });\n                });\n            };\n            setTimeout(loop2, 0);\n        };\n        setTimeout(loop1, 0);\n    };\n    RSAKey.prototype.sign = function (text, digestMethod, digestName) {\n        var header = getDigestHeader(digestName);\n        var digest = header + digestMethod(text).toString();\n        var maxLength = this.n.bitLength() / 4;\n        var m = pkcs1pad1(digest, maxLength);\n        if (m == null) {\n            return null;\n        }\n        var c = this.doPrivate(m);\n        if (c == null) {\n            return null;\n        }\n        var h = c.toString(16);\n        var length = h.length;\n        // fix zero before result\n        for (var i = 0; i < maxLength - length; i++) {\n            h = \"0\" + h;\n        }\n        return h;\n    };\n    RSAKey.prototype.verify = function (text, signature, digestMethod) {\n        var c = (0,_jsbn__WEBPACK_IMPORTED_MODULE_0__.parseBigInt)(signature, 16);\n        var m = this.doPublic(c);\n        if (m == null) {\n            return null;\n        }\n        var unpadded = m.toString(16).replace(/^1f+00/, \"\");\n        var digest = removeDigestHeader(unpadded);\n        return digest == digestMethod(text).toString();\n    };\n    return RSAKey;\n}());\n\n// Undo PKCS#1 (type 2, random) padding and, if valid, return the plaintext\nfunction pkcs1unpad2(d, n) {\n    var b = d.toByteArray();\n    var i = 0;\n    while (i < b.length && b[i] == 0) {\n        ++i;\n    }\n    if (b.length - i != n - 1 || b[i] != 2) {\n        return null;\n    }\n    ++i;\n    while (b[i] != 0) {\n        if (++i >= b.length) {\n            return null;\n        }\n    }\n    var ret = \"\";\n    while (++i < b.length) {\n        var c = b[i] & 255;\n        if (c < 128) { // utf-8 decode\n            ret += String.fromCharCode(c);\n        }\n        else if ((c > 191) && (c < 224)) {\n            ret += String.fromCharCode(((c & 31) << 6) | (b[i + 1] & 63));\n            ++i;\n        }\n        else {\n            ret += String.fromCharCode(((c & 15) << 12) | ((b[i + 1] & 63) << 6) | (b[i + 2] & 63));\n            i += 2;\n        }\n    }\n    return ret;\n}\n// https://tools.ietf.org/html/rfc3447#page-43\nvar DIGEST_HEADERS = {\n    md2: \"3020300c06082a864886f70d020205000410\",\n    md5: \"3020300c06082a864886f70d020505000410\",\n    sha1: \"3021300906052b0e03021a05000414\",\n    sha224: \"302d300d06096086480165030402040500041c\",\n    sha256: \"3031300d060960864801650304020105000420\",\n    sha384: \"3041300d060960864801650304020205000430\",\n    sha512: \"3051300d060960864801650304020305000440\",\n    ripemd160: \"3021300906052b2403020105000414\"\n};\nfunction getDigestHeader(name) {\n    return DIGEST_HEADERS[name] || \"\";\n}\nfunction removeDigestHeader(str) {\n    for (var name_1 in DIGEST_HEADERS) {\n        if (DIGEST_HEADERS.hasOwnProperty(name_1)) {\n            var header = DIGEST_HEADERS[name_1];\n            var len = header.length;\n            if (str.substring(0, len) == header) {\n                return str.substring(len);\n            }\n        }\n    }\n    return str;\n}\n// Return the PKCS#1 RSA encryption of \"text\" as a Base64-encoded string\n// function RSAEncryptB64(text) {\n//  var h = this.encrypt(text);\n//  if(h) return hex2b64(h); else return null;\n// }\n// public\n// RSAKey.prototype.encrypt_b64 = RSAEncryptB64;\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/rsa.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/sha256.js":
/*!********************************!*\
  !*** ./lib/lib/jsbn/sha256.js ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rstr2hex: function() { return /* binding */ rstr2hex; },\n/* harmony export */   rstr_sha256: function() { return /* binding */ rstr_sha256; }\n/* harmony export */ });\n/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-256, as defined\n * in FIPS 180-2\n * Version 2.2 Copyright Angel Marin, Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n * Also http://anmar.eu.org/projects/jssha2/\n */\n// /*\n//  * Configurable variables. You may need to tweak these to be compatible with\n//  * the server-side, but the defaults work in most cases.\n//  */\n// var hexcase = 0; /* hex output format. 0 - lowercase; 1 - uppercase        */\n// var b64pad = \"\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n// /*\n//  * These are the functions you'll usually want to call\n//  * They take string arguments and return either hex or base-64 encoded strings\n//  */\n// function hex_sha256(s: string) {\n//     return rstr2hex(rstr_sha256(str2rstr_utf8(s)));\n// }\n// function b64_sha256(s: string) {\n//     return rstr2b64(rstr_sha256(str2rstr_utf8(s)));\n// }\n// function any_sha256(s: string, e: any) {\n//     return rstr2any(rstr_sha256(str2rstr_utf8(s)), e);\n// }\n// function hex_hmac_sha256(k: string, d: any) {\n//     return rstr2hex(rstr_hmac_sha256(str2rstr_utf8(k), str2rstr_utf8(d)));\n// }\n// function b64_hmac_sha256(k: string, d: any) {\n//     return rstr2b64(rstr_hmac_sha256(str2rstr_utf8(k), str2rstr_utf8(d)));\n// }\n// function any_hmac_sha256(k: string, d: string, e: any) {\n//     return rstr2any(rstr_hmac_sha256(str2rstr_utf8(k), str2rstr_utf8(d)), e);\n// }\n// /*\n//  * Perform a simple self-test to see if the VM is working\n//  */\n// function sha256_vm_test() {\n//     return (\n//         hex_sha256(\"abc\").toLowerCase() ==\n//         \"ba7816bf8f01cfea414140de5dae2223b00361a396177a9cb410ff61f20015ad\"\n//     );\n// }\n/*\n * Calculate the sha256 of a raw string\n */\nfunction rstr_sha256(s) {\n    return binb2rstr(binb_sha256(rstr2binb(s), s.length * 8));\n}\n// /*\n//  * Calculate the HMAC-sha256 of a key and some data (raw strings)\n//  */\n// function rstr_hmac_sha256(key: string, data: any) {\n//     var bkey = rstr2binb(key);\n//     if (bkey.length > 16) bkey = binb_sha256(bkey, key.length * 8);\n//     var ipad = Array(16),\n//         opad = Array(16);\n//     for (var i = 0; i < 16; i++) {\n//         ipad[i] = bkey[i] ^ 0x36363636;\n//         opad[i] = bkey[i] ^ 0x5c5c5c5c;\n//     }\n//     var hash = binb_sha256(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n//     return binb2rstr(binb_sha256(opad.concat(hash), 512 + 256));\n// }\n// /*\n//  * Convert a raw string to a hex string\n//  */\nfunction rstr2hex(input) {\n    var hex_tab = \"0123456789abcdef\";\n    var output = \"\";\n    for (var i = 0; i < input.length; i++) {\n        var x = input.charCodeAt(i);\n        output += hex_tab.charAt((x >>> 4) & 0x0f) + hex_tab.charAt(x & 0x0f);\n    }\n    return output;\n}\n// /*\n//  * Convert a raw string to a base-64 string\n//  */\n// function rstr2b64(input: string) {\n//     try {\n//         b64pad;\n//     } catch (e) {\n//         b64pad = \"\";\n//     }\n//     var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n//     var output = \"\";\n//     var len = input.length;\n//     for (var i = 0; i < len; i += 3) {\n//         var triplet =\n//             (input.charCodeAt(i) << 16) |\n//             (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) |\n//             (i + 2 < len ? input.charCodeAt(i + 2) : 0);\n//         for (var j = 0; j < 4; j++) {\n//             if (i * 8 + j * 6 > input.length * 8) output += b64pad;\n//             else output += tab.charAt((triplet >>> (6 * (3 - j))) & 0x3f);\n//         }\n//     }\n//     return output;\n// }\n// /*\n//  * Convert a raw string to an arbitrary string encoding\n//  */\n// function rstr2any(input: string, encoding: string) {\n//     var divisor = encoding.length;\n//     var remainders = Array();\n//     var i, q, x, quotient;\n//     /* Convert to an array of 16-bit big-endian values, forming the dividend */\n//     var dividend = Array(Math.ceil(input.length / 2));\n//     for (i = 0; i < dividend.length; i++) {\n//         dividend[i] = (input.charCodeAt(i * 2) << 8) | input.charCodeAt(i * 2 + 1);\n//     }\n//     /*\n//      * Repeatedly perform a long division. The binary array forms the dividend,\n//      * the length of the encoding is the divisor. Once computed, the quotient\n//      * forms the dividend for the next step. We stop when the dividend is zero.\n//      * All remainders are stored for later use.\n//      */\n//     while (dividend.length > 0) {\n//         quotient = Array();\n//         x = 0;\n//         for (i = 0; i < dividend.length; i++) {\n//             x = (x << 16) + dividend[i];\n//             q = Math.floor(x / divisor);\n//             x -= q * divisor;\n//             if (quotient.length > 0 || q > 0) quotient[quotient.length] = q;\n//         }\n//         remainders[remainders.length] = x;\n//         dividend = quotient;\n//     }\n//     /* Convert the remainders to the output string */\n//     var output = \"\";\n//     for (i = remainders.length - 1; i >= 0; i--) output += encoding.charAt(remainders[i]);\n//     /* Append leading zero equivalents */\n//     var full_length = Math.ceil(\n//         (input.length * 8) / (Math.log(encoding.length) / Math.log(2)),\n//     );\n//     for (i = output.length; i < full_length; i++) output = encoding[0] + output;\n//     return output;\n// }\n// /*\n//  * Encode a string as utf-8.\n//  * For efficiency, this assumes the input is valid utf-16.\n//  */\n// function str2rstr_utf8(input: string) {\n//     var output = \"\";\n//     var i = -1;\n//     var x, y;\n//     while (++i < input.length) {\n//         /* Decode utf-16 surrogate pairs */\n//         x = input.charCodeAt(i);\n//         y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n//         if (0xd800 <= x && x <= 0xdbff && 0xdc00 <= y && y <= 0xdfff) {\n//             x = 0x10000 + ((x & 0x03ff) << 10) + (y & 0x03ff);\n//             i++;\n//         }\n//         /* Encode output as utf-8 */\n//         if (x <= 0x7f) output += String.fromCharCode(x);\n//         else if (x <= 0x7ff)\n//             output += String.fromCharCode(0xc0 | ((x >>> 6) & 0x1f), 0x80 | (x & 0x3f));\n//         else if (x <= 0xffff)\n//             output += String.fromCharCode(\n//                 0xe0 | ((x >>> 12) & 0x0f),\n//                 0x80 | ((x >>> 6) & 0x3f),\n//                 0x80 | (x & 0x3f),\n//             );\n//         else if (x <= 0x1fffff)\n//             output += String.fromCharCode(\n//                 0xf0 | ((x >>> 18) & 0x07),\n//                 0x80 | ((x >>> 12) & 0x3f),\n//                 0x80 | ((x >>> 6) & 0x3f),\n//                 0x80 | (x & 0x3f),\n//             );\n//     }\n//     return output;\n// }\n// /*\n//  * Encode a string as utf-16\n//  */\n// function str2rstr_utf16le(input: string) {\n//     var output = \"\";\n//     for (var i = 0; i < input.length; i++)\n//         output += String.fromCharCode(\n//             input.charCodeAt(i) & 0xff,\n//             (input.charCodeAt(i) >>> 8) & 0xff,\n//         );\n//     return output;\n// }\n// function str2rstr_utf16be(input: string) {\n//     var output = \"\";\n//     for (var i = 0; i < input.length; i++)\n//         output += String.fromCharCode(\n//             (input.charCodeAt(i) >>> 8) & 0xff,\n//             input.charCodeAt(i) & 0xff,\n//         );\n//     return output;\n// }\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input) {\n    var output = Array(input.length >> 2);\n    for (var i = 0; i < output.length; i++)\n        output[i] = 0;\n    for (var i = 0; i < input.length * 8; i += 8)\n        output[i >> 5] |= (input.charCodeAt(i / 8) & 0xff) << (24 - (i % 32));\n    return output;\n}\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input) {\n    var output = \"\";\n    for (var i = 0; i < input.length * 32; i += 8)\n        output += String.fromCharCode((input[i >> 5] >>> (24 - (i % 32))) & 0xff);\n    return output;\n}\n/*\n * Main sha256 function, with its support functions\n */\nfunction sha256_S(X, n) {\n    return (X >>> n) | (X << (32 - n));\n}\nfunction sha256_R(X, n) {\n    return X >>> n;\n}\nfunction sha256_Ch(x, y, z) {\n    return (x & y) ^ (~x & z);\n}\nfunction sha256_Maj(x, y, z) {\n    return (x & y) ^ (x & z) ^ (y & z);\n}\nfunction sha256_Sigma0256(x) {\n    return sha256_S(x, 2) ^ sha256_S(x, 13) ^ sha256_S(x, 22);\n}\nfunction sha256_Sigma1256(x) {\n    return sha256_S(x, 6) ^ sha256_S(x, 11) ^ sha256_S(x, 25);\n}\nfunction sha256_Gamma0256(x) {\n    return sha256_S(x, 7) ^ sha256_S(x, 18) ^ sha256_R(x, 3);\n}\nfunction sha256_Gamma1256(x) {\n    return sha256_S(x, 17) ^ sha256_S(x, 19) ^ sha256_R(x, 10);\n}\n// function sha256_Sigma0512(x: number) {\n//     return sha256_S(x, 28) ^ sha256_S(x, 34) ^ sha256_S(x, 39);\n// }\n// function sha256_Sigma1512(x: number) {\n//     return sha256_S(x, 14) ^ sha256_S(x, 18) ^ sha256_S(x, 41);\n// }\n// function sha256_Gamma0512(x: number) {\n//     return sha256_S(x, 1) ^ sha256_S(x, 8) ^ sha256_R(x, 7);\n// }\n// function sha256_Gamma1512(x: number) {\n//     return sha256_S(x, 19) ^ sha256_S(x, 61) ^ sha256_R(x, 6);\n// }\nvar sha256_K = new Array(1116352408, 1899447441, -1245643825, -373957723, 961987163, 1508970993, -1841331548, -1424204075, -670586216, 310598401, 607225278, 1426881987, 1925078388, -2132889090, -1680079193, -1046744716, -459576895, -272742522, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, -1740746414, -1473132947, -1341970488, -1084653625, -958395405, -710438585, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, -2117940946, -1838011259, -1564481375, -1474664885, -1035236496, -949202525, -778901479, -694614492, -200395387, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, -2067236844, -1933114872, -1866530822, -1538233109, -1090935817, -965641998);\nfunction binb_sha256(m, l) {\n    var HASH = new Array(1779033703, -1150833019, 1013904242, -1521486534, 1359893119, -1694144372, 528734635, 1541459225);\n    var W = new Array(64);\n    var a, b, c, d, e, f, g, h;\n    var i, j, T1, T2;\n    /* append padding */\n    m[l >> 5] |= 0x80 << (24 - (l % 32));\n    m[(((l + 64) >> 9) << 4) + 15] = l;\n    for (i = 0; i < m.length; i += 16) {\n        a = HASH[0];\n        b = HASH[1];\n        c = HASH[2];\n        d = HASH[3];\n        e = HASH[4];\n        f = HASH[5];\n        g = HASH[6];\n        h = HASH[7];\n        for (j = 0; j < 64; j++) {\n            if (j < 16)\n                W[j] = m[j + i];\n            else\n                W[j] = safe_add(safe_add(safe_add(sha256_Gamma1256(W[j - 2]), W[j - 7]), sha256_Gamma0256(W[j - 15])), W[j - 16]);\n            T1 = safe_add(safe_add(safe_add(safe_add(h, sha256_Sigma1256(e)), sha256_Ch(e, f, g)), sha256_K[j]), W[j]);\n            T2 = safe_add(sha256_Sigma0256(a), sha256_Maj(a, b, c));\n            h = g;\n            g = f;\n            f = e;\n            e = safe_add(d, T1);\n            d = c;\n            c = b;\n            b = a;\n            a = safe_add(T1, T2);\n        }\n        HASH[0] = safe_add(a, HASH[0]);\n        HASH[1] = safe_add(b, HASH[1]);\n        HASH[2] = safe_add(c, HASH[2]);\n        HASH[3] = safe_add(d, HASH[3]);\n        HASH[4] = safe_add(e, HASH[4]);\n        HASH[5] = safe_add(f, HASH[5]);\n        HASH[6] = safe_add(g, HASH[6]);\n        HASH[7] = safe_add(h, HASH[7]);\n    }\n    return HASH;\n}\nfunction safe_add(x, y) {\n    var lsw = (x & 0xffff) + (y & 0xffff);\n    var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n    return (msw << 16) | (lsw & 0xffff);\n}\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/sha256.js?\n}");

/***/ }),

/***/ "./lib/lib/jsbn/util.js":
/*!******************************!*\
  !*** ./lib/lib/jsbn/util.js ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cbit: function() { return /* binding */ cbit; },\n/* harmony export */   int2char: function() { return /* binding */ int2char; },\n/* harmony export */   lbit: function() { return /* binding */ lbit; },\n/* harmony export */   op_and: function() { return /* binding */ op_and; },\n/* harmony export */   op_andnot: function() { return /* binding */ op_andnot; },\n/* harmony export */   op_or: function() { return /* binding */ op_or; },\n/* harmony export */   op_xor: function() { return /* binding */ op_xor; }\n/* harmony export */ });\nvar BI_RM = \"0123456789abcdefghijklmnopqrstuvwxyz\";\nfunction int2char(n) {\n    return BI_RM.charAt(n);\n}\n//#region BIT_OPERATIONS\n// (public) this & a\nfunction op_and(x, y) {\n    return x & y;\n}\n// (public) this | a\nfunction op_or(x, y) {\n    return x | y;\n}\n// (public) this ^ a\nfunction op_xor(x, y) {\n    return x ^ y;\n}\n// (public) this & ~a\nfunction op_andnot(x, y) {\n    return x & ~y;\n}\n// return index of lowest 1-bit in x, x < 2^31\nfunction lbit(x) {\n    if (x == 0) {\n        return -1;\n    }\n    var r = 0;\n    if ((x & 0xffff) == 0) {\n        x >>= 16;\n        r += 16;\n    }\n    if ((x & 0xff) == 0) {\n        x >>= 8;\n        r += 8;\n    }\n    if ((x & 0xf) == 0) {\n        x >>= 4;\n        r += 4;\n    }\n    if ((x & 3) == 0) {\n        x >>= 2;\n        r += 2;\n    }\n    if ((x & 1) == 0) {\n        ++r;\n    }\n    return r;\n}\n// return number of 1 bits in x\nfunction cbit(x) {\n    var r = 0;\n    while (x != 0) {\n        x &= x - 1;\n        ++r;\n    }\n    return r;\n}\n//#endregion BIT_OPERATIONS\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsbn/util.js?\n}");

/***/ }),

/***/ "./lib/lib/jsrsasign/asn1-1.0.js":
/*!***************************************!*\
  !*** ./lib/lib/jsrsasign/asn1-1.0.js ***!
  \***************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KJUR: function() { return /* binding */ KJUR; },\n/* harmony export */   extendClass: function() { return /* binding */ extendClass; }\n/* harmony export */ });\n/* harmony import */ var _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsbn/jsbn */ \"./lib/lib/jsbn/jsbn.js\");\n/* asn1-1.0.13.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license\n */\n/*\n * asn1.js - ASN.1 DER encoder classes\n *\n * Copyright (c) 2013-2017 Kenji Urushima (<EMAIL>)\n *\n * This software is licensed under the terms of the MIT License.\n * https://kjur.github.io/jsrsasign/license\n *\n * The above copyright and license notice shall be\n * included in all copies or substantial portions of the Software.\n */\n\nfunction extendClass(subc, superc, overrides) {\n    if (!superc || !subc) {\n        throw new Error(\"extend failed, please check that \" +\n            \"all dependencies are included.\");\n    }\n    var F = function () { };\n    F.prototype = superc.prototype;\n    subc.prototype = new F();\n    subc.prototype.constructor = subc;\n    subc.superclass = superc.prototype;\n    if (superc.prototype.constructor == Object.prototype.constructor) {\n        superc.prototype.constructor = superc;\n    }\n    if (overrides) {\n        var i;\n        for (i in overrides) {\n            subc.prototype[i] = overrides[i];\n        }\n        /*\n         * IE will not enumerate native functions in a derived object even if the\n         * function was overridden.  This is a workaround for specific functions\n         * we care about on the Object prototype.\n         * @property _IEEnumFix\n         * @param {Function} r  the object to receive the augmentation\n         * @param {Function} s  the object that supplies the properties to augment\n         * @static\n         * @private\n         */\n        var _IEEnumFix = function () { }, ADD = [\"toString\", \"valueOf\"];\n        try {\n            if (/MSIE/.test(navigator.userAgent)) {\n                _IEEnumFix = function (r, s) {\n                    for (i = 0; i < ADD.length; i = i + 1) {\n                        var fname = ADD[i], f = s[fname];\n                        if (typeof f === 'function' && f != Object.prototype[fname]) {\n                            r[fname] = f;\n                        }\n                    }\n                };\n            }\n        }\n        catch (ex) { }\n        ;\n        _IEEnumFix(subc.prototype, overrides);\n    }\n}\n/**\n * @fileOverview\n * @name asn1-1.0.js\n * <AUTHOR> * @version asn1 1.0.13 (2017-Jun-02)\n * @since jsrsasign 2.1\n * @license <a href=\"https://kjur.github.io/jsrsasign/license/\">MIT License</a>\n */\n/**\n * kjur's class library name space\n * <p>\n * This name space provides following name spaces:\n * <ul>\n * <li>{@link KJUR.asn1} - ASN.1 primitive hexadecimal encoder</li>\n * <li>{@link KJUR.asn1.x509} - ASN.1 structure for X.509 certificate and CRL</li>\n * <li>{@link KJUR.crypto} - Java Cryptographic Extension(JCE) style MessageDigest/Signature\n * class and utilities</li>\n * </ul>\n * </p>\n * NOTE: Please ignore method summary and document of this namespace. This caused by a bug of jsdoc2.\n * @name KJUR\n * @namespace kjur's class library name space\n */\nvar KJUR = {};\n/**\n * kjur's ASN.1 class library name space\n * <p>\n * This is ITU-T X.690 ASN.1 DER encoder class library and\n * class structure and methods is very similar to\n * org.bouncycastle.asn1 package of\n * well known BouncyCaslte Cryptography Library.\n * <h4>PROVIDING ASN.1 PRIMITIVES</h4>\n * Here are ASN.1 DER primitive classes.\n * <ul>\n * <li>0x01 {@link KJUR.asn1.DERBoolean}</li>\n * <li>0x02 {@link KJUR.asn1.DERInteger}</li>\n * <li>0x03 {@link KJUR.asn1.DERBitString}</li>\n * <li>0x04 {@link KJUR.asn1.DEROctetString}</li>\n * <li>0x05 {@link KJUR.asn1.DERNull}</li>\n * <li>0x06 {@link KJUR.asn1.DERObjectIdentifier}</li>\n * <li>0x0a {@link KJUR.asn1.DEREnumerated}</li>\n * <li>0x0c {@link KJUR.asn1.DERUTF8String}</li>\n * <li>0x12 {@link KJUR.asn1.DERNumericString}</li>\n * <li>0x13 {@link KJUR.asn1.DERPrintableString}</li>\n * <li>0x14 {@link KJUR.asn1.DERTeletexString}</li>\n * <li>0x16 {@link KJUR.asn1.DERIA5String}</li>\n * <li>0x17 {@link KJUR.asn1.DERUTCTime}</li>\n * <li>0x18 {@link KJUR.asn1.DERGeneralizedTime}</li>\n * <li>0x30 {@link KJUR.asn1.DERSequence}</li>\n * <li>0x31 {@link KJUR.asn1.DERSet}</li>\n * </ul>\n * <h4>OTHER ASN.1 CLASSES</h4>\n * <ul>\n * <li>{@link KJUR.asn1.ASN1Object}</li>\n * <li>{@link KJUR.asn1.DERAbstractString}</li>\n * <li>{@link KJUR.asn1.DERAbstractTime}</li>\n * <li>{@link KJUR.asn1.DERAbstractStructured}</li>\n * <li>{@link KJUR.asn1.DERTaggedObject}</li>\n * </ul>\n * <h4>SUB NAME SPACES</h4>\n * <ul>\n * <li>{@link KJUR.asn1.cades} - CAdES long term signature format</li>\n * <li>{@link KJUR.asn1.cms} - Cryptographic Message Syntax</li>\n * <li>{@link KJUR.asn1.csr} - Certificate Signing Request (CSR/PKCS#10)</li>\n * <li>{@link KJUR.asn1.tsp} - RFC 3161 Timestamping Protocol Format</li>\n * <li>{@link KJUR.asn1.x509} - RFC 5280 X.509 certificate and CRL</li>\n * </ul>\n * </p>\n * NOTE: Please ignore method summary and document of this namespace.\n * This caused by a bug of jsdoc2.\n * @name KJUR.asn1\n * @namespace\n */\nif (typeof KJUR.asn1 == \"undefined\" || !KJUR.asn1)\n    KJUR.asn1 = {};\n/**\n * ASN1 utilities class\n * @name KJUR.asn1.ASN1Util\n * @class ASN1 utilities class\n * @since asn1 1.0.2\n */\nKJUR.asn1.ASN1Util = new function () {\n    this.integerToByteHex = function (i) {\n        var h = i.toString(16);\n        if ((h.length % 2) == 1)\n            h = '0' + h;\n        return h;\n    };\n    this.bigIntToMinTwosComplementsHex = function (bigIntegerValue) {\n        var h = bigIntegerValue.toString(16);\n        if (h.substring(0, 1) != '-') {\n            if (h.length % 2 == 1) {\n                h = '0' + h;\n            }\n            else {\n                if (!h.match(/^[0-7]/)) {\n                    h = '00' + h;\n                }\n            }\n        }\n        else {\n            var hPos = h.substring(1);\n            var xorLen = hPos.length;\n            if (xorLen % 2 == 1) {\n                xorLen += 1;\n            }\n            else {\n                if (!h.match(/^[0-7]/)) {\n                    xorLen += 2;\n                }\n            }\n            var hMask = '';\n            for (var i = 0; i < xorLen; i++) {\n                hMask += 'f';\n            }\n            var biMask = new _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(hMask, 16);\n            var biNeg = biMask.xor(bigIntegerValue).add(_jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger.ONE);\n            h = biNeg.toString(16).replace(/^-/, '');\n        }\n        return h;\n    };\n    /**\n     * get PEM string from hexadecimal data and header string\n     * @name getPEMStringFromHex\n     * @memberOf KJUR.asn1.ASN1Util\n     * @function\n     * @param {String} dataHex hexadecimal string of PEM body\n     * @param {String} pemHeader PEM header string (ex. 'RSA PRIVATE KEY')\n     * @return {String} PEM formatted string of input data\n     * @description\n     * This method converts a hexadecimal string to a PEM string with\n     * a specified header. Its line break will be CRLF(\"\\r\\n\").\n     * @example\n     * var pem  = KJUR.asn1.ASN1Util.getPEMStringFromHex('616161', 'RSA PRIVATE KEY');\n     * // value of pem will be:\n     * -----BEGIN PRIVATE KEY-----\n     * YWFh\n     * -----END PRIVATE KEY-----\n     */\n    this.getPEMStringFromHex = function (dataHex, pemHeader) {\n        return hextopem(dataHex, pemHeader);\n    };\n    /**\n     * generate ASN1Object specifed by JSON parameters\n     * @name newObject\n     * @memberOf KJUR.asn1.ASN1Util\n     * @function\n     * @param {Array} param JSON parameter to generate ASN1Object\n     * @return {KJUR.asn1.ASN1Object} generated object\n     * @since asn1 1.0.3\n     * @description\n     * generate any ASN1Object specified by JSON param\n     * including ASN.1 primitive or structured.\n     * Generally 'param' can be described as follows:\n     * <blockquote>\n     * {TYPE-OF-ASNOBJ: ASN1OBJ-PARAMETER}\n     * </blockquote>\n     * 'TYPE-OF-ASN1OBJ' can be one of following symbols:\n     * <ul>\n     * <li>'bool' - DERBoolean</li>\n     * <li>'int' - DERInteger</li>\n     * <li>'bitstr' - DERBitString</li>\n     * <li>'octstr' - DEROctetString</li>\n     * <li>'null' - DERNull</li>\n     * <li>'oid' - DERObjectIdentifier</li>\n     * <li>'enum' - DEREnumerated</li>\n     * <li>'utf8str' - DERUTF8String</li>\n     * <li>'numstr' - DERNumericString</li>\n     * <li>'prnstr' - DERPrintableString</li>\n     * <li>'telstr' - DERTeletexString</li>\n     * <li>'ia5str' - DERIA5String</li>\n     * <li>'utctime' - DERUTCTime</li>\n     * <li>'gentime' - DERGeneralizedTime</li>\n     * <li>'seq' - DERSequence</li>\n     * <li>'set' - DERSet</li>\n     * <li>'tag' - DERTaggedObject</li>\n     * </ul>\n     * @example\n     * newObject({'prnstr': 'aaa'});\n     * newObject({'seq': [{'int': 3}, {'prnstr': 'aaa'}]})\n     * // ASN.1 Tagged Object\n     * newObject({'tag': {'tag': 'a1',\n     *                    'explicit': true,\n     *                    'obj': {'seq': [{'int': 3}, {'prnstr': 'aaa'}]}}});\n     * // more simple representation of ASN.1 Tagged Object\n     * newObject({'tag': ['a1',\n     *                    true,\n     *                    {'seq': [\n     *                      {'int': 3},\n     *                      {'prnstr': 'aaa'}]}\n     *                   ]});\n     */\n    this.newObject = function (param) {\n        var _KJUR = KJUR, _KJUR_asn1 = _KJUR.asn1, _DERBoolean = _KJUR_asn1.DERBoolean, _DERInteger = _KJUR_asn1.DERInteger, _DERBitString = _KJUR_asn1.DERBitString, _DEROctetString = _KJUR_asn1.DEROctetString, _DERNull = _KJUR_asn1.DERNull, _DERObjectIdentifier = _KJUR_asn1.DERObjectIdentifier, _DEREnumerated = _KJUR_asn1.DEREnumerated, _DERUTF8String = _KJUR_asn1.DERUTF8String, _DERNumericString = _KJUR_asn1.DERNumericString, _DERPrintableString = _KJUR_asn1.DERPrintableString, _DERTeletexString = _KJUR_asn1.DERTeletexString, _DERIA5String = _KJUR_asn1.DERIA5String, _DERUTCTime = _KJUR_asn1.DERUTCTime, _DERGeneralizedTime = _KJUR_asn1.DERGeneralizedTime, _DERSequence = _KJUR_asn1.DERSequence, _DERSet = _KJUR_asn1.DERSet, _DERTaggedObject = _KJUR_asn1.DERTaggedObject, _newObject = _KJUR_asn1.ASN1Util.newObject;\n        var keys = Object.keys(param);\n        if (keys.length != 1)\n            throw \"key of param shall be only one.\";\n        var key = keys[0];\n        if (\":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:\".indexOf(\":\" + key + \":\") == -1)\n            throw \"undefined key: \" + key;\n        if (key == \"bool\")\n            return new _DERBoolean(param[key]);\n        if (key == \"int\")\n            return new _DERInteger(param[key]);\n        if (key == \"bitstr\")\n            return new _DERBitString(param[key]);\n        if (key == \"octstr\")\n            return new _DEROctetString(param[key]);\n        if (key == \"null\")\n            return new _DERNull(param[key]);\n        if (key == \"oid\")\n            return new _DERObjectIdentifier(param[key]);\n        if (key == \"enum\")\n            return new _DEREnumerated(param[key]);\n        if (key == \"utf8str\")\n            return new _DERUTF8String(param[key]);\n        if (key == \"numstr\")\n            return new _DERNumericString(param[key]);\n        if (key == \"prnstr\")\n            return new _DERPrintableString(param[key]);\n        if (key == \"telstr\")\n            return new _DERTeletexString(param[key]);\n        if (key == \"ia5str\")\n            return new _DERIA5String(param[key]);\n        if (key == \"utctime\")\n            return new _DERUTCTime(param[key]);\n        if (key == \"gentime\")\n            return new _DERGeneralizedTime(param[key]);\n        if (key == \"seq\") {\n            var paramList = param[key];\n            var a = [];\n            for (var i = 0; i < paramList.length; i++) {\n                var asn1Obj = _newObject(paramList[i]);\n                a.push(asn1Obj);\n            }\n            return new _DERSequence({ 'array': a });\n        }\n        if (key == \"set\") {\n            var paramList = param[key];\n            var a = [];\n            for (var i = 0; i < paramList.length; i++) {\n                var asn1Obj = _newObject(paramList[i]);\n                a.push(asn1Obj);\n            }\n            return new _DERSet({ 'array': a });\n        }\n        if (key == \"tag\") {\n            var tagParam = param[key];\n            if (Object.prototype.toString.call(tagParam) === '[object Array]' &&\n                tagParam.length == 3) {\n                var obj = _newObject(tagParam[2]);\n                return new _DERTaggedObject({\n                    tag: tagParam[0],\n                    explicit: tagParam[1],\n                    obj: obj\n                });\n            }\n            else {\n                var newParam = {};\n                if (tagParam.explicit !== undefined)\n                    newParam.explicit = tagParam.explicit;\n                if (tagParam.tag !== undefined)\n                    newParam.tag = tagParam.tag;\n                if (tagParam.obj === undefined)\n                    throw \"obj shall be specified for 'tag'.\";\n                newParam.obj = _newObject(tagParam.obj);\n                return new _DERTaggedObject(newParam);\n            }\n        }\n    };\n    /**\n     * get encoded hexadecimal string of ASN1Object specifed by JSON parameters\n     * @name jsonToASN1HEX\n     * @memberOf KJUR.asn1.ASN1Util\n     * @function\n     * @param {Array} param JSON parameter to generate ASN1Object\n     * @return hexadecimal string of ASN1Object\n     * @since asn1 1.0.4\n     * @description\n     * As for ASN.1 object representation of JSON object,\n     * please see {@link newObject}.\n     * @example\n     * jsonToASN1HEX({'prnstr': 'aaa'});\n     */\n    this.jsonToASN1HEX = function (param) {\n        var asn1Obj = this.newObject(param);\n        return asn1Obj.getEncodedHex();\n    };\n};\n/**\n * get dot noted oid number string from hexadecimal value of OID\n * @name oidHexToInt\n * @memberOf KJUR.asn1.ASN1Util\n * @function\n * @param {String} hex hexadecimal value of object identifier\n * @return {String} dot noted string of object identifier\n * @since jsrsasign 4.8.3 asn1 1.0.7\n * @description\n * This static method converts from hexadecimal string representation of\n * ASN.1 value of object identifier to oid number string.\n * @example\n * KJUR.asn1.ASN1Util.oidHexToInt('550406') &rarr; \"*******\"\n */\nKJUR.asn1.ASN1Util.oidHexToInt = function (hex) {\n    var s = \"\";\n    var i01 = parseInt(hex.substring(0, 2), 16);\n    var i0 = Math.floor(i01 / 40);\n    var i1 = i01 % 40;\n    var s = i0 + \".\" + i1;\n    var binbuf = \"\";\n    for (var i = 2; i < hex.length; i += 2) {\n        var value = parseInt(hex.substring(i, i + 2), 16);\n        var bin = (\"00000000\" + value.toString(2)).slice(-8);\n        binbuf = binbuf + bin.substring(1, 8);\n        if (bin.substring(0, 1) == \"0\") {\n            var bi = new _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(binbuf, 2);\n            s = s + \".\" + bi.toString(10);\n            binbuf = \"\";\n        }\n    }\n    ;\n    return s;\n};\n/**\n * get hexadecimal value of object identifier from dot noted oid value\n * @name oidIntToHex\n * @memberOf KJUR.asn1.ASN1Util\n * @function\n * @param {String} oidString dot noted string of object identifier\n * @return {String} hexadecimal value of object identifier\n * @since jsrsasign 4.8.3 asn1 1.0.7\n * @description\n * This static method converts from object identifier value string.\n * to hexadecimal string representation of it.\n * @example\n * KJUR.asn1.ASN1Util.oidIntToHex(\"*******\") &rarr; \"550406\"\n */\nKJUR.asn1.ASN1Util.oidIntToHex = function (oidString) {\n    var itox = function (i) {\n        var h = i.toString(16);\n        if (h.length == 1)\n            h = '0' + h;\n        return h;\n    };\n    var roidtox = function (roid) {\n        var h = '';\n        var bi = new _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(roid, 10);\n        var b = bi.toString(2);\n        var padLen = 7 - b.length % 7;\n        if (padLen == 7)\n            padLen = 0;\n        var bPad = '';\n        for (var i = 0; i < padLen; i++)\n            bPad += '0';\n        b = bPad + b;\n        for (var i = 0; i < b.length - 1; i += 7) {\n            var b8 = b.substring(i, i + 7);\n            if (i != b.length - 7)\n                b8 = '1' + b8;\n            h += itox(parseInt(b8, 2));\n        }\n        return h;\n    };\n    if (!oidString.match(/^[0-9.]+$/)) {\n        throw \"malformed oid string: \" + oidString;\n    }\n    var h = '';\n    var a = oidString.split('.');\n    var i0 = parseInt(a[0]) * 40 + parseInt(a[1]);\n    h += itox(i0);\n    a.splice(0, 2);\n    for (var i = 0; i < a.length; i++) {\n        h += roidtox(a[i]);\n    }\n    return h;\n};\n// ********************************************************************\n//  Abstract ASN.1 Classes\n// ********************************************************************\n// ********************************************************************\n/**\n * base class for ASN.1 DER encoder object\n * @name KJUR.asn1.ASN1Object\n * @class base class for ASN.1 DER encoder object\n * @property {Boolean} isModified flag whether internal data was changed\n * @property {String} hTLV hexadecimal string of ASN.1 TLV\n * @property {String} hT hexadecimal string of ASN.1 TLV tag(T)\n * @property {String} hL hexadecimal string of ASN.1 TLV length(L)\n * @property {String} hV hexadecimal string of ASN.1 TLV value(V)\n * @description\n */\nKJUR.asn1.ASN1Object = function () {\n    var isModified = true;\n    var hTLV = null;\n    var hT = '00';\n    var hL = '00';\n    var hV = '';\n    /**\n     * get hexadecimal ASN.1 TLV length(L) bytes from TLV value(V)\n     * @name getLengthHexFromValue\n     * @memberOf KJUR.asn1.ASN1Object#\n     * @function\n     * @return {String} hexadecimal string of ASN.1 TLV length(L)\n     */\n    this.getLengthHexFromValue = function () {\n        if (typeof this.hV == \"undefined\" || this.hV == null) {\n            throw \"this.hV is null or undefined.\";\n        }\n        if (this.hV.length % 2 == 1) {\n            throw \"value hex must be even length: n=\" + hV.length + \",v=\" + this.hV;\n        }\n        var n = this.hV.length / 2;\n        var hN = n.toString(16);\n        if (hN.length % 2 == 1) {\n            hN = \"0\" + hN;\n        }\n        if (n < 128) {\n            return hN;\n        }\n        else {\n            var hNlen = hN.length / 2;\n            if (hNlen > 15) {\n                throw \"ASN.1 length too long to represent by 8x: n = \" + n.toString(16);\n            }\n            var head = 128 + hNlen;\n            return head.toString(16) + hN;\n        }\n    };\n    /**\n     * get hexadecimal string of ASN.1 TLV bytes\n     * @name getEncodedHex\n     * @memberOf KJUR.asn1.ASN1Object#\n     * @function\n     * @return {String} hexadecimal string of ASN.1 TLV\n     */\n    this.getEncodedHex = function () {\n        if (this.hTLV == null || this.isModified) {\n            this.hV = this.getFreshValueHex();\n            this.hL = this.getLengthHexFromValue();\n            this.hTLV = this.hT + this.hL + this.hV;\n            this.isModified = false;\n            //alert(\"first time: \" + this.hTLV);\n        }\n        return this.hTLV;\n    };\n    /**\n     * get hexadecimal string of ASN.1 TLV value(V) bytes\n     * @name getValueHex\n     * @memberOf KJUR.asn1.ASN1Object#\n     * @function\n     * @return {String} hexadecimal string of ASN.1 TLV value(V) bytes\n     */\n    this.getValueHex = function () {\n        this.getEncodedHex();\n        return this.hV;\n    };\n    this.getFreshValueHex = function () {\n        return '';\n    };\n};\n// == BEGIN DERAbstractString ================================================\n/**\n * base class for ASN.1 DER string classes\n * @name KJUR.asn1.DERAbstractString\n * @class base class for ASN.1 DER string classes\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @property {String} s internal string of value\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERAbstractString = function (params) {\n    KJUR.asn1.DERAbstractString.superclass.constructor.call(this);\n    var s = null;\n    var hV = null;\n    /**\n     * get string value of this string object\n     * @name getString\n     * @memberOf KJUR.asn1.DERAbstractString#\n     * @function\n     * @return {String} string value of this string object\n     */\n    this.getString = function () {\n        return this.s;\n    };\n    /**\n     * set value by a string\n     * @name setString\n     * @memberOf KJUR.asn1.DERAbstractString#\n     * @function\n     * @param {String} newS value by a string to set\n     */\n    this.setString = function (newS) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = newS;\n        this.hV = stohex(this.s);\n    };\n    /**\n     * set value by a hexadecimal string\n     * @name setStringHex\n     * @memberOf KJUR.asn1.DERAbstractString#\n     * @function\n     * @param {String} newHexString value by a hexadecimal string to set\n     */\n    this.setStringHex = function (newHexString) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = null;\n        this.hV = newHexString;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params == \"string\") {\n            this.setString(params);\n        }\n        else if (typeof params['str'] != \"undefined\") {\n            this.setString(params['str']);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setStringHex(params['hex']);\n        }\n    }\n};\nextendClass(KJUR.asn1.DERAbstractString, KJUR.asn1.ASN1Object);\n// == END   DERAbstractString ================================================\n// == BEGIN DERAbstractTime ==================================================\n/**\n * base class for ASN.1 DER Generalized/UTCTime class\n * @name KJUR.asn1.DERAbstractTime\n * @class base class for ASN.1 DER Generalized/UTCTime class\n * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERAbstractTime = function (params) {\n    KJUR.asn1.DERAbstractTime.superclass.constructor.call(this);\n    var s = null;\n    var date = null;\n    // --- PRIVATE METHODS --------------------\n    this.localDateToUTC = function (d) {\n        utc = d.getTime() + (d.getTimezoneOffset() * 60000);\n        var utcDate = new Date(utc);\n        return utcDate;\n    };\n    /*\n     * format date string by Data object\n     * @name formatDate\n     * @memberOf KJUR.asn1.AbstractTime;\n     * @param {Date} dateObject\n     * @param {string} type 'utc' or 'gen'\n     * @param {boolean} withMillis flag for with millisections or not\n     * @description\n     * 'withMillis' flag is supported from asn1 1.0.6.\n     */\n    this.formatDate = function (dateObject, type, withMillis) {\n        var pad = this.zeroPadding;\n        var d = this.localDateToUTC(dateObject);\n        var year = String(d.getFullYear());\n        if (type == 'utc')\n            year = year.substring(2, 4);\n        var month = pad(String(d.getMonth() + 1), 2);\n        var day = pad(String(d.getDate()), 2);\n        var hour = pad(String(d.getHours()), 2);\n        var min = pad(String(d.getMinutes()), 2);\n        var sec = pad(String(d.getSeconds()), 2);\n        var s = year + month + day + hour + min + sec;\n        if (withMillis === true) {\n            var millis = d.getMilliseconds();\n            if (millis != 0) {\n                var sMillis = pad(String(millis), 3);\n                sMillis = sMillis.replace(/[0]+$/, \"\");\n                s = s + \".\" + sMillis;\n            }\n        }\n        return s + \"Z\";\n    };\n    this.zeroPadding = function (s, len) {\n        if (s.length >= len)\n            return s;\n        return new Array(len - s.length + 1).join('0') + s;\n    };\n    // --- PUBLIC METHODS --------------------\n    /**\n     * get string value of this string object\n     * @name getString\n     * @memberOf KJUR.asn1.DERAbstractTime#\n     * @function\n     * @return {String} string value of this time object\n     */\n    this.getString = function () {\n        return this.s;\n    };\n    /**\n     * set value by a string\n     * @name setString\n     * @memberOf KJUR.asn1.DERAbstractTime#\n     * @function\n     * @param {String} newS value by a string to set such like \"130430235959Z\"\n     */\n    this.setString = function (newS) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = newS;\n        this.hV = stohex(newS);\n    };\n    /**\n     * set value by a Date object\n     * @name setByDateValue\n     * @memberOf KJUR.asn1.DERAbstractTime#\n     * @function\n     * @param {Integer} year year of date (ex. 2013)\n     * @param {Integer} month month of date between 1 and 12 (ex. 12)\n     * @param {Integer} day day of month\n     * @param {Integer} hour hours of date\n     * @param {Integer} min minutes of date\n     * @param {Integer} sec seconds of date\n     */\n    this.setByDateValue = function (year, month, day, hour, min, sec) {\n        var dateObject = new Date(Date.UTC(year, month - 1, day, hour, min, sec, 0));\n        this.setByDate(dateObject);\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n};\nextendClass(KJUR.asn1.DERAbstractTime, KJUR.asn1.ASN1Object);\n// == END   DERAbstractTime ==================================================\n// == BEGIN DERAbstractStructured ============================================\n/**\n * base class for ASN.1 DER structured class\n * @name KJUR.asn1.DERAbstractStructured\n * @class base class for ASN.1 DER structured class\n * @property {Array} asn1Array internal array of ASN1Object\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERAbstractStructured = function (params) {\n    KJUR.asn1.DERAbstractString.superclass.constructor.call(this);\n    var asn1Array = null;\n    /**\n     * set value by array of ASN1Object\n     * @name setByASN1ObjectArray\n     * @memberOf KJUR.asn1.DERAbstractStructured#\n     * @function\n     * @param {array} asn1ObjectArray array of ASN1Object to set\n     */\n    this.setByASN1ObjectArray = function (asn1ObjectArray) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.asn1Array = asn1ObjectArray;\n    };\n    /**\n     * append an ASN1Object to internal array\n     * @name appendASN1Object\n     * @memberOf KJUR.asn1.DERAbstractStructured#\n     * @function\n     * @param {ASN1Object} asn1Object to add\n     */\n    this.appendASN1Object = function (asn1Object) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.asn1Array.push(asn1Object);\n    };\n    this.asn1Array = new Array();\n    if (typeof params != \"undefined\") {\n        if (typeof params['array'] != \"undefined\") {\n            this.asn1Array = params['array'];\n        }\n    }\n};\nextendClass(KJUR.asn1.DERAbstractStructured, KJUR.asn1.ASN1Object);\n// ********************************************************************\n//  ASN.1 Object Classes\n// ********************************************************************\n// ********************************************************************\n/**\n * class for ASN.1 DER Boolean\n * @name KJUR.asn1.DERBoolean\n * @class class for ASN.1 DER Boolean\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERBoolean = function () {\n    KJUR.asn1.DERBoolean.superclass.constructor.call(this);\n    this.hT = \"01\";\n    this.hTLV = \"0101ff\";\n};\nextendClass(KJUR.asn1.DERBoolean, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER Integer\n * @name KJUR.asn1.DERInteger\n * @class class for ASN.1 DER Integer\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>int - specify initial ASN.1 value(V) by integer value</li>\n * <li>bigint - specify initial ASN.1 value(V) by BigInteger object</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERInteger = function (params) {\n    KJUR.asn1.DERInteger.superclass.constructor.call(this);\n    this.hT = \"02\";\n    /**\n     * set value by Tom Wu's BigInteger object\n     * @name setByBigInteger\n     * @memberOf KJUR.asn1.DERInteger#\n     * @function\n     * @param {BigInteger} bigIntegerValue to set\n     */\n    this.setByBigInteger = function (bigIntegerValue) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);\n    };\n    /**\n     * set value by integer value\n     * @name setByInteger\n     * @memberOf KJUR.asn1.DERInteger\n     * @function\n     * @param {Integer} integer value to set\n     */\n    this.setByInteger = function (intValue) {\n        var bi = new _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(String(intValue), 10);\n        this.setByBigInteger(bi);\n    };\n    /**\n     * set value by integer value\n     * @name setValueHex\n     * @memberOf KJUR.asn1.DERInteger#\n     * @function\n     * @param {String} hexadecimal string of integer value\n     * @description\n     * <br/>\n     * NOTE: Value shall be represented by minimum octet length of\n     * two's complement representation.\n     * @example\n     * new KJUR.asn1.DERInteger(123);\n     * new KJUR.asn1.DERInteger({'int': 123});\n     * new KJUR.asn1.DERInteger({'hex': '1fad'});\n     */\n    this.setValueHex = function (newHexString) {\n        this.hV = newHexString;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params['bigint'] != \"undefined\") {\n            this.setByBigInteger(params['bigint']);\n        }\n        else if (typeof params['int'] != \"undefined\") {\n            this.setByInteger(params['int']);\n        }\n        else if (typeof params == \"number\") {\n            this.setByInteger(params);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setValueHex(params['hex']);\n        }\n    }\n};\nextendClass(KJUR.asn1.DERInteger, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER encoded BitString primitive\n * @name KJUR.asn1.DERBitString\n * @class class for ASN.1 DER encoded BitString primitive\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>bin - specify binary string (ex. '10111')</li>\n * <li>array - specify array of boolean (ex. [true,false,true,true])</li>\n * <li>hex - specify hexadecimal string of ASN.1 value(V) including unused bits</li>\n * <li>obj - specify {@link KJUR.asn1.ASN1Util.newObject}\n * argument for \"BitString encapsulates\" structure.</li>\n * </ul>\n * NOTE1: 'params' can be omitted.<br/>\n * NOTE2: 'obj' parameter have been supported since\n * asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).<br/>\n * @example\n * // default constructor\n * o = new KJUR.asn1.DERBitString();\n * // initialize with binary string\n * o = new KJUR.asn1.DERBitString({bin: \"1011\"});\n * // initialize with boolean array\n * o = new KJUR.asn1.DERBitString({array: [true,false,true,true]});\n * // initialize with hexadecimal string (04 is unused bits)\n * o = new KJUR.asn1.DEROctetString({hex: \"04bac0\"});\n * // initialize with ASN1Util.newObject argument for encapsulated\n * o = new KJUR.asn1.DERBitString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});\n * // above generates a ASN.1 data like this:\n * // BIT STRING, encapsulates {\n * //   SEQUENCE {\n * //     INTEGER 3\n * //     PrintableString 'aaa'\n * //     }\n * //   }\n */\nKJUR.asn1.DERBitString = function (params) {\n    if (params !== undefined && typeof params.obj !== \"undefined\") {\n        var o = KJUR.asn1.ASN1Util.newObject(params.obj);\n        params.hex = \"00\" + o.getEncodedHex();\n    }\n    KJUR.asn1.DERBitString.superclass.constructor.call(this);\n    this.hT = \"03\";\n    /**\n     * set ASN.1 value(V) by a hexadecimal string including unused bits\n     * @name setHexValueIncludingUnusedBits\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {String} newHexStringIncludingUnusedBits\n     */\n    this.setHexValueIncludingUnusedBits = function (newHexStringIncludingUnusedBits) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = newHexStringIncludingUnusedBits;\n    };\n    /**\n     * set ASN.1 value(V) by unused bit and hexadecimal string of value\n     * @name setUnusedBitsAndHexValue\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {Integer} unusedBits\n     * @param {String} hValue\n     */\n    this.setUnusedBitsAndHexValue = function (unusedBits, hValue) {\n        if (unusedBits < 0 || 7 < unusedBits) {\n            throw \"unused bits shall be from 0 to 7: u = \" + unusedBits;\n        }\n        var hUnusedBits = \"0\" + unusedBits;\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = hUnusedBits + hValue;\n    };\n    /**\n     * set ASN.1 DER BitString by binary string<br/>\n     * @name setByBinaryString\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {String} binaryString binary value string (i.e. '10111')\n     * @description\n     * Its unused bits will be calculated automatically by length of\n     * 'binaryValue'. <br/>\n     * NOTE: Trailing zeros '0' will be ignored.\n     * @example\n     * o = new KJUR.asn1.DERBitString();\n     * o.setByBooleanArray(\"01011\");\n     */\n    this.setByBinaryString = function (binaryString) {\n        binaryString = binaryString.replace(/0+$/, '');\n        var unusedBits = 8 - binaryString.length % 8;\n        if (unusedBits == 8)\n            unusedBits = 0;\n        for (var i = 0; i <= unusedBits; i++) {\n            binaryString += '0';\n        }\n        var h = '';\n        for (var i = 0; i < binaryString.length - 1; i += 8) {\n            var b = binaryString.substring(i, i + 8);\n            var x = parseInt(b, 2).toString(16);\n            if (x.length == 1)\n                x = '0' + x;\n            h += x;\n        }\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = '0' + unusedBits + h;\n    };\n    /**\n     * set ASN.1 TLV value(V) by an array of boolean<br/>\n     * @name setByBooleanArray\n     * @memberOf KJUR.asn1.DERBitString#\n     * @function\n     * @param {array} booleanArray array of boolean (ex. [true, false, true])\n     * @description\n     * NOTE: Trailing falses will be ignored in the ASN.1 DER Object.\n     * @example\n     * o = new KJUR.asn1.DERBitString();\n     * o.setByBooleanArray([false, true, false, true, true]);\n     */\n    this.setByBooleanArray = function (booleanArray) {\n        var s = '';\n        for (var i = 0; i < booleanArray.length; i++) {\n            if (booleanArray[i] == true) {\n                s += '1';\n            }\n            else {\n                s += '0';\n            }\n        }\n        this.setByBinaryString(s);\n    };\n    /**\n     * generate an array of falses with specified length<br/>\n     * @name newFalseArray\n     * @memberOf KJUR.asn1.DERBitString\n     * @function\n     * @param {Integer} nLength length of array to generate\n     * @return {array} array of boolean falses\n     * @description\n     * This static method may be useful to initialize boolean array.\n     * @example\n     * o = new KJUR.asn1.DERBitString();\n     * o.newFalseArray(3) &rarr; [false, false, false]\n     */\n    this.newFalseArray = function (nLength) {\n        var a = new Array(nLength);\n        for (var i = 0; i < nLength; i++) {\n            a[i] = false;\n        }\n        return a;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params == \"string\" && params.toLowerCase().match(/^[0-9a-f]+$/)) {\n            this.setHexValueIncludingUnusedBits(params);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setHexValueIncludingUnusedBits(params['hex']);\n        }\n        else if (typeof params['bin'] != \"undefined\") {\n            this.setByBinaryString(params['bin']);\n        }\n        else if (typeof params['array'] != \"undefined\") {\n            this.setByBooleanArray(params['array']);\n        }\n    }\n};\nextendClass(KJUR.asn1.DERBitString, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER OctetString<br/>\n * @name KJUR.asn1.DEROctetString\n * @class class for ASN.1 DER OctetString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * This class provides ASN.1 OctetString simple type.<br/>\n * Supported \"params\" attributes are:\n * <ul>\n * <li>str - to set a string as a value</li>\n * <li>hex - to set a hexadecimal string as a value</li>\n * <li>obj - to set a encapsulated ASN.1 value by JSON object\n * which is defined in {@link KJUR.asn1.ASN1Util.newObject}</li>\n * </ul>\n * NOTE: A parameter 'obj' have been supported\n * for \"OCTET STRING, encapsulates\" structure.\n * since asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).\n * @see KJUR.asn1.DERAbstractString - superclass\n * @example\n * // default constructor\n * o = new KJUR.asn1.DEROctetString();\n * // initialize with string\n * o = new KJUR.asn1.DEROctetString({str: \"aaa\"});\n * // initialize with hexadecimal string\n * o = new KJUR.asn1.DEROctetString({hex: \"616161\"});\n * // initialize with ASN1Util.newObject argument\n * o = new KJUR.asn1.DEROctetString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});\n * // above generates a ASN.1 data like this:\n * // OCTET STRING, encapsulates {\n * //   SEQUENCE {\n * //     INTEGER 3\n * //     PrintableString 'aaa'\n * //     }\n * //   }\n */\nKJUR.asn1.DEROctetString = function (params) {\n    if (params !== undefined && typeof params.obj !== \"undefined\") {\n        var o = KJUR.asn1.ASN1Util.newObject(params.obj);\n        params.hex = o.getEncodedHex();\n    }\n    KJUR.asn1.DEROctetString.superclass.constructor.call(this, params);\n    this.hT = \"04\";\n};\nextendClass(KJUR.asn1.DEROctetString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER Null\n * @name KJUR.asn1.DERNull\n * @class class for ASN.1 DER Null\n * @extends KJUR.asn1.ASN1Object\n * @description\n * @see KJUR.asn1.ASN1Object - superclass\n */\nKJUR.asn1.DERNull = function () {\n    KJUR.asn1.DERNull.superclass.constructor.call(this);\n    this.hT = \"05\";\n    this.hTLV = \"0500\";\n};\nextendClass(KJUR.asn1.DERNull, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER ObjectIdentifier\n * @name KJUR.asn1.DERObjectIdentifier\n * @class class for ASN.1 DER ObjectIdentifier\n * @param {Array} params associative array of parameters (ex. {'oid': '*******'})\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>oid - specify initial ASN.1 value(V) by a oid string (ex. ********)</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERObjectIdentifier = function (params) {\n    var itox = function (i) {\n        var h = i.toString(16);\n        if (h.length == 1)\n            h = '0' + h;\n        return h;\n    };\n    var roidtox = function (roid) {\n        var h = '';\n        var bi = new _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(roid, 10);\n        var b = bi.toString(2);\n        var padLen = 7 - b.length % 7;\n        if (padLen == 7)\n            padLen = 0;\n        var bPad = '';\n        for (var i = 0; i < padLen; i++)\n            bPad += '0';\n        b = bPad + b;\n        for (var i = 0; i < b.length - 1; i += 7) {\n            var b8 = b.substring(i, i + 7);\n            if (i != b.length - 7)\n                b8 = '1' + b8;\n            h += itox(parseInt(b8, 2));\n        }\n        return h;\n    };\n    KJUR.asn1.DERObjectIdentifier.superclass.constructor.call(this);\n    this.hT = \"06\";\n    /**\n     * set value by a hexadecimal string\n     * @name setValueHex\n     * @memberOf KJUR.asn1.DERObjectIdentifier#\n     * @function\n     * @param {String} newHexString hexadecimal value of OID bytes\n     */\n    this.setValueHex = function (newHexString) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = null;\n        this.hV = newHexString;\n    };\n    /**\n     * set value by a OID string<br/>\n     * @name setValueOidString\n     * @memberOf KJUR.asn1.DERObjectIdentifier#\n     * @function\n     * @param {String} oidString OID string (ex. ********)\n     * @example\n     * o = new KJUR.asn1.DERObjectIdentifier();\n     * o.setValueOidString(\"********\");\n     */\n    this.setValueOidString = function (oidString) {\n        if (!oidString.match(/^[0-9.]+$/)) {\n            throw \"malformed oid string: \" + oidString;\n        }\n        var h = '';\n        var a = oidString.split('.');\n        var i0 = parseInt(a[0]) * 40 + parseInt(a[1]);\n        h += itox(i0);\n        a.splice(0, 2);\n        for (var i = 0; i < a.length; i++) {\n            h += roidtox(a[i]);\n        }\n        this.hTLV = null;\n        this.isModified = true;\n        this.s = null;\n        this.hV = h;\n    };\n    /**\n     * set value by a OID name\n     * @name setValueName\n     * @memberOf KJUR.asn1.DERObjectIdentifier#\n     * @function\n     * @param {String} oidName OID name (ex. 'serverAuth')\n     * @since 1.0.1\n     * @description\n     * OID name shall be defined in 'KJUR.asn1.x509.OID.name2oidList'.\n     * Otherwise raise error.\n     * @example\n     * o = new KJUR.asn1.DERObjectIdentifier();\n     * o.setValueName(\"serverAuth\");\n     */\n    this.setValueName = function (oidName) {\n        var oid = KJUR.asn1.x509.OID.name2oid(oidName);\n        if (oid !== '') {\n            this.setValueOidString(oid);\n        }\n        else {\n            throw \"DERObjectIdentifier oidName undefined: \" + oidName;\n        }\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (params !== undefined) {\n        if (typeof params === \"string\") {\n            if (params.match(/^[0-2].[0-9.]+$/)) {\n                this.setValueOidString(params);\n            }\n            else {\n                this.setValueName(params);\n            }\n        }\n        else if (params.oid !== undefined) {\n            this.setValueOidString(params.oid);\n        }\n        else if (params.hex !== undefined) {\n            this.setValueHex(params.hex);\n        }\n        else if (params.name !== undefined) {\n            this.setValueName(params.name);\n        }\n    }\n};\nextendClass(KJUR.asn1.DERObjectIdentifier, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER Enumerated\n * @name KJUR.asn1.DEREnumerated\n * @class class for ASN.1 DER Enumerated\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>int - specify initial ASN.1 value(V) by integer value</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n * @example\n * new KJUR.asn1.DEREnumerated(123);\n * new KJUR.asn1.DEREnumerated({int: 123});\n * new KJUR.asn1.DEREnumerated({hex: '1fad'});\n */\nKJUR.asn1.DEREnumerated = function (params) {\n    KJUR.asn1.DEREnumerated.superclass.constructor.call(this);\n    this.hT = \"0a\";\n    /**\n     * set value by Tom Wu's BigInteger object\n     * @name setByBigInteger\n     * @memberOf KJUR.asn1.DEREnumerated#\n     * @function\n     * @param {BigInteger} bigIntegerValue to set\n     */\n    this.setByBigInteger = function (bigIntegerValue) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.hV = KJUR.asn1.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);\n    };\n    /**\n     * set value by integer value\n     * @name setByInteger\n     * @memberOf KJUR.asn1.DEREnumerated#\n     * @function\n     * @param {Integer} integer value to set\n     */\n    this.setByInteger = function (intValue) {\n        var bi = new _jsbn_jsbn__WEBPACK_IMPORTED_MODULE_0__.BigInteger(String(intValue), 10);\n        this.setByBigInteger(bi);\n    };\n    /**\n     * set value by integer value\n     * @name setValueHex\n     * @memberOf KJUR.asn1.DEREnumerated#\n     * @function\n     * @param {String} hexadecimal string of integer value\n     * @description\n     * <br/>\n     * NOTE: Value shall be represented by minimum octet length of\n     * two's complement representation.\n     */\n    this.setValueHex = function (newHexString) {\n        this.hV = newHexString;\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params['int'] != \"undefined\") {\n            this.setByInteger(params['int']);\n        }\n        else if (typeof params == \"number\") {\n            this.setByInteger(params);\n        }\n        else if (typeof params['hex'] != \"undefined\") {\n            this.setValueHex(params['hex']);\n        }\n    }\n};\nextendClass(KJUR.asn1.DEREnumerated, KJUR.asn1.ASN1Object);\n// ********************************************************************\n/**\n * class for ASN.1 DER UTF8String\n * @name KJUR.asn1.DERUTF8String\n * @class class for ASN.1 DER UTF8String\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERUTF8String = function (params) {\n    KJUR.asn1.DERUTF8String.superclass.constructor.call(this, params);\n    this.hT = \"0c\";\n};\nextendClass(KJUR.asn1.DERUTF8String, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER NumericString\n * @name KJUR.asn1.DERNumericString\n * @class class for ASN.1 DER NumericString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERNumericString = function (params) {\n    KJUR.asn1.DERNumericString.superclass.constructor.call(this, params);\n    this.hT = \"12\";\n};\nextendClass(KJUR.asn1.DERNumericString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER PrintableString\n * @name KJUR.asn1.DERPrintableString\n * @class class for ASN.1 DER PrintableString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERPrintableString = function (params) {\n    KJUR.asn1.DERPrintableString.superclass.constructor.call(this, params);\n    this.hT = \"13\";\n};\nextendClass(KJUR.asn1.DERPrintableString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER TeletexString\n * @name KJUR.asn1.DERTeletexString\n * @class class for ASN.1 DER TeletexString\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERTeletexString = function (params) {\n    KJUR.asn1.DERTeletexString.superclass.constructor.call(this, params);\n    this.hT = \"14\";\n};\nextendClass(KJUR.asn1.DERTeletexString, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER IA5String\n * @name KJUR.asn1.DERIA5String\n * @class class for ASN.1 DER IA5String\n * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})\n * @extends KJUR.asn1.DERAbstractString\n * @description\n * @see KJUR.asn1.DERAbstractString - superclass\n */\nKJUR.asn1.DERIA5String = function (params) {\n    KJUR.asn1.DERIA5String.superclass.constructor.call(this, params);\n    this.hT = \"16\";\n};\nextendClass(KJUR.asn1.DERIA5String, KJUR.asn1.DERAbstractString);\n// ********************************************************************\n/**\n * class for ASN.1 DER UTCTime\n * @name KJUR.asn1.DERUTCTime\n * @class class for ASN.1 DER UTCTime\n * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})\n * @extends KJUR.asn1.DERAbstractTime\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string (ex.'130430235959Z')</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * <li>date - specify Date object.</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n * <h4>EXAMPLES</h4>\n * @example\n * d1 = new KJUR.asn1.DERUTCTime();\n * d1.setString('130430125959Z');\n *\n * d2 = new KJUR.asn1.DERUTCTime({'str': '130430125959Z'});\n * d3 = new KJUR.asn1.DERUTCTime({'date': new Date(Date.UTC(2015, 0, 31, 0, 0, 0, 0))});\n * d4 = new KJUR.asn1.DERUTCTime('130430125959Z');\n */\nKJUR.asn1.DERUTCTime = function (params) {\n    KJUR.asn1.DERUTCTime.superclass.constructor.call(this, params);\n    this.hT = \"17\";\n    /**\n     * set value by a Date object<br/>\n     * @name setByDate\n     * @memberOf KJUR.asn1.DERUTCTime#\n     * @function\n     * @param {Date} dateObject Date object to set ASN.1 value(V)\n     * @example\n     * o = new KJUR.asn1.DERUTCTime();\n     * o.setByDate(new Date(\"2016/12/31\"));\n     */\n    this.setByDate = function (dateObject) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.date = dateObject;\n        this.s = this.formatDate(this.date, 'utc');\n        this.hV = stohex(this.s);\n    };\n    this.getFreshValueHex = function () {\n        if (typeof this.date == \"undefined\" && typeof this.s == \"undefined\") {\n            this.date = new Date();\n            this.s = this.formatDate(this.date, 'utc');\n            this.hV = stohex(this.s);\n        }\n        return this.hV;\n    };\n    if (params !== undefined) {\n        if (params.str !== undefined) {\n            this.setString(params.str);\n        }\n        else if (typeof params == \"string\" && params.match(/^[0-9]{12}Z$/)) {\n            this.setString(params);\n        }\n        else if (params.hex !== undefined) {\n            this.setStringHex(params.hex);\n        }\n        else if (params.date !== undefined) {\n            this.setByDate(params.date);\n        }\n    }\n};\nextendClass(KJUR.asn1.DERUTCTime, KJUR.asn1.DERAbstractTime);\n// ********************************************************************\n/**\n * class for ASN.1 DER GeneralizedTime\n * @name KJUR.asn1.DERGeneralizedTime\n * @class class for ASN.1 DER GeneralizedTime\n * @param {Array} params associative array of parameters (ex. {'str': '20130430235959Z'})\n * @property {Boolean} withMillis flag to show milliseconds or not\n * @extends KJUR.asn1.DERAbstractTime\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>str - specify initial ASN.1 value(V) by a string (ex.'20130430235959Z')</li>\n * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>\n * <li>date - specify Date object.</li>\n * <li>millis - specify flag to show milliseconds (from 1.0.6)</li>\n * </ul>\n * NOTE1: 'params' can be omitted.\n * NOTE2: 'withMillis' property is supported from asn1 1.0.6.\n */\nKJUR.asn1.DERGeneralizedTime = function (params) {\n    KJUR.asn1.DERGeneralizedTime.superclass.constructor.call(this, params);\n    this.hT = \"18\";\n    this.withMillis = false;\n    /**\n     * set value by a Date object\n     * @name setByDate\n     * @memberOf KJUR.asn1.DERGeneralizedTime#\n     * @function\n     * @param {Date} dateObject Date object to set ASN.1 value(V)\n     * @example\n     * When you specify UTC time, use 'Date.UTC' method like this:<br/>\n     * o1 = new DERUTCTime();\n     * o1.setByDate(date);\n     *\n     * date = new Date(Date.UTC(2015, 0, 31, 23, 59, 59, 0)); #2015JAN31 23:59:59\n     */\n    this.setByDate = function (dateObject) {\n        this.hTLV = null;\n        this.isModified = true;\n        this.date = dateObject;\n        this.s = this.formatDate(this.date, 'gen', this.withMillis);\n        this.hV = stohex(this.s);\n    };\n    this.getFreshValueHex = function () {\n        if (this.date === undefined && this.s === undefined) {\n            this.date = new Date();\n            this.s = this.formatDate(this.date, 'gen', this.withMillis);\n            this.hV = stohex(this.s);\n        }\n        return this.hV;\n    };\n    if (params !== undefined) {\n        if (params.str !== undefined) {\n            this.setString(params.str);\n        }\n        else if (typeof params == \"string\" && params.match(/^[0-9]{14}Z$/)) {\n            this.setString(params);\n        }\n        else if (params.hex !== undefined) {\n            this.setStringHex(params.hex);\n        }\n        else if (params.date !== undefined) {\n            this.setByDate(params.date);\n        }\n        if (params.millis === true) {\n            this.withMillis = true;\n        }\n    }\n};\nextendClass(KJUR.asn1.DERGeneralizedTime, KJUR.asn1.DERAbstractTime);\n// ********************************************************************\n/**\n * class for ASN.1 DER Sequence\n * @name KJUR.asn1.DERSequence\n * @class class for ASN.1 DER Sequence\n * @extends KJUR.asn1.DERAbstractStructured\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>array - specify array of ASN1Object to set elements of content</li>\n * </ul>\n * NOTE: 'params' can be omitted.\n */\nKJUR.asn1.DERSequence = function (params) {\n    KJUR.asn1.DERSequence.superclass.constructor.call(this, params);\n    this.hT = \"30\";\n    this.getFreshValueHex = function () {\n        var h = '';\n        for (var i = 0; i < this.asn1Array.length; i++) {\n            var asn1Obj = this.asn1Array[i];\n            h += asn1Obj.getEncodedHex();\n        }\n        this.hV = h;\n        return this.hV;\n    };\n};\nextendClass(KJUR.asn1.DERSequence, KJUR.asn1.DERAbstractStructured);\n// ********************************************************************\n/**\n * class for ASN.1 DER Set\n * @name KJUR.asn1.DERSet\n * @class class for ASN.1 DER Set\n * @extends KJUR.asn1.DERAbstractStructured\n * @description\n * <br/>\n * As for argument 'params' for constructor, you can specify one of\n * following properties:\n * <ul>\n * <li>array - specify array of ASN1Object to set elements of content</li>\n * <li>sortflag - flag for sort (default: true). ASN.1 BER is not sorted in 'SET OF'.</li>\n * </ul>\n * NOTE1: 'params' can be omitted.<br/>\n * NOTE2: sortflag is supported since 1.0.5.\n */\nKJUR.asn1.DERSet = function (params) {\n    KJUR.asn1.DERSet.superclass.constructor.call(this, params);\n    this.hT = \"31\";\n    this.sortFlag = true; // item shall be sorted only in ASN.1 DER\n    this.getFreshValueHex = function () {\n        var a = new Array();\n        for (var i = 0; i < this.asn1Array.length; i++) {\n            var asn1Obj = this.asn1Array[i];\n            a.push(asn1Obj.getEncodedHex());\n        }\n        if (this.sortFlag == true)\n            a.sort();\n        this.hV = a.join('');\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params.sortflag != \"undefined\" &&\n            params.sortflag == false)\n            this.sortFlag = false;\n    }\n};\nextendClass(KJUR.asn1.DERSet, KJUR.asn1.DERAbstractStructured);\n// ********************************************************************\n/**\n * class for ASN.1 DER TaggedObject\n * @name KJUR.asn1.DERTaggedObject\n * @class class for ASN.1 DER TaggedObject\n * @extends KJUR.asn1.ASN1Object\n * @description\n * <br/>\n * Parameter 'tagNoNex' is ASN.1 tag(T) value for this object.\n * For example, if you find '[1]' tag in a ASN.1 dump,\n * 'tagNoHex' will be 'a1'.\n * <br/>\n * As for optional argument 'params' for constructor, you can specify *ANY* of\n * following properties:\n * <ul>\n * <li>explicit - specify true if this is explicit tag otherwise false\n *     (default is 'true').</li>\n * <li>tag - specify tag (default is 'a0' which means [0])</li>\n * <li>obj - specify ASN1Object which is tagged</li>\n * </ul>\n * @example\n * d1 = new KJUR.asn1.DERUTF8String({'str':'a'});\n * d2 = new KJUR.asn1.DERTaggedObject({'obj': d1});\n * hex = d2.getEncodedHex();\n */\nKJUR.asn1.DERTaggedObject = function (params) {\n    KJUR.asn1.DERTaggedObject.superclass.constructor.call(this);\n    this.hT = \"a0\";\n    this.hV = '';\n    this.isExplicit = true;\n    this.asn1Object = null;\n    /**\n     * set value by an ASN1Object\n     * @name setString\n     * @memberOf KJUR.asn1.DERTaggedObject#\n     * @function\n     * @param {Boolean} isExplicitFlag flag for explicit/implicit tag\n     * @param {Integer} tagNoHex hexadecimal string of ASN.1 tag\n     * @param {ASN1Object} asn1Object ASN.1 to encapsulate\n     */\n    this.setASN1Object = function (isExplicitFlag, tagNoHex, asn1Object) {\n        this.hT = tagNoHex;\n        this.isExplicit = isExplicitFlag;\n        this.asn1Object = asn1Object;\n        if (this.isExplicit) {\n            this.hV = this.asn1Object.getEncodedHex();\n            this.hTLV = null;\n            this.isModified = true;\n        }\n        else {\n            this.hV = null;\n            this.hTLV = asn1Object.getEncodedHex();\n            this.hTLV = this.hTLV.replace(/^../, tagNoHex);\n            this.isModified = false;\n        }\n    };\n    this.getFreshValueHex = function () {\n        return this.hV;\n    };\n    if (typeof params != \"undefined\") {\n        if (typeof params['tag'] != \"undefined\") {\n            this.hT = params['tag'];\n        }\n        if (typeof params['explicit'] != \"undefined\") {\n            this.isExplicit = params['explicit'];\n        }\n        if (typeof params['obj'] != \"undefined\") {\n            this.asn1Object = params['obj'];\n            this.setASN1Object(this.isExplicit, this.hT, this.asn1Object);\n        }\n    }\n};\nextendClass(KJUR.asn1.DERTaggedObject, KJUR.asn1.ASN1Object);\n\n\n//# sourceURL=webpack://JSEncrypt/./lib/lib/jsrsasign/asn1-1.0.js?\n}");

/***/ }),

/***/ "./node_modules/process/browser.js":
/*!*****************************************!*\
  !*** ./node_modules/process/browser.js ***!
  \*****************************************/
/***/ (function(module) {

eval("{// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n//# sourceURL=webpack://JSEncrypt/./node_modules/process/browser.js?\n}");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./lib/index.js");
/******/ 	__webpack_exports__ = __webpack_exports__["default"];
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});